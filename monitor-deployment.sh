#!/bin/bash

# Script de monitoreo completo post-deployment
echo "📊 MONITOREO COMPLETO DE SERVICIOS COREDESK"
echo "============================================"

ssh coredesk-prod << 'EOF'
cd /opt/coredesk

echo "=== 🐳 Estado de todos los contenedores ==="
docker-compose ps

echo ""
echo "=== 📊 Uso de recursos ==="
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"

echo ""
echo "=== 🏥 Health Checks Detallados ==="

# Portal
echo "🌐 Portal:"
if docker-compose ps | grep -q "srv-portal.*Up"; then
    if curl -f http://localhost:3000/health > /dev/null 2>&1; then
        echo "   ✅ Funcionando correctamente"
    else
        echo "   ⚠️  Contenedor activo pero no responde"
    fi
else
    echo "   ❌ Contenedor no está ejecutándose"
fi

# API
echo "🔌 API:"
if docker-compose ps | grep -q "srv-api.*Up"; then
    if curl -f http://localhost:3010/v1/health > /dev/null 2>&1; then
        echo "   ✅ Funcionando correctamente"
    else
        echo "   ⚠️  Contenedor activo pero no responde"
    fi
else
    echo "   ❌ Contenedor no está ejecutándose"
fi

# Admin Panel
echo "👥 Admin Panel:"
if docker-compose ps | grep -q "admin-panel.*Up"; then
    if curl -f http://localhost:3011/health > /dev/null 2>&1; then
        echo "   ✅ Funcionando correctamente"
    else
        echo "   ⚠️  Contenedor activo pero no responde"
    fi
else
    echo "   ❌ Contenedor no está ejecutándose"
fi

# MongoDB
echo "🗄️  MongoDB:"
if docker-compose ps | grep -q "srv-mongo.*Up"; then
    echo "   ✅ Base de datos ejecutándose"
else
    echo "   ❌ Base de datos no está ejecutándose"
fi

# System Manager
echo "⚙️  System Manager:"
if docker-compose ps | grep -q "srv-system-manager.*Up"; then
    if curl -f http://localhost:3012/ > /dev/null 2>&1; then
        echo "   ✅ Funcionando correctamente"
    else
        echo "   ⚠️  Contenedor activo pero no responde"
    fi
else
    echo "   ❌ Contenedor no está ejecutándose"
fi

echo ""
echo "=== 📝 Logs recientes (últimas 10 líneas por servicio) ==="

echo "--- Portal ---"
docker-compose logs --tail=10 portal 2>/dev/null || echo "No hay logs disponibles"

echo "--- API ---"
docker-compose logs --tail=10 api 2>/dev/null || echo "No hay logs disponibles"

echo "--- Admin Panel ---"
docker-compose logs --tail=10 admin-panel 2>/dev/null || echo "No hay logs disponibles"

echo ""
echo "=== 🌐 URLs de Acceso ==="
echo "Portal:         https://coredeskpro.com"
echo "API:           https://api.coredeskpro.com"
echo "Admin Panel:   https://admin.coredeskpro.com"
echo "MongoDB Admin: http://**************:8081"
echo "System Manager: http://**************:3012"

EOF
