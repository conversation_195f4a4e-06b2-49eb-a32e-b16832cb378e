{"appId": "com.coredesk.framework", "productName": "CoreDesk Framework", "copyright": "Copyright © 2025 CoreDesk Team", "directories": {"output": "dist"}, "files": [{"from": "src/", "to": "src/", "filter": ["**/*"]}, {"from": "main.js", "to": "main.js"}, {"from": "package.json", "to": "package.json"}, {"from": "preload.js", "to": "preload.js"}], "extraResources": [{"from": "src/assets/icons/", "to": "icons/", "filter": ["**/*"]}], "publish": [{"provider": "generic", "url": "https://coredeskpro.com/download"}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "icon": "src/assets/icons/app.ico", "publisherName": "CoreDesk Team"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "src/assets/icons/app.icns", "category": "public.app-category.productivity"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}], "icon": "src/assets/icons/app.png", "category": "Office"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}, "buildDependenciesFromSource": false, "nodeGypRebuild": false}