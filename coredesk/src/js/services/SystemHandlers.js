/**
 * SystemHandlers.js
 * System information IPC handlers for main process
 */

const { ipcMain, app } = require('electron');
const os = require('os');

class SystemHandlers {
    constructor(logger) {
        this.logger = logger;
        this.setupHandlers();
    }

    setupHandlers() {
        // App info handlers
        ipcMain.handle('app:getVersion', () => {
            return app.getVersion();
        });

        ipcMain.handle('app:getPlatform', () => {
            return process.platform;
        });

        ipcMain.handle('app:getUserDataPath', () => {
            return app.getPath('userData');
        });

        ipcMain.handle('app:getAppPath', () => {
            return app.getAppPath();
        });

        // System info handlers
        ipcMain.handle('system:getInfo', () => {
            return this.getSystemInfo();
        });

        ipcMain.handle('system:getBasicInfo', () => {
            return this.getBasicSystemInfo();
        });

        ipcMain.handle('system:getMemoryInfo', () => {
            return this.getMemoryInfo();
        });

        ipcMain.handle('system:getUsername', () => {
            return this.getUsername();
        });
    }

    getSystemInfo() {
        try {
            return {
                platform: os.platform(),
                arch: os.arch(),
                cpus: os.cpus(),
                totalmem: os.totalmem(),
                freemem: os.freemem(),
                hostname: os.hostname(),
                release: os.release(),
                type: os.type(),
                uptime: os.uptime()
            };
        } catch (error) {
            this.logger?.error('SystemHandlers', 'Failed to get system info', error);
            return {};
        }
    }

    getBasicSystemInfo() {
        try {
            return {
                platform: os.platform(),
                arch: os.arch(),
                hostname: os.hostname(),
                type: os.type()
            };
        } catch (error) {
            this.logger?.error('SystemHandlers', 'Failed to get basic system info', error);
            return {};
        }
    }

    getMemoryInfo() {
        try {
            return {
                total: os.totalmem(),
                free: os.freemem(),
                used: os.totalmem() - os.freemem(),
                usagePercent: ((os.totalmem() - os.freemem()) / os.totalmem()) * 100
            };
        } catch (error) {
            this.logger?.error('SystemHandlers', 'Failed to get memory info', error);
            return {};
        }
    }

    /**
     * Get current username
     */
    getUsername() {
        try {
            return os.userInfo().username;
        } catch (error) {
            this.logger?.error('SystemHandlers', 'Failed to get username', error);
            return 'User';
        }
    }

    cleanup() {
        ipcMain.removeAllListeners('app:getVersion');
        ipcMain.removeAllListeners('app:getPlatform');
        ipcMain.removeAllListeners('app:getUserDataPath');
        ipcMain.removeAllListeners('app:getAppPath');
        ipcMain.removeAllListeners('system:getInfo');
        ipcMain.removeAllListeners('system:getBasicInfo');
        ipcMain.removeAllListeners('system:getMemoryInfo');
        ipcMain.removeAllListeners('system:getUsername');
    }
}

module.exports = SystemHandlers;