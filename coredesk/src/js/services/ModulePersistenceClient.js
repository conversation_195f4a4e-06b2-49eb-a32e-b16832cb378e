/**
 * ModulePersistenceClient
 * Client-side wrapper for JsonPersistenceService that works through IPC
 * Provides persistence interface using JSON files instead of unreliable SQLite
 */

class ModulePersistenceClient {
    constructor(logger) {
        this.logger = logger || console;
        this.isInitialized = false;
        this.persistenceMode = 'json'; // Use JSON persistence by default
        
        // Cache for frequently accessed data
        this.moduleCache = new Map();
        this.cacheExpiry = 5 * 60 * 1000; // 5 minutes
        this.lastCacheUpdate = 0;
    }

    /**
     * Initialize the client
     * @returns {Promise<boolean>}
     */
    async initialize() {
        try {
            this.logger.info('ModulePersistenceClient', 'Initializing JSON-based persistence...');

            // Check if JSON persistence is available through IPC
            if (!window.electronAPI || !window.electronAPI.jsonPersistence) {
                this.logger.warn('ModulePersistenceClient', 'JSON persistence API not available, using localStorage fallback');
                return await this.initializeLocalStorageFallback();
            }

            // Test JSON persistence connection
            const testResult = await window.electronAPI.jsonPersistence.getStats();
            if (testResult && testResult.isInitialized) {
                this.logger.info('ModulePersistenceClient', 'JSON persistence service is ready');
                
                // Clean up corrupted localStorage data first
                await this.cleanupCorruptedData();
                
                // Load initial cache
                await this.refreshCache();
                this.isInitialized = true;
                return true;
            } else {
                this.logger.warn('ModulePersistenceClient', 'JSON persistence service not initialized, using localStorage fallback');
                return await this.initializeLocalStorageFallback();
            }

        } catch (error) {
            this.logger.error('ModulePersistenceClient', 'Failed to initialize:', error);
            return await this.initializeLocalStorageFallback();
        }
    }

    async initializeLocalStorageFallback() {
        try {
            this.logger.info('ModulePersistenceClient', 'Initializing localStorage fallback...');
            this.persistenceMode = 'localStorage';
            
            // Clean up corrupted data first
            await this.cleanupCorruptedData();
            
            // Load modules from localStorage
            await this.loadFromLocalStorage();
            
            this.isInitialized = true;
            this.logger.info('ModulePersistenceClient', 'localStorage fallback initialized successfully');
            return true;

        } catch (error) {
            this.logger.error('ModulePersistenceClient', 'localStorage fallback initialization failed:', error);
            return false;
        }
    }

    async loadFromLocalStorage() {
        try {
            this.logger.info('ModulePersistenceClient', 'Loading modules from localStorage...');
            
            const moduleKeys = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith('coredesk_module_')) {
                    moduleKeys.push(key);
                }
            }

            this.logger.info('ModulePersistenceClient', `Found ${moduleKeys.length} modules in localStorage`);
            
            // Refresh cache with localStorage data
            this.moduleCache.clear();
            this.lastCacheUpdate = Date.now();

            for (const key of moduleKeys) {
                try {
                    const moduleData = JSON.parse(localStorage.getItem(key));
                    if (moduleData && moduleData.moduleId) {
                        // Ensure module has status field
                        if (!moduleData.status) {
                            moduleData.status = 'installed';
                        }
                        this.moduleCache.set(moduleData.moduleId, {
                            ...moduleData,
                            source: 'localStorage'
                        });
                        this.logger.debug('ModulePersistenceClient', `Loaded module ${moduleData.moduleId} from localStorage`);
                    }
                } catch (parseError) {
                    this.logger.warn('ModulePersistenceClient', `Failed to parse module data for ${key}:`, parseError);
                }
            }

            this.logger.info('ModulePersistenceClient', `Loaded ${this.moduleCache.size} modules from localStorage`);
            return true;

        } catch (error) {
            this.logger.error('ModulePersistenceClient', 'Failed to load from localStorage:', error);
            return false;
        }
    }


    /**
     * Register a module as installed
     * @param {Object} moduleData - Module data to register
     * @returns {Promise<boolean>}
     */
    async registerInstalledModule(moduleData) {
        try {
            if (!this.isInitialized) {
                throw new Error('Client not initialized');
            }

            const { moduleId, name, version, installPath, manifestData, status = 'active' } = moduleData;
            
            if (!moduleId || !name || !version || !installPath) {
                throw new Error('Missing required module data');
            }

            this.logger.info('ModulePersistenceClient', `Registering module: ${moduleId}@${version} (mode: ${this.persistenceMode})`);

            // Try JSON persistence first
            if (this.persistenceMode === 'json' && window.electronAPI && window.electronAPI.jsonPersistence) {
                try {
                    const result = await window.electronAPI.jsonPersistence.registerModule({
                        moduleId,
                        name,
                        version,
                        status,
                        installPath,
                        manifestData
                    });
                    
                    if (result) {
                        this.logger.info('ModulePersistenceClient', `Module registered successfully with JSON persistence: ${moduleId}`);
                        await this.refreshCache();
                        return true;
                    } else {
                        throw new Error('JSON persistence registration failed');
                    }
                } catch (jsonError) {
                    this.logger.warn('ModulePersistenceClient', `JSON persistence failed for ${moduleId}, falling back to localStorage:`, jsonError);
                    this.persistenceMode = 'localStorage';
                }
            }

            // localStorage persistence fallback
            if (this.persistenceMode === 'localStorage') {
                return this.registerModuleInLocalStorage(moduleData);
            }

            throw new Error('No valid persistence mode available');
            
        } catch (error) {
            this.logger.error('ModulePersistenceClient', `Failed to register module ${moduleData?.moduleId}:`, error);
            return false;
        }
    }

    registerModuleInLocalStorage(moduleData) {
        try {
            const { moduleId, name, version, installPath, manifestData, status = 'installed' } = moduleData;
            
            const storageKey = `coredesk_module_${moduleId}`;
            const moduleRecord = {
                moduleId,
                name,
                version,
                status,
                installPath,
                manifestData,
                installedAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                source: 'localStorage'
            };

            localStorage.setItem(storageKey, JSON.stringify(moduleRecord));
            
            // Update cache immediately
            this.moduleCache.set(moduleId, moduleRecord);
            this.lastCacheUpdate = Date.now();
            
            this.logger.info('ModulePersistenceClient', `Registered module in localStorage: ${moduleId}`);
            return true;
            
        } catch (error) {
            this.logger.error('ModulePersistenceClient', `Failed to register module in localStorage:`, error);
            return false;
        }
    }

    /**
     * Unregister a module
     * @param {string} moduleId - Module ID to unregister
     * @returns {Promise<boolean>}
     */
    async unregisterModule(moduleId) {
        try {
            if (!this.isInitialized) {
                this.logger.warn('ModulePersistenceClient', `Cannot unregister module ${moduleId}: Client not initialized`);
                return false;
            }

            this.logger.info('ModulePersistenceClient', `Unregistering module: ${moduleId}`);

            // Try JSON persistence first
            if (this.persistenceMode === 'json' && window.electronAPI && window.electronAPI.jsonPersistence) {
                try {
                    const result = await window.electronAPI.jsonPersistence.unregisterModule(moduleId);
                    
                    if (result) {
                        this.logger.info('ModulePersistenceClient', `Successfully unregistered module from JSON persistence: ${moduleId}`);
                        
                        // Remove from localStorage as well
                        const storageKey = `coredesk_module_${moduleId}`;
                        localStorage.removeItem(storageKey);
                        
                        // Remove from cache
                        this.moduleCache.delete(moduleId);
                        
                        return true;
                    }
                } catch (jsonError) {
                    this.logger.warn('ModulePersistenceClient', `JSON persistence unregister failed for ${moduleId}:`, jsonError);
                }
            }

            // Try database persistence as fallback
            if (window.electronAPI && window.electronAPI.database) {
                try {
                    const sql = 'DELETE FROM installed_modules WHERE module_id = ?';
                    const result = await window.electronAPI.database.execute(sql, [moduleId]);
                    
                    if (result && result.success) {
                        this.logger.info('ModulePersistenceClient', `Successfully unregistered module from database: ${moduleId}`);
                        
                        // Refresh cache
                        await this.refreshCache();
                        
                        return true;
                    } else {
                        this.logger.warn('ModulePersistenceClient', `Database unregister failed: ${result?.error || 'Unknown error'}`);
                    }
                } catch (dbError) {
                    this.logger.warn('ModulePersistenceClient', `Database unregister failed for ${moduleId}:`, dbError);
                }
            }

            // Always cleanup localStorage regardless of persistence mode
            const storageKey = `coredesk_module_${moduleId}`;
            localStorage.removeItem(storageKey);
            this.moduleCache.delete(moduleId);
            this.logger.info('ModulePersistenceClient', `Removed module from localStorage: ${moduleId}`);
            return true;
        } catch (error) {
            this.logger.error('ModulePersistenceClient', `Failed to unregister module ${moduleId}:`, error);
            return false;
        }
    }

    /**
     * Get all installed modules
     * @returns {Promise<Array>}
     */
    async getInstalledModules() {
        try {
            if (!this.isInitialized) {
                throw new Error('Client not initialized');
            }

            // Use cache if available and fresh
            if (this.isCacheValid()) {
                return Array.from(this.moduleCache.values());
            }

            // Try database persistence first (highest priority)
            if (window.electronAPI && window.electronAPI.database) {
                try {
                    const dbModules = await this.getModulesFromDatabase();
                    if (dbModules && Array.isArray(dbModules) && dbModules.length > 0) {
                        this.logger.info('ModulePersistenceClient', `Retrieved ${dbModules.length} modules from database`);
                        this.updateCache(dbModules);
                        return dbModules;
                    }
                } catch (dbError) {
                    this.logger.warn('ModulePersistenceClient', 'Database query failed, falling back to JSON persistence:', dbError);
                }
            }

            // Try JSON persistence second
            if (this.persistenceMode === 'json' && window.electronAPI && window.electronAPI.jsonPersistence) {
                try {
                    const modules = await window.electronAPI.jsonPersistence.getInstalledModules();
                    
                    if (modules && Array.isArray(modules)) {
                        this.logger.info('ModulePersistenceClient', `Retrieved ${modules.length} modules from JSON persistence`);
                        // Update cache
                        this.updateCache(modules);
                        return modules;
                    }
                } catch (jsonError) {
                    this.logger.warn('ModulePersistenceClient', 'JSON persistence query failed, falling back to localStorage:', jsonError);
                    this.persistenceMode = 'localStorage';
                }
            }

            // localStorage persistence fallback (lowest priority)
            await this.loadFromLocalStorage();
            const localModules = Array.from(this.moduleCache.values());
            this.logger.info('ModulePersistenceClient', `Retrieved ${localModules.length} modules from localStorage`);
            return localModules;
        } catch (error) {
            this.logger.error('ModulePersistenceClient', 'Failed to get installed modules:', error);
            return [];
        }
    }

    /**
     * Get a specific installed module
     * @param {string} moduleId - Module ID
     * @returns {Promise<Object|null>}
     */
    async getInstalledModule(moduleId) {
        try {
            if (!this.isInitialized) {
                throw new Error('Client not initialized');
            }

            // Check cache first
            if (this.isCacheValid() && this.moduleCache.has(moduleId)) {
                return this.moduleCache.get(moduleId);
            }

            // Try JSON persistence first
            if (this.persistenceMode === 'json' && window.electronAPI && window.electronAPI.jsonPersistence) {
                try {
                    const module = await window.electronAPI.jsonPersistence.getModule(moduleId);
                    
                    if (module) {
                        // Update cache entry
                        this.moduleCache.set(moduleId, module);
                        return module;
                    }
                    return null;
                } catch (jsonError) {
                    this.logger.warn('ModulePersistenceClient', `JSON persistence query failed for ${moduleId}, checking localStorage:`, jsonError);
                    this.persistenceMode = 'localStorage';
                }
            }

            // localStorage fallback
            if (this.persistenceMode === 'localStorage') {
                const storageKey = `coredesk_module_${moduleId}`;
                const moduleData = localStorage.getItem(storageKey);
                
                if (moduleData) {
                    try {
                        const parsedModule = JSON.parse(moduleData);
                        this.moduleCache.set(moduleId, parsedModule);
                        return parsedModule;
                    } catch (parseError) {
                        this.logger.warn('ModulePersistenceClient', `Failed to parse module data for ${moduleId}:`, parseError);
                    }
                }
            }
            
            return null;
        } catch (error) {
            this.logger.error('ModulePersistenceClient', `Failed to get module ${moduleId}:`, error);
            return null;
        }
    }

    /**
     * Check if a module is installed
     * @param {string} moduleId - Module ID
     * @returns {Promise<boolean>}
     */
    async isModuleInstalled(moduleId) {
        try {
            const module = await this.getInstalledModule(moduleId);
            return module !== null;
        } catch (error) {
            this.logger.error('ModulePersistenceClient', `Failed to check if module ${moduleId} is installed:`, error);
            return false;
        }
    }

    /**
     * Update module status
     * @param {string} moduleId - Module ID
     * @param {string} status - New status ('active' or 'inactive')
     * @returns {Promise<boolean>}
     */
    async updateModuleStatus(moduleId, status) {
        try {
            if (!this.isInitialized) {
                throw new Error('Client not initialized');
            }

            if (!['active', 'inactive'].includes(status)) {
                throw new Error('Invalid status. Must be "active" or "inactive"');
            }

            this.logger.info('ModulePersistenceClient', `Updating module ${moduleId} status to: ${status}`);

            const sql = `
                UPDATE installed_modules 
                SET status = ?, updated_at = CURRENT_TIMESTAMP 
                WHERE module_id = ?
            `;
            
            const result = await window.electronAPI.database.execute(sql, [status, moduleId]);
            
            if (result.success) {
                this.logger.info('ModulePersistenceClient', `Successfully updated module ${moduleId} status`);
                
                // Refresh cache
                await this.refreshCache();
                
                return true;
            } else {
                this.logger.warn('ModulePersistenceClient', `Failed to update module status: ${result.error}`);
                return false;
            }
        } catch (error) {
            this.logger.error('ModulePersistenceClient', `Failed to update module ${moduleId} status:`, error);
            return false;
        }
    }

    /**
     * Get active modules only
     * @returns {Promise<Array>}
     */
    async getActiveModules() {
        try {
            const allModules = await this.getInstalledModules();
            return allModules.filter(module => module.status === 'active');
        } catch (error) {
            this.logger.error('ModulePersistenceClient', 'Failed to get active modules:', error);
            return [];
        }
    }

    /**
     * Refresh cache
     * @private
     */
    async refreshCache() {
        try {
            // Try JSON persistence first
            if (this.persistenceMode === 'json' && window.electronAPI && window.electronAPI.jsonPersistence) {
                try {
                    const modules = await window.electronAPI.jsonPersistence.getInstalledModules();
                    
                    if (modules && Array.isArray(modules)) {
                        this.updateCache(modules);
                        this.logger.debug('ModulePersistenceClient', `Cache refreshed with ${modules.length} modules from JSON persistence`);
                        return;
                    }
                } catch (jsonError) {
                    this.logger.warn('ModulePersistenceClient', 'Failed to refresh cache from JSON persistence:', jsonError);
                    this.persistenceMode = 'localStorage';
                }
            }

            // localStorage fallback
            if (this.persistenceMode === 'localStorage') {
                await this.loadFromLocalStorage();
                this.logger.debug('ModulePersistenceClient', `Cache refreshed with ${this.moduleCache.size} modules from localStorage`);
            }
        } catch (error) {
            this.logger.error('ModulePersistenceClient', 'Failed to refresh cache:', error);
        }
    }

    /**
     * Update cache with modules array
     * @private
     */
    updateCache(modules) {
        this.moduleCache.clear();
        modules.forEach(module => {
            // Use moduleId for consistency (JSON persistence) or module_id for backward compatibility
            const id = module.moduleId || module.module_id;
            if (id) {
                this.moduleCache.set(id, module);
            }
        });
        this.lastCacheUpdate = Date.now();
    }

    /**
     * Check if cache is valid
     * @private
     */
    isCacheValid() {
        return (Date.now() - this.lastCacheUpdate) < this.cacheExpiry;
    }

    /**
     * Clear cache
     */
    clearCache() {
        this.moduleCache.clear();
        this.lastCacheUpdate = 0;
        this.logger.debug('ModulePersistenceClient', 'Cache cleared');
    }

    /**
     * Clean up corrupted localStorage entries
     * @returns {Promise<Object>} Cleanup results
     */
    async cleanupCorruptedData() {
        try {
            this.logger.info('ModulePersistenceClient', 'Starting cleanup of corrupted localStorage entries...');
            
            const cleanupResults = {
                totalEntries: 0,
                corruptedEntries: 0,
                cleanedEntries: 0,
                validEntries: 0,
                errors: []
            };

            // Find all module entries in localStorage
            const moduleKeys = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith('coredesk_module_')) {
                    moduleKeys.push(key);
                }
            }

            cleanupResults.totalEntries = moduleKeys.length;
            this.logger.info('ModulePersistenceClient', `Found ${moduleKeys.length} module entries in localStorage`);

            // Process each module entry
            for (const key of moduleKeys) {
                try {
                    const rawData = localStorage.getItem(key);
                    
                    // Check for obviously corrupted data
                    if (!rawData || 
                        rawData === 'undefined' || 
                        rawData === 'null' ||
                        rawData === 'undefined undefined' ||
                        rawData === 'null null' ||
                        typeof rawData !== 'string' ||
                        rawData.trim() === '') {
                        
                        this.logger.warn('ModulePersistenceClient', `Removing corrupted entry ${key}: ${JSON.stringify(rawData)}`);
                        localStorage.removeItem(key);
                        cleanupResults.corruptedEntries++;
                        continue;
                    }

                    // Try to parse JSON
                    let moduleData;
                    try {
                        moduleData = JSON.parse(rawData);
                    } catch (parseError) {
                        this.logger.warn('ModulePersistenceClient', `Removing unparseable entry ${key}: ${parseError.message}`);
                        localStorage.removeItem(key);
                        cleanupResults.corruptedEntries++;
                        continue;
                    }

                    // Validate and sanitize the module data
                    const sanitizedData = this.validateAndSanitizeModuleData(moduleData, key);
                    
                    if (!sanitizedData) {
                        this.logger.warn('ModulePersistenceClient', `Removing invalid entry ${key}: failed validation`);
                        localStorage.removeItem(key);
                        cleanupResults.corruptedEntries++;
                        continue;
                    }

                    // Check if data was modified during sanitization
                    const originalSerialized = JSON.stringify(moduleData);
                    const sanitizedSerialized = JSON.stringify(sanitizedData);
                    
                    if (originalSerialized !== sanitizedSerialized) {
                        this.logger.info('ModulePersistenceClient', `Cleaning up entry ${key}: data was sanitized`);
                        localStorage.setItem(key, sanitizedSerialized);
                        cleanupResults.cleanedEntries++;
                    } else {
                        cleanupResults.validEntries++;
                    }

                } catch (error) {
                    this.logger.error('ModulePersistenceClient', `Error processing entry ${key}:`, error);
                    cleanupResults.errors.push({ key, error: error.message });
                    
                    // Remove entries that cause errors
                    try {
                        localStorage.removeItem(key);
                        cleanupResults.corruptedEntries++;
                    } catch (removeError) {
                        this.logger.error('ModulePersistenceClient', `Failed to remove corrupted entry ${key}:`, removeError);
                    }
                }
            }

            this.logger.info('ModulePersistenceClient', 'Cleanup completed:', cleanupResults);
            return cleanupResults;

        } catch (error) {
            this.logger.error('ModulePersistenceClient', 'Cleanup failed:', error);
            throw error;
        }
    }

    /**
     * Validate and sanitize module data
     * @private
     */
    validateAndSanitizeModuleData(moduleData, storageKey) {
        if (!moduleData || typeof moduleData !== 'object') {
            this.logger.warn('ModulePersistenceClient', `Invalid module data for ${storageKey}:`, moduleData);
            return null;
        }

        // Create sanitized copy
        const sanitized = {};

        // Validate required fields
        const requiredFields = ['moduleId', 'name', 'version'];
        for (const field of requiredFields) {
            const value = moduleData[field];
            if (!value || 
                value === 'undefined' || 
                value === 'null' ||
                typeof value !== 'string' ||
                value.trim() === '') {
                
                this.logger.warn('ModulePersistenceClient', `Missing or invalid required field '${field}' in ${storageKey}`);
                return null;
            }
            sanitized[field] = value;
        }

        // Validate optional fields
        const optionalFields = [
            'status', 'installPath', 'installedAt', 'updatedAt', 'source'
        ];

        for (const field of optionalFields) {
            const value = moduleData[field];
            if (value !== undefined && 
                value !== null && 
                value !== 'undefined' && 
                value !== 'null') {
                
                if (typeof value === 'string' && value.trim() !== '') {
                    sanitized[field] = value;
                } else if (typeof value === 'boolean' || typeof value === 'number') {
                    sanitized[field] = value;
                }
            }
        }

        // Handle manifestData specially
        if (moduleData.manifestData) {
            if (typeof moduleData.manifestData === 'object') {
                // Validate manifest data
                const manifestData = moduleData.manifestData;
                if (manifestData.id && manifestData.name && manifestData.version) {
                    sanitized.manifestData = manifestData;
                } else {
                    this.logger.warn('ModulePersistenceClient', `Invalid manifest data in ${storageKey}, creating minimal manifest`);
                    sanitized.manifestData = {
                        id: sanitized.moduleId,
                        name: sanitized.name,
                        version: sanitized.version,
                        description: 'Restored from corrupted data'
                    };
                }
            } else {
                this.logger.warn('ModulePersistenceClient', `Invalid manifest data type in ${storageKey}, creating minimal manifest`);
                sanitized.manifestData = {
                    id: sanitized.moduleId,
                    name: sanitized.name,
                    version: sanitized.version,
                    description: 'Restored from corrupted data'
                };
            }
        }

        // Set defaults for missing fields
        if (!sanitized.status) {
            sanitized.status = 'active';
        }
        if (!sanitized.source) {
            sanitized.source = 'localStorage';
        }
        if (!sanitized.updatedAt) {
            sanitized.updatedAt = new Date().toISOString();
        }

        return sanitized;
    }

    /**
     * Get modules directly from database
     * @private
     */
    async getModulesFromDatabase() {
        try {
            if (!window.electronAPI || !window.electronAPI.database) {
                throw new Error('Database API not available');
            }

            const query = 'SELECT * FROM installed_modules WHERE status = ?';
            const result = await window.electronAPI.database.execute(query, ['active']);

            if (!result.success) {
                throw new Error(`Database query failed: ${result.error}`);
            }

            // Convert database results to standardized format
            const modules = result.data.map(row => ({
                moduleId: row.module_id,
                name: row.name,
                version: row.version,
                status: row.status,
                installPath: row.install_path,
                installedAt: row.installed_at,
                updatedAt: row.updated_at,
                manifestData: row.manifest_data ? JSON.parse(row.manifest_data) : null,
                source: 'database'
            }));

            return modules;

        } catch (error) {
            this.logger.error('ModulePersistenceClient', 'Failed to get modules from database:', error);
            throw error;
        }
    }

    /**
     * Migrate data from one persistence layer to another
     * @param {string} fromSource - Source persistence layer
     * @param {string} toSource - Target persistence layer
     * @returns {Promise<boolean>} Migration success
     */
    async migrateData(fromSource, toSource) {
        try {
            this.logger.info('ModulePersistenceClient', `Starting data migration from ${fromSource} to ${toSource}`);

            let sourceModules = [];

            // Get modules from source
            switch (fromSource) {
                case 'localStorage':
                    await this.loadFromLocalStorage();
                    sourceModules = Array.from(this.moduleCache.values());
                    break;
                case 'json':
                    if (window.electronAPI && window.electronAPI.jsonPersistence) {
                        sourceModules = await window.electronAPI.jsonPersistence.getInstalledModules();
                    }
                    break;
                case 'database':
                    sourceModules = await this.getModulesFromDatabase();
                    break;
                default:
                    throw new Error(`Unknown source: ${fromSource}`);
            }

            if (!sourceModules || sourceModules.length === 0) {
                this.logger.info('ModulePersistenceClient', `No modules found in ${fromSource} to migrate`);
                return true;
            }

            // Migrate each module to target
            let migratedCount = 0;
            for (const module of sourceModules) {
                try {
                    // Standardize module data
                    const moduleData = {
                        moduleId: module.moduleId || module.module_id,
                        name: module.name,
                        version: module.version,
                        status: module.status || 'active',
                        installPath: module.installPath || module.install_path,
                        manifestData: module.manifestData || module.manifest_data
                    };

                    // Skip if required fields are missing
                    if (!moduleData.moduleId || !moduleData.name || !moduleData.version) {
                        this.logger.warn('ModulePersistenceClient', `Skipping module with missing required fields:`, moduleData);
                        continue;
                    }

                    // Register in target persistence layer
                    const success = await this.registerInstalledModule(moduleData);
                    if (success) {
                        migratedCount++;
                        this.logger.info('ModulePersistenceClient', `Migrated module ${moduleData.moduleId} from ${fromSource} to ${toSource}`);
                    }
                } catch (moduleError) {
                    this.logger.error('ModulePersistenceClient', `Failed to migrate module:`, moduleError);
                }
            }

            this.logger.info('ModulePersistenceClient', `Migration completed: ${migratedCount}/${sourceModules.length} modules migrated from ${fromSource} to ${toSource}`);
            return true;

        } catch (error) {
            this.logger.error('ModulePersistenceClient', `Migration failed from ${fromSource} to ${toSource}:`, error);
            return false;
        }
    }
}

// Export for browser environment
if (typeof window !== 'undefined') {
    window.ModulePersistenceClient = ModulePersistenceClient;
}
