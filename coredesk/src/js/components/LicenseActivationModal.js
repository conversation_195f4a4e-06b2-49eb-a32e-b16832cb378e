/**
 * LicenseActivationModal.js
 * Modal component for license activation and trial flow
 * Provides UI for the 4-step trial process and license activation
 */

class LicenseActivationModal {
    constructor() {
        this.modal = null;
        this.isVisible = false;
        this.licenseManager = null;
        
        this.trialFlowHandler = null;
        this.licenseActivationHandler = null;
        this.modalRenderer = null;
        
        this.initialize();
    }

    /**
     * Initialize the license activation modal
     */
    async initialize() {
        console.log('LicenseModal', '[LicenseActivationModal] Initializing...', );
        
        try {
            // Wait for license manager to be ready
            this.licenseManager = window.licenseManager;
            
            if (!this.licenseManager) {
                await this.waitForLicenseManager();
            }
            
            // Initialize handlers
            this.trialFlowHandler = new TrialFlowHandler();
            this.licenseActivationHandler = new LicenseActivationHandler();
            this.modalRenderer = new ModalRenderer();
            
            // Create modal HTML
            this.createModal();
            
            // Set up event listeners
            this.setupEventListeners();
            
            console.log('LicenseModal', '[LicenseActivationModal] Initialized successfully', );
            
        } catch (error) {
            console.error('LicenseModal', '[LicenseActivationModal] Initialization failed:', error);
        }
    }

    /**
     * Wait for license manager to be available
     */
    async waitForLicenseManager() {
        return new Promise((resolve) => {
            const checkLicenseManager = () => {
                if (window.licenseManager) {
                    this.licenseManager = window.licenseManager;
                    resolve();
                } else {
                    setTimeout(checkLicenseManager, 100);
                }
            };
            checkLicenseManager();
        });
    }

    /**
     * Create the modal HTML structure
     */
    createModal() {
        this.modal = document.createElement('div');
        this.modal.className = 'license-modal-container';
        this.modal.id = 'license-modal';
        this.modal.style.display = 'none';
        
        this.modal.innerHTML = this.modalRenderer.getBaseStructure();
        document.body.appendChild(this.modal);
    }

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Close modal
        const closeButton = this.modal.querySelector('#license-modal-close');
        closeButton.addEventListener('click', () => this.hide());
        
        // Close on background click
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal) {
                this.hide();
            }
        });
        
        // Listen for license events
        window.addEventListener(window.COREDESK_CONSTANTS?.EVENTS?.LICENSE_ACTIVATED, () => {
            this.hide();
            this.showSuccessMessage();
        });
    }

    /**
     * Show the modal
     */
    show(mode = 'trial') {
        console.log("Component", `[LicenseActivationModal] Showing modal in ${mode} mode`);
        
        this.isVisible = true;
        this.modal.style.display = 'flex';
        
        // Check current state and show appropriate step
        if (mode === 'trial') {
            this.trialFlowHandler.showTrialFlow(this);
        } else {
            this.licenseActivationHandler.showLicenseActivation(this);
        }
        
        // Focus on modal
        this.modal.querySelector('.license-modal').focus();
    }

    /**
     * Hide the modal
     */
    hide() {
        this.isVisible = false;
        this.modal.style.display = 'none';
        console.log('LicenseModal', '[LicenseActivationModal] Modal hidden', );
    }

    /**
     * Show success message notification
     */
    showSuccessMessage() {
        console.log('LicenseModal', '[LicenseActivationModal] License activated successfully', );
    }

    /**
     * Get modal body and footer elements
     */
    getModalElements() {
        return {
            body: this.modal.querySelector('#license-modal-body'),
            footer: this.modal.querySelector('#license-modal-footer')
        };
    }
}

/**
 * TrialFlowHandler class
 * Handles the 4-step trial license flow
 */
class TrialFlowHandler {
    constructor() {
        this.currentStep = 0;
        this.steps = {
            0: 'welcome',
            1: 'email',
            2: 'verification',
            3: 'generate',
            4: 'activate'
        };
    }

    /**
     * Show trial flow based on current state
     */
    showTrialFlow(modalInstance) {
        const trialStatus = modalInstance.licenseManager.getTrialFlowStatus();
        
        switch (trialStatus.step) {
            case 0:
                this.showWelcomeStep(modalInstance);
                break;
            case 1:
            case 2:
                this.showEmailVerificationStep(modalInstance, trialStatus);
                break;
            case 3:
                this.showGenerateStep(modalInstance, trialStatus);
                break;
            case 4:
                this.showActivateStep(modalInstance, trialStatus);
                break;
            default:
                this.showWelcomeStep(modalInstance);
        }
    }

    /**
     * Show welcome step
     */
    showWelcomeStep(modalInstance) {
        this.currentStep = 0;
        const { body, footer } = modalInstance.getModalElements();
        
        body.innerHTML = modalInstance.modalRenderer.getWelcomeStepContent();
        footer.innerHTML = modalInstance.modalRenderer.getWelcomeStepFooter();
        
        // Add event listeners
        modalInstance.modal.querySelector('#trial-option button').addEventListener('click', () => {
            this.showEmailStep(modalInstance);
        });
        
        modalInstance.modal.querySelector('#license-option button').addEventListener('click', () => {
            modalInstance.licenseActivationHandler.showLicenseActivation(modalInstance);
        });
    }

    /**
     * Show email step (Step 1)
     */
    showEmailStep(modalInstance) {
        this.currentStep = 1;
        const { body, footer } = modalInstance.getModalElements();
        
        body.innerHTML = modalInstance.modalRenderer.getEmailStepContent();
        footer.innerHTML = modalInstance.modalRenderer.getEmailStepFooter();
        
        this.setupEmailStepListeners(modalInstance);
        
        // Focus on email input
        modalInstance.modal.querySelector('#trial-email').focus();
    }

    /**
     * Setup email step event listeners
     */
    setupEmailStepListeners(modalInstance) {
        modalInstance.modal.querySelector('#back-button').addEventListener('click', () => {
            this.showWelcomeStep(modalInstance);
        });
        
        modalInstance.modal.querySelector('#send-email-button').addEventListener('click', () => {
            this.handleEmailSubmit(modalInstance);
        });
        
        // Enter key support
        modalInstance.modal.querySelector('#trial-email').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.handleEmailSubmit(modalInstance);
            }
        });
    }

    /**
     * Handle email submission
     */
    async handleEmailSubmit(modalInstance) {
        const emailInput = modalInstance.modal.querySelector('#trial-email');
        const termsCheckbox = modalInstance.modal.querySelector('#terms-checkbox');
        const loadingIndicator = modalInstance.modal.querySelector('#email-loading');
        const errorDiv = modalInstance.modal.querySelector('#email-error');
        const sendButton = modalInstance.modal.querySelector('#send-email-button');
        
        const email = emailInput.value.trim();
        
        // Validate email using security manager
        const emailValidation = window.securityManager?.validateInput(email, 'email');
        if (!emailValidation?.valid) {
            this.showError(modalInstance, emailValidation?.error || 'Please enter a valid email address');
            return;
        }
        
        if (!termsCheckbox.checked) {
            this.showError(modalInstance, 'Please accept the Terms of Service');
            return;
        }
        
        // Check rate limiting for email verification requests
        const rateLimitCheck = window.inputValidator?.validateRateLimit(`email_verification_${emailValidation.sanitized}`, 3, 300000); // 3 requests per 5 minutes
        if (!rateLimitCheck?.valid) {
            this.showError(modalInstance, 'Too many verification requests. Please try again later.');
            return;
        }
        
        // Show loading
        loadingIndicator.style.display = 'block';
        errorDiv.style.display = 'none';
        sendButton.disabled = true;
        
        try {
            // Start trial flow with sanitized email
            const result = await modalInstance.licenseManager.startTrialFlow(emailValidation.sanitized);
            
            if (result.success) {
                this.showEmailVerificationStep(modalInstance, { email: emailValidation.sanitized });
            } else {
                this.showError(modalInstance, result.error);
            }
            
        } catch (error) {
            this.showError(modalInstance, 'Failed to send verification email. Please try again.');
        } finally {
            loadingIndicator.style.display = 'none';
            sendButton.disabled = false;
        }
    }

    /**
     * Show email verification step (Step 2)
     */
    showEmailVerificationStep(modalInstance, trialStatus) {
        this.currentStep = 2;
        const { body, footer } = modalInstance.getModalElements();
        
        body.innerHTML = modalInstance.modalRenderer.getVerificationStepContent(trialStatus.email);
        footer.innerHTML = modalInstance.modalRenderer.getVerificationStepFooter();
        
        this.setupVerificationStepListeners(modalInstance, trialStatus);
        
        // Focus on code input
        modalInstance.modal.querySelector('#verification-code').focus();
    }

    /**
     * Setup verification step event listeners
     */
    setupVerificationStepListeners(modalInstance, trialStatus) {
        modalInstance.modal.querySelector('#back-button').addEventListener('click', () => {
            this.showEmailStep(modalInstance);
        });
        
        modalInstance.modal.querySelector('#verify-button').addEventListener('click', () => {
            this.handleVerificationSubmit(modalInstance);
        });
        
        modalInstance.modal.querySelector('#resend-button').addEventListener('click', () => {
            this.handleResendCode(modalInstance, trialStatus.email);
        });
        
        // Auto-format verification code
        const codeInput = modalInstance.modal.querySelector('#verification-code');
        codeInput.addEventListener('input', (e) => {
            e.target.value = e.target.value.replace(/\D/g, '');
        });
        
        // Enter key support
        codeInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.handleVerificationSubmit(modalInstance);
            }
        });
    }

    /**
     * Handle verification code submission
     */
    async handleVerificationSubmit(modalInstance) {
        const codeInput = modalInstance.modal.querySelector('#verification-code');
        const loadingIndicator = modalInstance.modal.querySelector('#verification-loading');
        const errorDiv = modalInstance.modal.querySelector('#verification-error');
        const verifyButton = modalInstance.modal.querySelector('#verify-button');
        
        const code = codeInput.value.trim();
        
        // Validate verification code using security manager
        const codeValidation = window.securityManager?.validateInput(code, 'verificationCode');
        if (!codeValidation?.valid) {
            this.showError(modalInstance, codeValidation?.error || 'Please enter a valid 6-digit verification code');
            return;
        }
        
        // Check rate limiting for verification attempts
        const rateLimitCheck = window.inputValidator?.validateRateLimit('verification_attempts', 10, 600000); // 10 attempts per 10 minutes
        if (!rateLimitCheck?.valid) {
            this.showError(modalInstance, 'Too many verification attempts. Please try again later.');
            return;
        }
        
        // Show loading
        loadingIndicator.style.display = 'block';
        errorDiv.style.display = 'none';
        verifyButton.disabled = true;
        
        try {
            // Verify email with sanitized code
            const result = await modalInstance.licenseManager.verifyTrialEmail(codeValidation.sanitized);
            
            if (result.success) {
                this.showGenerateStep(modalInstance, result);
            } else {
                this.showError(modalInstance, result.error);
            }
            
        } catch (error) {
            this.showError(modalInstance, 'Verification failed. Please try again.');
        } finally {
            loadingIndicator.style.display = 'none';
            verifyButton.disabled = false;
        }
    }

    /**
     * Show generate step (Step 3)
     */
    showGenerateStep(modalInstance, trialStatus) {
        this.currentStep = 3;
        const { body, footer } = modalInstance.getModalElements();
        
        body.innerHTML = modalInstance.modalRenderer.getGenerateStepContent();
        footer.innerHTML = modalInstance.modalRenderer.getGenerateStepFooter();
        
        // Add event listeners
        modalInstance.modal.querySelector('#back-button').addEventListener('click', () => {
            this.showEmailVerificationStep(modalInstance, trialStatus);
        });
        
        modalInstance.modal.querySelector('#generate-button').addEventListener('click', () => {
            this.handleGenerateTrial(modalInstance);
        });
    }

    /**
     * Handle trial license generation
     */
    async handleGenerateTrial(modalInstance) {
        const loadingIndicator = modalInstance.modal.querySelector('#generate-loading');
        const errorDiv = modalInstance.modal.querySelector('#generate-error');
        const generateButton = modalInstance.modal.querySelector('#generate-button');
        
        // Show loading
        loadingIndicator.style.display = 'block';
        errorDiv.style.display = 'none';
        generateButton.disabled = true;
        
        try {
            // Generate trial license
            const result = await modalInstance.licenseManager.generateTrialLicense();
            
            if (result.success) {
                this.showActivateStep(modalInstance, result);
            } else {
                this.showError(modalInstance, result.error);
            }
            
        } catch (error) {
            this.showError(modalInstance, 'Failed to generate trial license. Please try again.');
        } finally {
            loadingIndicator.style.display = 'none';
            generateButton.disabled = false;
        }
    }

    /**
     * Show activate step (Step 4)
     */
    showActivateStep(modalInstance, trialStatus) {
        this.currentStep = 4;
        const { body, footer } = modalInstance.getModalElements();
        
        body.innerHTML = modalInstance.modalRenderer.getActivateStepContent(trialStatus.licenseKey);
        footer.innerHTML = modalInstance.modalRenderer.getActivateStepFooter();
        
        // Add event listeners
        modalInstance.modal.querySelector('#back-button').addEventListener('click', () => {
            this.showGenerateStep(modalInstance, trialStatus);
        });
        
        modalInstance.modal.querySelector('#activate-button').addEventListener('click', () => {
            this.handleActivateTrial(modalInstance);
        });
        
        modalInstance.modal.querySelector('#copy-license-key').addEventListener('click', () => {
            this.copyLicenseKey(modalInstance, trialStatus.licenseKey);
        });
    }

    /**
     * Handle trial license activation
     */
    async handleActivateTrial(modalInstance) {
        const loadingIndicator = modalInstance.modal.querySelector('#activate-loading');
        const errorDiv = modalInstance.modal.querySelector('#activate-error');
        const activateButton = modalInstance.modal.querySelector('#activate-button');
        
        // Show loading
        loadingIndicator.style.display = 'block';
        errorDiv.style.display = 'none';
        activateButton.disabled = true;
        
        try {
            // Activate trial license
            const result = await modalInstance.licenseManager.activateTrialLicense();
            
            if (result.success) {
                this.showSuccessStep(modalInstance, result);
            } else {
                this.showError(modalInstance, result.error);
            }
            
        } catch (error) {
            this.showError(modalInstance, 'Failed to activate license. Please try again.');
        } finally {
            loadingIndicator.style.display = 'none';
            activateButton.disabled = false;
        }
    }

    /**
     * Show success step
     */
    showSuccessStep(modalInstance, result) {
        const { body, footer } = modalInstance.getModalElements();
        
        body.innerHTML = modalInstance.modalRenderer.getSuccessStepContent(result);
        footer.innerHTML = modalInstance.modalRenderer.getSuccessStepFooter();
        
        // Add event listener
        modalInstance.modal.querySelector('#start-using-button').addEventListener('click', () => {
            modalInstance.hide();
            
            // Emit event to start using the application
            window.CoreDeskEvents?.emit(window.COREDESK_CONSTANTS?.EVENTS?.LICENSE_ACTIVATION_COMPLETE, {
                license: result.license,
                isTrialLicense: result.license?.type === 'trial'
            });
        });
    }

    /**
     * Copy license key to clipboard
     */
    async copyLicenseKey(modalInstance, licenseKey) {
        try {
            await navigator.clipboard.writeText(licenseKey);
            
            // Visual feedback
            const copyButton = modalInstance.modal.querySelector('#copy-license-key');
            const originalText = copyButton.textContent;
            copyButton.textContent = '✓';
            copyButton.style.background = '#28a745';
            
            setTimeout(() => {
                copyButton.textContent = originalText;
                copyButton.style.background = '';
            }, 2000);
            
        } catch (error) {
            console.error('LicenseModal', 'Failed to copy license key:', error);
        }
    }

    /**
     * Handle resend verification code
     */
    async handleResendCode(modalInstance, email) {
        const resendButton = modalInstance.modal.querySelector('#resend-button');
        
        resendButton.disabled = true;
        resendButton.textContent = 'Sending...';
        
        try {
            // Simulate resend
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            resendButton.textContent = 'Code sent!';
            
            setTimeout(() => {
                resendButton.textContent = 'Resend verification email';
                resendButton.disabled = false;
            }, 3000);
            
        } catch (error) {
            resendButton.textContent = 'Failed to resend';
            resendButton.disabled = false;
        }
    }

    /**
     * Show error message
     */
    showError(modalInstance, message) {
        const errorDivs = modalInstance.modal.querySelectorAll('.error-message');
        errorDivs.forEach(div => {
            div.textContent = message;
            div.style.display = 'block';
        });
    }
}

/**
 * LicenseActivationHandler class
 * Handles license key activation for existing licenses
 */
class LicenseActivationHandler {
    /**
     * Show license activation for existing license keys
     */
    showLicenseActivation(modalInstance) {
        const { body, footer } = modalInstance.getModalElements();
        
        body.innerHTML = modalInstance.modalRenderer.getLicenseActivationContent();
        footer.innerHTML = modalInstance.modalRenderer.getLicenseActivationFooter();
        
        this.setupLicenseActivationListeners(modalInstance);
        
        // Focus on license input
        modalInstance.modal.querySelector('#license-key-input').focus();
    }

    /**
     * Setup license activation event listeners
     */
    setupLicenseActivationListeners(modalInstance) {
        // Add event listeners
        modalInstance.modal.querySelector('#back-button').addEventListener('click', () => {
            modalInstance.trialFlowHandler.showWelcomeStep(modalInstance);
        });
        
        modalInstance.modal.querySelector('#activate-license-button').addEventListener('click', () => {
            this.handleLicenseActivation(modalInstance);
        });
        
        // Format license key input
        const licenseInput = modalInstance.modal.querySelector('#license-key-input');
        licenseInput.addEventListener('input', (e) => {
            this.formatLicenseKeyInput(e.target);
        });
        
        // Enter key support
        licenseInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.handleLicenseActivation(modalInstance);
            }
        });
    }

    /**
     * Handle license key activation
     */
    async handleLicenseActivation(modalInstance) {
        const licenseInput = modalInstance.modal.querySelector('#license-key-input');
        const loadingIndicator = modalInstance.modal.querySelector('#license-loading');
        const errorDiv = modalInstance.modal.querySelector('#license-error');
        const activateButton = modalInstance.modal.querySelector('#activate-license-button');
        
        const licenseKey = licenseInput.value.trim();
        
        // Validate license key using security manager
        const licenseValidation = window.securityManager?.validateInput(licenseKey, 'licenseKey');
        if (!licenseValidation?.valid) {
            this.showError(modalInstance, licenseValidation?.error || 'Please enter a valid license key');
            return;
        }
        
        // Check rate limiting for license activation attempts
        const rateLimitCheck = window.inputValidator?.validateRateLimit(`license_activation_${licenseValidation.sanitized}`, 5, 900000); // 5 attempts per 15 minutes
        if (!rateLimitCheck?.valid) {
            this.showError(modalInstance, 'Too many activation attempts. Please try again later.');
            return;
        }
        
        // Show loading
        loadingIndicator.style.display = 'block';
        errorDiv.style.display = 'none';
        activateButton.disabled = true;
        
        try {
            // Activate license with sanitized license key
            const result = await modalInstance.licenseManager.activateLicense(licenseValidation.sanitized);
            
            if (result.success) {
                modalInstance.trialFlowHandler.showSuccessStep(modalInstance, result);
            } else {
                this.showError(modalInstance, result.error);
            }
            
        } catch (error) {
            this.showError(modalInstance, 'License activation failed. Please try again.');
        } finally {
            loadingIndicator.style.display = 'none';
            activateButton.disabled = false;
        }
    }

    /**
     * Format license key input
     */
    formatLicenseKeyInput(input) {
        let value = input.value.replace(/[^A-Z0-9]/g, ''); // Remove non-alphanumeric
        value = value.substring(0, 16); // Limit to 16 characters
        
        // Add hyphens
        let formatted = '';
        for (let i = 0; i < value.length; i++) {
            if (i > 0 && i % 4 === 0) {
                formatted += '-';
            }
            formatted += value[i];
        }
        
        input.value = formatted;
    }

    /**
     * Show error message
     */
    showError(modalInstance, message) {
        const errorDivs = modalInstance.modal.querySelectorAll('.error-message');
        errorDivs.forEach(div => {
            div.textContent = message;
            div.style.display = 'block';
        });
    }
}

/**
 * ModalRenderer class
 * Handles HTML content generation for modal steps
 */
class ModalRenderer {
    /**
     * Get base modal structure
     */
    getBaseStructure() {
        return `
            <div class="license-modal">
                <div class="license-modal-header">
                    <h2 class="license-modal-title">CoreDesk License Activation</h2>
                    <button class="license-modal-close" id="license-modal-close">×</button>
                </div>
                
                <div class="license-modal-body" id="license-modal-body">
                    <!-- Dynamic content will be inserted here -->
                </div>
                
                <div class="license-modal-footer" id="license-modal-footer">
                    <!-- Dynamic footer will be inserted here -->
                </div>
            </div>
        `;
    }

    /**
     * Get welcome step content
     */
    getWelcomeStepContent() {
        return `
            <div class="license-step welcome-step">
                <div class="step-header">
                    <h3>Welcome to CoreDesk</h3>
                    <p>Choose how you'd like to activate CoreDesk:</p>
                </div>
                
                <div class="activation-options">
                    <div class="option-card trial-option" id="trial-option">
                        <div class="option-icon">🚀</div>
                        <h4>Start Free Trial</h4>
                        <p>Try CoreDesk free for 30 days</p>
                        <ul>
                            <li>✓ Full access to LexFlow module</li>
                            <li>✓ Full access to ProtocolX module</li>
                            <li>✓ 30 days trial period</li>
                            <li>✓ Email support</li>
                        </ul>
                        <button class="btn btn-primary">Start Free Trial</button>
                    </div>
                    
                    <div class="option-card license-option" id="license-option">
                        <div class="option-icon">🔑</div>
                        <h4>Activate License</h4>
                        <p>Already have a license key?</p>
                        <ul>
                            <li>✓ Full features unlocked</li>
                            <li>✓ No time limitations</li>
                            <li>✓ Priority support</li>
                            <li>✓ Future updates</li>
                        </ul>
                        <button class="btn btn-secondary">Enter License Key</button>
                    </div>
                </div>
                
                <div class="device-info">
                    <p><strong>Device ID:</strong> ${this.getDeviceIdPreview()}</p>
                    <p><small>This unique identifier is used for license validation</small></p>
                </div>
            </div>
        `;
    }

    /**
     * Get welcome step footer
     */
    getWelcomeStepFooter() {
        return `
            <div class="footer-info">
                <p>Need help? Visit our <a href="#" onclick="window.electronAPI?.openExternal('https://coredeskpro.com/support')">support center</a></p>
            </div>
        `;
    }

    /**
     * Get email step content
     */
    getEmailStepContent() {
        return `
            <div class="license-step email-step">
                <div class="step-header">
                    <div class="step-indicator">
                        <span class="step-number">1</span>
                        <span class="step-title">Email Verification</span>
                    </div>
                    <p>Enter your email address to start your free trial</p>
                </div>
                
                <div class="step-content">
                    <div class="form-group">
                        <label for="trial-email">Email Address</label>
                        <input type="email" id="trial-email" class="form-input" 
                               placeholder="<EMAIL>" required>
                        <small class="form-help">We'll send you a verification code</small>
                    </div>
                    
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="terms-checkbox" required>
                            I agree to the <a href="#" onclick="window.electronAPI?.openExternal('https://coredeskpro.com/terms')">Terms of Service</a> 
                            and <a href="#" onclick="window.electronAPI?.openExternal('https://coredeskpro.com/privacy')">Privacy Policy</a>
                        </label>
                    </div>
                </div>
                
                <div class="loading-indicator" id="email-loading" style="display: none;">
                    <div class="spinner"></div>
                    <p>Sending verification email...</p>
                </div>
                
                <div class="error-message" id="email-error" style="display: none;"></div>
            </div>
        `;
    }

    /**
     * Get email step footer
     */
    getEmailStepFooter() {
        return `
            <div class="modal-actions">
                <button class="btn btn-secondary" id="back-button">Back</button>
                <button class="btn btn-primary" id="send-email-button">Send Verification Email</button>
            </div>
        `;
    }

    /**
     * Get verification step content
     */
    getVerificationStepContent(email) {
        return `
            <div class="license-step verification-step">
                <div class="step-header">
                    <div class="step-indicator">
                        <span class="step-number">2</span>
                        <span class="step-title">Email Verification</span>
                    </div>
                    <p>We've sent a verification code to <strong>${email}</strong></p>
                </div>
                
                <div class="step-content">
                    <div class="form-group">
                        <label for="verification-code">Verification Code</label>
                        <input type="text" id="verification-code" class="form-input verification-input" 
                               placeholder="000000" maxlength="6" required>
                        <small class="form-help">Enter the 6-digit code from your email</small>
                    </div>
                    
                    <div class="resend-section">
                        <p>Didn't receive the email?</p>
                        <button class="btn btn-link" id="resend-button">Resend verification email</button>
                    </div>
                </div>
                
                <div class="loading-indicator" id="verification-loading" style="display: none;">
                    <div class="spinner"></div>
                    <p>Verifying code...</p>
                </div>
                
                <div class="error-message" id="verification-error" style="display: none;"></div>
            </div>
        `;
    }

    /**
     * Get verification step footer
     */
    getVerificationStepFooter() {
        return `
            <div class="modal-actions">
                <button class="btn btn-secondary" id="back-button">Back</button>
                <button class="btn btn-primary" id="verify-button">Verify Code</button>
            </div>
        `;
    }

    /**
     * Get generate step content
     */
    getGenerateStepContent() {
        return `
            <div class="license-step generate-step">
                <div class="step-header">
                    <div class="step-indicator">
                        <span class="step-number">3</span>
                        <span class="step-title">Generate Trial License</span>
                    </div>
                    <p>Ready to generate your 30-day trial license</p>
                </div>
                
                <div class="step-content">
                    <div class="device-summary">
                        <h4>Device Information</h4>
                        ${this.renderDeviceSummary()}
                    </div>
                    
                    <div class="trial-info">
                        <h4>Trial License Details</h4>
                        <ul>
                            <li>✓ 30 days full access</li>
                            <li>✓ LexFlow module included</li>
                            <li>✓ ProtocolX module included</li>
                            <li>✓ Email support</li>
                        </ul>
                    </div>
                </div>
                
                <div class="loading-indicator" id="generate-loading" style="display: none;">
                    <div class="spinner"></div>
                    <p>Generating trial license...</p>
                </div>
                
                <div class="error-message" id="generate-error" style="display: none;"></div>
            </div>
        `;
    }

    /**
     * Get generate step footer
     */
    getGenerateStepFooter() {
        return `
            <div class="modal-actions">
                <button class="btn btn-secondary" id="back-button">Back</button>
                <button class="btn btn-primary" id="generate-button">Generate Trial License</button>
            </div>
        `;
    }

    /**
     * Get activate step content
     */
    getActivateStepContent(licenseKey) {
        return `
            <div class="license-step activate-step">
                <div class="step-header">
                    <div class="step-indicator">
                        <span class="step-number">4</span>
                        <span class="step-title">Activate Trial License</span>
                    </div>
                    <p>Your trial license has been generated successfully!</p>
                </div>
                
                <div class="step-content">
                    <div class="license-display">
                        <label>Your Trial License Key:</label>
                        <div class="license-key-display">
                            <code id="license-key-text">${licenseKey}</code>
                            <button class="btn btn-sm copy-button" id="copy-license-key" title="Copy to clipboard">📋</button>
                        </div>
                        <small class="form-help">Save this license key for future reference</small>
                    </div>
                    
                    <div class="activation-info">
                        <h4>Ready to Activate</h4>
                        <p>Click "Activate License" to complete the setup and start using CoreDesk.</p>
                    </div>
                </div>
                
                <div class="loading-indicator" id="activate-loading" style="display: none;">
                    <div class="spinner"></div>
                    <p>Activating license...</p>
                </div>
                
                <div class="error-message" id="activate-error" style="display: none;"></div>
            </div>
        `;
    }

    /**
     * Get activate step footer
     */
    getActivateStepFooter() {
        return `
            <div class="modal-actions">
                <button class="btn btn-secondary" id="back-button">Back</button>
                <button class="btn btn-primary" id="activate-button">Activate License</button>
            </div>
        `;
    }

    /**
     * Get license activation content
     */
    getLicenseActivationContent() {
        return `
            <div class="license-step license-activation-step">
                <div class="step-header">
                    <h3>Activate License Key</h3>
                    <p>Enter your CoreDesk license key to activate the software</p>
                </div>
                
                <div class="step-content">
                    <div class="form-group">
                        <label for="license-key-input">License Key</label>
                        <input type="text" id="license-key-input" class="form-input license-key-input" 
                               placeholder="XXXX-XXXX-XXXX-XXXX" maxlength="19" required>
                        <small class="form-help">Enter the license key from your purchase confirmation</small>
                    </div>
                    
                    <div class="device-summary">
                        <h4>Device Information</h4>
                        ${this.renderDeviceSummary()}
                    </div>
                </div>
                
                <div class="loading-indicator" id="license-loading" style="display: none;">
                    <div class="spinner"></div>
                    <p>Activating license...</p>
                </div>
                
                <div class="error-message" id="license-error" style="display: none;"></div>
            </div>
        `;
    }

    /**
     * Get license activation footer
     */
    getLicenseActivationFooter() {
        return `
            <div class="modal-actions">
                <button class="btn btn-secondary" id="back-button">Back</button>
                <button class="btn btn-primary" id="activate-license-button">Activate License</button>
            </div>
        `;
    }

    /**
     * Get success step content
     */
    getSuccessStepContent(result) {
        const licenseType = result.license?.type || 'unknown';
        const isTrialLicense = licenseType === 'trial';
        
        return `
            <div class="license-step success-step">
                <div class="success-header">
                    <div class="success-icon">✅</div>
                    <h3>License Activated Successfully!</h3>
                    <p>${isTrialLicense ? 'Your 30-day trial has started' : 'CoreDesk is now fully activated'}</p>
                </div>
                
                <div class="license-details">
                    <h4>License Information</h4>
                    <div class="license-info-grid">
                        <div class="info-item">
                            <label>License Type:</label>
                            <span>${this.formatLicenseType(licenseType)}</span>
                        </div>
                        <div class="info-item">
                            <label>License Key:</label>
                            <span><code>${result.license?.key}</code></span>
                        </div>
                        ${result.license?.email ? `
                            <div class="info-item">
                                <label>Email:</label>
                                <span>${result.license.email}</span>
                            </div>
                        ` : ''}
                        ${result.license?.modules ? `
                            <div class="info-item">
                                <label>Available Modules:</label>
                                <span>${result.license.modules.join(', ')}</span>
                            </div>
                        ` : ''}
                        ${isTrialLicense ? `
                            <div class="info-item">
                                <label>Trial Period:</label>
                                <span>30 days (expires ${this.formatExpirationDate(result.license?.expiresAt)})</span>
                            </div>
                        ` : ''}
                    </div>
                </div>
                
                <div class="next-steps">
                    <h4>What's Next?</h4>
                    <ul>
                        <li>✓ CoreDesk is ready to use</li>
                        <li>✓ All features are now unlocked</li>
                        <li>✓ You can start working with your modules</li>
                        ${isTrialLicense ? '<li>📧 You will receive activation confirmation via email</li>' : ''}
                    </ul>
                </div>
            </div>
        `;
    }

    /**
     * Get success step footer
     */
    getSuccessStepFooter() {
        return `
            <div class="modal-actions">
                <button class="btn btn-primary btn-large" id="start-using-button">Start Using CoreDesk</button>
            </div>
        `;
    }

    /**
     * Get device ID preview
     */
    getDeviceIdPreview() {
        if (window.licenseManager?.licenseValidator?.getDeviceFingerprint) {
            const deviceFingerprint = window.licenseManager.licenseValidator.getDeviceFingerprint();
            if (deviceFingerprint) {
                return `${deviceFingerprint.substring(0, 8)}...`;
            }
        }
        return 'Generating...';
    }

    /**
     * Render device summary
     */
    renderDeviceSummary() {
        if (window.licenseManager?.licenseValidator?.getDeviceSummary) {
            const deviceSummary = window.licenseManager.licenseValidator.getDeviceSummary();
            
            if (deviceSummary) {
                return `
                    <div class="device-info-grid">
                        <div class="device-info-item">
                            <label>Platform:</label>
                            <span>${deviceSummary.platform}</span>
                        </div>
                        <div class="device-info-item">
                            <label>Architecture:</label>
                            <span>${deviceSummary.architecture}</span>
                        </div>
                        <div class="device-info-item">
                            <label>CPU Cores:</label>
                            <span>${deviceSummary.cpuCores}</span>
                        </div>
                        <div class="device-info-item">
                            <label>Screen Resolution:</label>
                            <span>${deviceSummary.screenResolution}</span>
                        </div>
                        <div class="device-info-item">
                            <label>Device ID:</label>
                            <span><code>${deviceSummary.fingerprint}</code></span>
                        </div>
                    </div>
                `;
            }
        }
        
        return '<p>Device information not available</p>';
    }

    /**
     * Format license type for display
     */
    formatLicenseType(type) {
        const types = {
            trial: 'Trial License',
            standard: 'Standard License',
            professional: 'Professional License',
            enterprise: 'Enterprise License'
        };
        
        return types[type] || 'Unknown License';
    }

    /**
     * Format expiration date
     */
    formatExpirationDate(dateString) {
        if (!dateString) return 'Never';
        
        const date = new Date(dateString);
        return date.toLocaleDateString();
    }
}

// Create global instance
window.licenseActivationModal = new LicenseActivationModal();

console.log('LicenseModal', '[LicenseActivationModal] Class defined and global instance created successfully', );