/**
 * Panel Manager Component
 * Manages all panels in the CoreDesk interface (left, right, bottom panels)
 * Integrates with Activity Bar for VS Code-like experience
 */

class PanelManager {
    constructor() {
        this.panels = {
            left: {
                element: null,
                isOpen: true,
                currentContent: 'explorer',
                width: 300
            },
            right: {
                element: null,
                isOpen: false,
                currentContent: null,
                width: 300
            },
            bottom: {
                element: null,
                isOpen: false,
                currentContent: 'terminal',
                height: 200
            }
        };
        
        this.contentProviders = new Map();
        this.lastKnownActiveModule = null;
        this.initialize();
    }

    /**
     * Initialize panel manager
     */
    initialize() {
        console.log('UI', '[PanelManager] Initializing...', );
        
        this.findPanelElements();
        this.setupEventListeners();
        this.registerContentProviders();
        this.updatePanelStates();
        
        console.log('UI', '[PanelManager] Initialized successfully', );
    }

    /**
     * Find panel elements in DOM
     */
    findPanelElements() {
        this.panels.left.element = document.getElementById('left-panel');
        this.panels.right.element = document.getElementById('right-panel');
        this.panels.bottom.element = document.getElementById('bottom-panel');
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Panel close buttons
        document.addEventListener('click', (e) => {
            const closeBtn = e.target.closest('.panel-close');
            if (closeBtn) {
                const panelType = closeBtn.dataset.panel;
                this.closePanel(panelType);
            }
        });

        // Title bar panel toggles
        const leftToggle = document.getElementById('toggle-left-panel');
        const rightToggle = document.getElementById('toggle-right-panel');
        const bottomToggle = document.getElementById('toggle-bottom-panel');

        if (leftToggle) {
            leftToggle.addEventListener('click', () => this.togglePanel('left'));
        }
        if (rightToggle) {
            rightToggle.addEventListener('click', () => this.togglePanel('right'));
        }
        if (bottomToggle) {
            bottomToggle.addEventListener('click', () => this.togglePanel('bottom'));
        }

        // Activity bar buttons handling is now done by ActivityBar.js component

        // Module activation buttons
        document.addEventListener('click', (e) => {
            const activateBtn = e.target.closest('.activate-module-btn');
            if (activateBtn) {
                const moduleCode = activateBtn.dataset.module;
                this.handleModuleActivation(moduleCode);
            }
        });

        // Module deactivation buttons
        document.addEventListener('click', (e) => {
            const deactivateBtn = e.target.closest('.deactivate-module-btn');
            if (deactivateBtn) {
                const moduleCode = deactivateBtn.dataset.module;
                this.handleModuleDeactivation(moduleCode);
            }
        });

        // Browse repository button
        document.addEventListener('click', (e) => {
            const browseBtn = e.target.closest('.browse-repository-btn');
            if (browseBtn) {
                this.handleBrowseRepository();
            }
        });

        // Install module buttons
        document.addEventListener('click', (e) => {
            const installBtn = e.target.closest('.install-module-btn');
            if (installBtn) {
                const moduleId = installBtn.dataset.module;
                this.handleModuleInstallation(moduleId);
            }
        });

        // Module refresh button
        document.addEventListener('click', (e) => {
            const refreshBtn = e.target.closest('.module-refresh-btn');
            if (refreshBtn) {
                this.refreshModulesPanel();
            }
        });

        // Listen for module switch events from other sources
        window.addEventListener('module:switched', (e) => {
            console.log('UI', '[PanelManager] Received module:switched event:', e.detail);
            this.refreshModulesPanel();
        });

        // Listen for window focus to refresh panel (in case of external changes)
        window.addEventListener('focus', () => {
            // Small delay to ensure state has settled
            setTimeout(() => {
                this.refreshModulesPanel();
            }, 100);
        });

        // Setup periodic check for module state changes (backup mechanism)
        this.setupPeriodicRefresh();
        
        // Setup file explorer event handlers
        this.setupFileExplorerHandlers();
    }

    /**
     * Register content providers for different panel types
     */
    registerContentProviders() {
        // Explorer content - Real file system explorer
        this.contentProviders.set('explorer', () => {
            if (window.fileExplorer) {
                return window.fileExplorer.generateExplorerContent();
            } else {
                return `
                    <div class="explorer-loading">
                        <div class="loading-spinner"></div>
                        <p>Inicializando explorador de archivos...</p>
                    </div>
                `;
            }
        });

        // Search content
        this.contentProviders.set('search', () => `
            <div class="search-content">
                <div class="search-box">
                    <input type="text" placeholder="Buscar en CoreDesk..." class="search-input">
                    <button class="search-btn">🔍</button>
                </div>
                <div class="search-filters">
                    <label><input type="checkbox" checked> Casos</label>
                    <label><input type="checkbox" checked> Documentos</label>
                    <label><input type="checkbox"> Clientes</label>
                </div>
                <div class="search-results">
                    <p class="no-results">Ingresa términos de búsqueda</p>
                </div>
            </div>
        `);

        // Cloud content
        this.contentProviders.set('cloud', () => `
            <div class="cloud-content">
                <div class="cloud-status">
                    <div class="status-indicator offline">
                        <span class="status-dot"></span>
                        <span>Sin conexión</span>
                    </div>
                </div>
                <div class="cloud-actions">
                    <button class="cloud-btn">
                        <span class="btn-icon">☁️</span>
                        <span>Conectar a la nube</span>
                    </button>
                    <button class="cloud-btn">
                        <span class="btn-icon">📤</span>
                        <span>Subir archivos</span>
                    </button>
                    <button class="cloud-btn">
                        <span class="btn-icon">🔄</span>
                        <span>Sincronizar</span>
                    </button>
                </div>
                <div class="cloud-storage">
                    <p class="storage-info">Almacenamiento: 0 MB / 5 GB</p>
                    <div class="storage-bar">
                        <div class="storage-used" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        `);

        // Modules content (for the modules button in activity bar)
        this.contentProviders.set('modules', () => `
            <div class="modules-content">
                <div class="modules-header">
                    <h4>Gestión de Módulos</h4>
                    <button class="module-refresh-btn" title="Actualizar módulos">🔄</button>
                </div>
                
                <div class="modules-section">
                    <div class="section-header">
                        <h5>Módulos Instalados</h5>
                    </div>
                    <div class="modules-list">
                        ${this.generateAvailableModules()}
                    </div>
                </div>
                
                <div class="modules-section">
                    <div class="section-header">
                        <h5>Explorar Repositorio</h5>
                        <button class="browse-repository-btn" title="Buscar módulos disponibles">🔍</button>
                    </div>
                    <div class="repository-modules-list" id="repository-modules-list">
                        <div class="repository-message">
                            <div class="module-icon-large">⏳</div>
                            <p>Cargando módulos disponibles...</p>
                        </div>
                    </div>
                </div>
                
                <div class="modules-info">
                    <p class="info-text">Los módulos proporcionan funcionalidades específicas para diferentes áreas de trabajo.</p>
                </div>
            </div>
        `);

        // Extensions content
        this.contentProviders.set('extensions', () => `
            <div class="extensions-content">
                <div class="extension-header">
                    <h4>Extensiones Instaladas</h4>
                    <button class="extension-btn" title="Buscar extensiones">🔍</button>
                </div>
                <div class="extension-list">
                    <div class="extension-item enabled">
                        <div class="extension-info">
                            <span class="extension-name">Theme Pack</span>
                            <span class="extension-version">v1.0.0</span>
                        </div>
                        <button class="extension-toggle">✓</button>
                    </div>
                    <div class="extension-item disabled">
                        <div class="extension-info">
                            <span class="extension-name">PDF Viewer</span>
                            <span class="extension-version">v2.1.0</span>
                        </div>
                        <button class="extension-toggle">○</button>
                    </div>
                </div>
            </div>
        `);

        // Terminal content for bottom panel
        this.contentProviders.set('terminal', () => `
            <div class="terminal-content">
                <div class="terminal-header">
                    <span class="terminal-title">Terminal</span>
                    <div class="terminal-actions">
                        <button class="terminal-btn" title="New Terminal">+</button>
                        <button class="terminal-btn" title="Clear">🗑️</button>
                    </div>
                </div>
                <div class="terminal-output">
                    <div class="terminal-line">
                        <span class="terminal-prompt">CoreDesk@v2.0.0:~$</span>
                        <span class="terminal-cursor">|</span>
                    </div>
                </div>
            </div>
        `);
    }

    /**
     * Generate module tree for explorer - Dynamic version
     */
    generateModuleTree() {
        // Get modules from dynamic system
        const dynamicManager = window.exclusiveModuleController?.dynamicModuleManager;
        const moduleRegistry = dynamicManager?.registry;
        
        let modules = [];
        
        if (dynamicManager && moduleRegistry) {
            // Get installed modules from dynamic system
            const installedModules = moduleRegistry.getInstalledModules();
            modules = installedModules.map(module => ({
                code: module.id,
                name: module.name,
                isActive: this.isModuleCurrentlyActive(module.id)
            }));
        }
        
        // Fallback if no modules found
        if (modules.length === 0) {
            modules = [
                { code: 'no-modules', name: 'No hay módulos instalados', isActive: false }
            ];
        }

        // Add option to view modules directory
        modules.push({
            code: 'view-modules-dir',
            name: '📁 Ver Directorio de Módulos',
            isActive: false,
            isSpecial: true
        });

        return modules.map(module => `
            <div class="module-tree-item ${module.isActive ? 'active' : ''}">
                <span class="tree-icon">${module.isActive ? '📂' : '📁'}</span>
                <span class="tree-label">${module.name}</span>
                ${module.isActive ? '<span class="active-indicator">●</span>' : ''}
            </div>
        `).join('');
    }

    /**
     * Generate available modules for modules panel - Dynamic version
     */
    generateAvailableModules() {
        // Get modules from dynamic system
        const dynamicManager = window.exclusiveModuleController?.dynamicModuleManager;
        const moduleRegistry = dynamicManager?.registry;
        
        let modules = [];
        
        if (dynamicManager && moduleRegistry) {
            // Get all installed modules
            const allModules = moduleRegistry.getInstalledModules();
            modules = allModules.map(module => ({
                code: module.id,
                name: module.name,
                description: module.description,
                available: moduleRegistry.isModuleRegistered(module.id),
                isActive: this.isModuleCurrentlyActive(module.id)
            }));
        }
        
        // Fallback message if no modules found
        if (modules.length === 0) {
            return `
                <div class="no-modules-message">
                    <div class="module-icon-large">📦</div>
                    <h5>No hay módulos disponibles</h5>
                    <p>Los módulos se cargarán automáticamente cuando estén disponibles.</p>
                </div>
            `;
        }

        return modules.map(module => `
            <div class="available-module-item ${module.available ? 'available' : 'unavailable'} ${module.isActive ? 'active' : ''}">
                <div class="module-icon-large">
                    ${this.getModuleIcon(module.code)}
                </div>
                <div class="module-details">
                    <h5 class="module-name">${module.name}</h5>
                    <p class="module-description">${module.description}</p>
                    <div class="module-actions">
                        ${module.available ? 
                            (module.isActive ? 
                                `<button class="deactivate-module-btn" data-module="${module.code}">Desactivar</button>` :
                                `<button class="activate-module-btn" data-module="${module.code}">Activar</button>`
                            ) :
                            `<span class="coming-soon">No instalado</span>`
                        }
                    </div>
                </div>
            </div>
        `).join('');
    }

    /**
     * Get module icon - Dynamic version
     */
    getModuleIcon(moduleCode) {
        // Try to get icon from module registry first
        const dynamicManager = window.exclusiveModuleController?.dynamicModuleManager;
        const moduleRegistry = dynamicManager?.registry;
        if (moduleRegistry) {
            const moduleInfo = moduleRegistry.getModuleMetadata(moduleCode);
            if (moduleInfo && moduleInfo.icon) {
                return moduleInfo.icon;
            }
        }
        
        // Fallback to predefined icons
        const icons = {
            lexflow: '📄',
            protocolx: '📋',
            auditpro: '📊',
            finsync: '💰'
        };
        return icons[moduleCode] || '📦';
    }

    /**
     * Toggle panel visibility
     */
    togglePanel(panelType) {
        if (!this.panels[panelType]) return;

        const isCurrentlyOpen = this.panels[panelType].isOpen;
        
        if (isCurrentlyOpen) {
            this.closePanel(panelType);
        } else {
            this.openPanel(panelType);
        }
    }

    /**
     * Open specific panel
     */
    openPanel(panelType, content = null) {
        if (!this.panels[panelType]) return;

        const panel = this.panels[panelType];
        panel.isOpen = true;
        
        if (content) {
            panel.currentContent = content;
        }

        // Update DOM
        if (panel.element) {
            panel.element.classList.remove('hidden');
            this.updatePanelContent(panelType);
            
            // Immediately set the correct width for this content type
            if (window.panelResizer) {
                window.panelResizer.loadPanelSizeForContent(panelType, panel.currentContent);
            }
        }

        // Update toggle buttons
        this.updateToggleButton(panelType, true);

        // Emit event
        window.dispatchEvent(new CustomEvent('panel:toggled', {
            detail: { panel: panelType, isOpen: true }
        }));

        console.log('UI', `[PanelManager] Opened ${panelType} panel`, );
    }

    /**
     * Close specific panel
     */
    closePanel(panelType) {
        if (!this.panels[panelType]) return;

        const panel = this.panels[panelType];
        panel.isOpen = false;

        // Update DOM
        if (panel.element) {
            panel.element.classList.add('hidden');
        }

        // Update toggle buttons
        this.updateToggleButton(panelType, false);

        // Emit event
        window.dispatchEvent(new CustomEvent('panel:toggled', {
            detail: { panel: panelType, isOpen: false }
        }));

        console.log('UI', `[PanelManager] Closed ${panelType} panel`, );
    }

    /**
     * Handle activity bar button clicks (legacy method, now handled by ActivityBar.js)
     */
    handleActivityBarClick(contentType) {
        const leftPanel = this.panels.left;
        
        // If left panel is closed, open it with the selected content
        if (!leftPanel.isOpen) {
            this.setPanelContent('left', contentType);
            this.openPanel('left');
            return;
        }

        // If left panel is open and showing the same content, close it
        if (leftPanel.currentContent === contentType) {
            this.closePanel('left');
            return;
        }

        // If left panel is open but showing different content, switch content
        this.setPanelContent('left', contentType);
    }

    /**
     * Handle module activation from modules panel
     */
    handleModuleActivation(moduleCode) {
        console.log('UI', `[PanelManager] Activating module: ${moduleCode}`, );
        
        // Try to activate module via exclusive module controller
        if (window.exclusiveModuleController) {
            window.exclusiveModuleController.switchToModule(moduleCode, {
                confirmationRequired: false,
                cleanupPreviousTabs: true
            }).then(() => {
                // Refresh panel content to show updated state
                this.refreshModulesPanel();
            });
        } else {
            console.warn('[PanelManager] ExclusiveModuleController not available');
        }
    }

    /**
     * Handle module deactivation from modules panel
     */
    handleModuleDeactivation(moduleCode) {
        console.log('UI', `[PanelManager] Deactivating module: ${moduleCode}`, );
        
        // Try to deactivate module via exclusive module controller
        if (window.exclusiveModuleController) {
            // Deactivate the current module (switch to no module)
            window.exclusiveModuleController.deactivateModule(moduleCode).then(() => {
                // Clear current module
                window.exclusiveModuleController.currentModule = null;
                localStorage.removeItem(window.exclusiveModuleController.storageKey);
                
                // Show welcome screen
                const welcomeScreen = document.getElementById('welcome-screen');
                if (welcomeScreen) {
                    welcomeScreen.classList.remove('hidden');
                }
                
                // Close all tabs
                if (window.simplifiedTabSystem) {
                    window.simplifiedTabSystem.closeAllTabs();
                }
                
                // Refresh panel content to show updated state
                this.refreshModulesPanel();
                
                console.log('UI', `[PanelManager] Module ${moduleCode} deactivated successfully`);
            }).catch(error => {
                console.error('UI', `[PanelManager] Error deactivating module ${moduleCode}:`, error);
            });
        } else {
            console.warn('[PanelManager] ExclusiveModuleController not available');
        }
    }

    /**
     * Get module repository URL (copied from app.js)
     */
    getModuleRepositoryUrl() {
        // Check if there's a configured URL in CoreDeskAuth
        if (window.CoreDeskAuth?.api?.moduleRepositoryUrl) {
            return window.CoreDeskAuth.api.moduleRepositoryUrl;
        }
        
        // For now, always use production URL since local development server may not be running
        // Users can set window.CoreDeskAuth.api.moduleRepositoryUrl if they have a local setup
        const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
        const isElectron = navigator.userAgent.toLowerCase().indexOf(' electron/') > -1;
        
        if (isLocalhost && !isElectron) {
            // Browser on localhost - use localhost portal
            return 'http://localhost:3000/modules/api/search';
        } else {
            // Production URL for all other cases (including Electron)
            return 'https://coredeskpro.com/modules/api/search';
        }
    }

    /**
     * Handle browse repository button click
     */
    async handleBrowseRepository() {
        console.log('UI', '[PanelManager] Browsing repository for available modules...');
        
        const repositoryContainer = document.getElementById('repository-modules-list');
        if (!repositoryContainer) return;
        
        try {
            // Show loading state
            repositoryContainer.innerHTML = `
                <div class="repository-message">
                    <div class="module-icon-large">⏳</div>
                    <p>Buscando módulos disponibles...</p>
                </div>
            `;
            
            // Fetch available modules from portal using the same URL logic as main app
            const moduleUrl = this.getModuleRepositoryUrl();
            console.log('UI', `[PanelManager] Fetching modules from: ${moduleUrl}`);
            
            const response = await fetch(moduleUrl);
            const data = await response.json();
            
            if (!data.success) {
                throw new Error(data.message || 'Failed to fetch modules');
            }
            
            const availableModules = data.modules;
            
            if (availableModules.length === 0) {
                repositoryContainer.innerHTML = `
                    <div class="repository-message">
                        <div class="module-icon-large">📦</div>
                        <p>No se encontraron módulos disponibles en el repositorio.</p>
                    </div>
                `;
                return;
            }
            
            // Filter out already installed modules
            const installedModules = this.getInstalledModuleIds();
            const availableForInstall = availableModules.filter(module => 
                !installedModules.includes(module.id)
            );
            
            if (availableForInstall.length === 0) {
                repositoryContainer.innerHTML = `
                    <div class="repository-message">
                        <div class="module-icon-large">✅</div>
                        <p>Todos los módulos disponibles ya están instalados.</p>
                    </div>
                `;
                return;
            }
            
            // Generate repository module list
            repositoryContainer.innerHTML = this.generateRepositoryModules(availableForInstall);
            
        } catch (error) {
            console.error('UI', '[PanelManager] Error fetching repository modules:', error);
            repositoryContainer.innerHTML = `
                <div class="repository-message">
                    <div class="module-icon-large">❌</div>
                    <p>Error al conectar con el repositorio. Verifica tu conexión a internet.</p>
                </div>
            `;
        }
    }

    /**
     * Handle module installation
     */
    async handleModuleInstallation(moduleId) {
        console.log('UI', `[PanelManager] Installing module: ${moduleId}`);
        
        const installBtn = document.querySelector(`[data-module="${moduleId}"].install-module-btn`);
        if (!installBtn) return;
        
        try {
            // Show installing state
            installBtn.disabled = true;
            installBtn.classList.add('installing-btn');
            installBtn.innerHTML = '<span class="loading-indicator"></span>Instalando...';
            
            // Download module from portal
            const downloadResponse = await fetch(`http://localhost:3000/modules/${moduleId}/download`);
            
            if (!downloadResponse.ok) {
                throw new Error(`Download failed: ${downloadResponse.status} ${downloadResponse.statusText}`);
            }
            
            // Get module info
            const infoResponse = await fetch(`http://localhost:3000/modules/${moduleId}/info`);
            const moduleInfo = await infoResponse.json();
            
            if (!moduleInfo.success) {
                throw new Error(moduleInfo.message || 'Failed to get module info');
            }
            
            // Create module package structure
            const modulePackage = {
                manifest: moduleInfo.module.manifest,
                moduleCode: await downloadResponse.text(), // This would be the actual module file
                downloadedAt: new Date().toISOString()
            };
            
            // Install via dynamic module manager
            const dynamicManager = window.exclusiveModuleController?.dynamicModuleManager;
            if (dynamicManager) {
                await dynamicManager.installModulePackage(modulePackage);
                
                // Show success state
                installBtn.innerHTML = '✅ Instalado';
                installBtn.classList.remove('installing-btn');
                installBtn.classList.add('installed-btn');
                
                // Refresh modules panel to show new module
                setTimeout(() => {
                    this.refreshModulesPanel();
                    this.handleBrowseRepository(); // Refresh repository list
                }, 1000);
                
                console.log('UI', `[PanelManager] Module ${moduleId} installed successfully`);
            } else {
                throw new Error('Dynamic Module Manager not available');
            }
            
        } catch (error) {
            console.error('UI', `[PanelManager] Error installing module ${moduleId}:`, error);
            
            // Show error state
            installBtn.disabled = false;
            installBtn.classList.remove('installing-btn');
            installBtn.innerHTML = 'Error - Reintentar';
            installBtn.style.background = 'var(--error)';
            
            // Reset button after 3 seconds
            setTimeout(() => {
                installBtn.innerHTML = 'Instalar';
                installBtn.style.background = '';
            }, 3000);
        }
    }

    /**
     * Get list of installed module IDs
     */
    getInstalledModuleIds() {
        const dynamicManager = window.exclusiveModuleController?.dynamicModuleManager;
        const moduleRegistry = dynamicManager?.registry;
        
        if (moduleRegistry) {
            return moduleRegistry.getInstalledModules().map(module => module.id);
        }
        return [];
    }

    /**
     * Generate HTML for repository modules (compact version for panel)
     */
    generateRepositoryModules(modules) {
        return modules.map(module => `
            <div class="repository-module-item compact">
                <div class="module-icon-small">
                    ${this.getModuleIcon(module.id)}
                </div>
                <div class="module-details">
                    <div class="module-header">
                        <h6 class="module-name">${module.name}</h6>
                        <span class="module-version">v${module.latestVersion || module.version || '1.0.0'}</span>
                    </div>
                    <p class="module-description">${this.truncateText(module.description || 'No hay descripción disponible.', 60)}</p>
                    <div class="module-actions">
                        <button class="install-module-btn compact" data-module="${module.id}">Instalar</button>
                    </div>
                </div>
            </div>
        `).join('');
    }

    /**
     * Truncate text to specified length with ellipsis
     */
    truncateText(text, maxLength) {
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength).trim() + '...';
    }

    /**
     * Update panel content
     */
    updatePanelContent(panelType) {
        const panel = this.panels[panelType];
        if (!panel || !panel.element) return;

        const contentElement = panel.element.querySelector('.panel-content');
        const titleElement = panel.element.querySelector('#' + panelType + '-panel-title');
        
        if (!contentElement) return;

        // Get content from provider
        const contentProvider = this.contentProviders.get(panel.currentContent);
        if (contentProvider) {
            contentElement.innerHTML = contentProvider();
            
            // Auto-load repository modules when modules panel is displayed
            if (panel.currentContent === 'modules') {
                setTimeout(() => {
                    this.handleBrowseRepository();
                }, 200);
            }
            
            // Update title
            if (titleElement) {
                const titles = {
                    explorer: 'Explorer',
                    search: 'Buscar',
                    cloud: 'Nube',
                    modules: 'Módulos',
                    extensions: 'Extensiones',
                    terminal: 'Terminal'
                };
                titleElement.textContent = titles[panel.currentContent] || panel.currentContent;
            }
        }
    }

    /**
     * Update toggle button state
     */
    updateToggleButton(panelType, isOpen) {
        const toggleButtons = {
            left: document.getElementById('toggle-left-panel'),
            right: document.getElementById('toggle-right-panel'),
            bottom: document.getElementById('toggle-bottom-panel')
        };

        const button = toggleButtons[panelType];
        if (button) {
            const icon = button.querySelector('.panel-icon');
            if (icon && panelType === 'left') {
                icon.src = isOpen ? 'assets/icons/leftPanel-on-w.svg' : 'assets/icons/leftPanel-off-w.svg';
            } else if (icon && panelType === 'right') {
                icon.src = isOpen ? 'assets/icons/rightPanel-on-w.svg' : 'assets/icons/rightPanel-off-w.svg';
            } else if (icon && panelType === 'bottom') {
                icon.src = isOpen ? 'assets/icons/bottomPanel-on-w.svg' : 'assets/icons/bottomPanel-off-w.svg';
            }
        }
    }

    /**
     * Update all panel states on initialization
     */
    updatePanelStates() {
        Object.keys(this.panels).forEach(panelType => {
            const panel = this.panels[panelType];
            if (panel.isOpen) {
                this.openPanel(panelType);
            } else {
                this.closePanel(panelType);
            }
        });
    }

    /**
     * Get panel status
     */
    getStatus() {
        return {
            left: this.panels.left.isOpen,
            right: this.panels.right.isOpen,
            bottom: this.panels.bottom.isOpen,
            initialized: true
        };
    }

    /**
     * Set panel content
     */
    setPanelContent(panelType, contentType) {
        if (!this.panels[panelType]) return;
        
        // Save current panel size before switching content
        if (window.panelResizer && this.panels[panelType].isOpen) {
            window.panelResizer.savePanelSizes();
        }
        
        this.panels[panelType].currentContent = contentType;
        if (this.panels[panelType].isOpen) {
            this.updatePanelContent(panelType);
            
            // Load size for new content type
            if (window.panelResizer) {
                // Small delay to ensure DOM is updated
                setTimeout(() => {
                    window.panelResizer.loadPanelSizeForContent(panelType, contentType);
                }, 50);
            }
        }
    }

    /**
     * Refresh modules panel to show updated state
     */
    refreshModulesPanel() {
        // Refresh left panel if it's showing modules
        if (this.panels.left.isOpen && this.panels.left.currentContent === 'modules') {
            this.updatePanelContent('left');
            
            // Auto-load repository modules after a short delay to let the panel render
            setTimeout(() => {
                this.handleBrowseRepository();
            }, 200);
        }
        
        // Refresh left panel if it's showing explorer (which shows active modules)
        if (this.panels.left.isOpen && this.panels.left.currentContent === 'explorer') {
            this.updatePanelContent('left');
        }
        
        // Also update status bar
        this.updateStatusBar();
    }

    /**
     * Update status bar to reflect current module
     */
    updateStatusBar() {
        const statusElement = document.getElementById('current-module');
        if (statusElement) {
            const textElement = statusElement.querySelector('.status-text');
            if (textElement) {
                if (window.exclusiveModuleController?.currentModule) {
                    const moduleInfo = window.exclusiveModuleController.getModuleInfo(window.exclusiveModuleController.currentModule);
                    textElement.textContent = moduleInfo ? moduleInfo.name : window.exclusiveModuleController.currentModule;
                } else {
                    textElement.textContent = 'Sin módulo activo';
                }
            }
        }
    }

    /**
     * Check if a module is currently active (more reliable check)
     * @param {string} moduleId - Module identifier
     * @returns {boolean}
     */
    isModuleCurrentlyActive(moduleId) {
        // Check ExclusiveModuleController first (authoritative source)
        if (window.exclusiveModuleController?.currentModule === moduleId) {
            return true;
        }
        
        // Double-check with dynamic module manager
        const dynamicManager = window.exclusiveModuleController?.dynamicModuleManager;
        if (dynamicManager && dynamicManager.isModuleActive(moduleId)) {
            return true;
        }
        
        return false;
    }

    /**
     * Setup periodic refresh as backup mechanism for detecting module state changes
     * @private
     */
    setupPeriodicRefresh() {
        // Check for module state changes every 2 seconds as backup
        setInterval(() => {
            const currentActiveModule = window.exclusiveModuleController?.currentModule;
            
            // Only refresh if active module has changed since last check
            if (this.lastKnownActiveModule !== currentActiveModule) {
                console.log('UI', `[PanelManager] Module state change detected: ${this.lastKnownActiveModule} -> ${currentActiveModule}`);
                this.lastKnownActiveModule = currentActiveModule;
                this.refreshModulesPanel();
            }
        }, 2000);
    }

    /**
     * Setup file explorer event handlers
     * @private
     */
    setupFileExplorerHandlers() {
        document.addEventListener('click', (e) => {
            // Handle file/folder double-click
            if (e.target.closest('.file-item')) {
                const fileItem = e.target.closest('.file-item');
                const fileName = fileItem.dataset.path;
                const isDirectory = fileItem.classList.contains('directory');
                
                if (isDirectory && fileName !== '..') {
                    // Navigate to directory
                    const currentPath = window.fileExplorer?.currentPath || '';
                    const newPath = this.joinPath(currentPath, fileName);
                    this.navigateToPath(newPath);
                } else if (fileName === '..') {
                    // Go up one level
                    this.navigateUp();
                }
            }
            
            // Handle bookmark clicks
            if (e.target.closest('.bookmark-item')) {
                const bookmark = e.target.closest('.bookmark-item');
                const path = bookmark.dataset.path;
                if (path) {
                    this.navigateToPath(path);
                }
            }
            
            // Handle navigation buttons
            if (e.target.closest('.back-btn')) {
                this.navigateBack();
            } else if (e.target.closest('.forward-btn')) {
                this.navigateForward();
            } else if (e.target.closest('.up-btn')) {
                this.navigateUp();
            } else if (e.target.closest('.refresh-btn')) {
                this.refreshExplorer();
            } else if (e.target.closest('.go-btn')) {
                this.navigateToInputPath();
            }
            
            // Handle view toggle
            if (e.target.closest('.view-btn')) {
                this.toggleExplorerView(e.target.closest('.view-btn'));
            }
            
            // Handle sort order
            if (e.target.closest('.sort-order-btn')) {
                this.toggleSortOrder();
            }
        });
        
        // Handle path input enter key
        document.addEventListener('keydown', (e) => {
            if (e.target.classList.contains('path-input') && e.key === 'Enter') {
                this.navigateToInputPath();
            }
        });
        
        // Handle sort select change
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('sort-select')) {
                this.changeSortBy(e.target.value);
            }
            
            if (e.target.classList.contains('hidden-toggle') && e.target.type === 'checkbox') {
                this.toggleHiddenFiles(e.target.checked);
            }
        });
    }

    /**
     * Navigate to a specific path
     */
    navigateToPath(path) {
        if (window.fileExplorer) {
            window.fileExplorer.navigateTo(path);
        }
    }

    /**
     * Navigate up one directory level
     */
    navigateUp() {
        if (window.fileExplorer) {
            const currentPath = window.fileExplorer.currentPath;
            const parentPath = this.getParentPath(currentPath);
            if (parentPath !== currentPath) {
                window.fileExplorer.navigateTo(parentPath);
            }
        }
    }

    /**
     * Navigate back in history
     */
    navigateBack() {
        if (window.fileExplorer && window.fileExplorer.canGoBack()) {
            window.fileExplorer.historyIndex--;
            window.fileExplorer.currentPath = window.fileExplorer.history[window.fileExplorer.historyIndex];
            window.fileExplorer.refresh();
        }
    }

    /**
     * Navigate forward in history
     */
    navigateForward() {
        if (window.fileExplorer && window.fileExplorer.canGoForward()) {
            window.fileExplorer.historyIndex++;
            window.fileExplorer.currentPath = window.fileExplorer.history[window.fileExplorer.historyIndex];
            window.fileExplorer.refresh();
        }
    }

    /**
     * Navigate to path from input
     */
    navigateToInputPath() {
        const pathInput = document.querySelector('.path-input');
        if (pathInput && window.fileExplorer) {
            const path = pathInput.value.trim();
            if (path) {
                window.fileExplorer.navigateTo(path);
            }
        }
    }

    /**
     * Refresh the explorer
     */
    refreshExplorer() {
        if (window.fileExplorer) {
            window.fileExplorer.refresh();
        }
    }

    /**
     * Toggle explorer view (list/grid)
     */
    toggleExplorerView(button) {
        document.querySelectorAll('.view-btn').forEach(btn => btn.classList.remove('active'));
        button.classList.add('active');
        
        // Add view logic here
        const isGridView = button.classList.contains('grid-view');
        console.log('Toggling to', isGridView ? 'grid' : 'list', 'view');
    }

    /**
     * Toggle sort order
     */
    toggleSortOrder() {
        if (window.fileExplorer) {
            window.fileExplorer.sortOrder = window.fileExplorer.sortOrder === 'asc' ? 'desc' : 'asc';
            window.fileExplorer.refresh();
        }
    }

    /**
     * Change sort criteria
     */
    changeSortBy(sortBy) {
        if (window.fileExplorer) {
            window.fileExplorer.sortBy = sortBy;
            window.fileExplorer.refresh();
        }
    }

    /**
     * Toggle hidden files visibility
     */
    toggleHiddenFiles(show) {
        if (window.fileExplorer) {
            window.fileExplorer.showHidden = show;
            window.fileExplorer.refresh();
        }
    }

    /**
     * Helper method to join paths correctly
     */
    joinPath(basePath, fileName) {
        const isWindows = navigator.platform.indexOf('Win') !== -1;
        const separator = isWindows ? '\\' : '/';
        
        if (basePath.endsWith(separator)) {
            return basePath + fileName;
        } else {
            return basePath + separator + fileName;
        }
    }

    /**
     * Helper method to get parent path
     */
    getParentPath(path) {
        const isWindows = navigator.platform.indexOf('Win') !== -1;
        const separator = isWindows ? '\\' : '/';
        
        if (isWindows && path.length <= 3) {
            return path; // Root drive like C:\
        } else if (!isWindows && path === '/') {
            return path; // Unix root
        }
        
        const lastSeparator = path.lastIndexOf(separator);
        if (lastSeparator <= 0) {
            return isWindows ? path.charAt(0) + ':\\' : '/';
        }
        
        return path.substring(0, lastSeparator);
    }
}

// Create global instance
window.panelManager = new PanelManager();
window.panelManagerInitialized = true;

console.log('UI', '[PanelManager] Global instance created successfully', );
