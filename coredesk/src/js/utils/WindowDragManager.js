/**
 * Window Drag Functionality
 * Permite arrastrar la ventana de Electron desde áreas específicas
 */

class WindowDragManager {
    constructor() {
        this.isDragging = false;
        this.startX = 0;
        this.startY = 0;
        this.dragElements = [];
        
        this.init();
    }

    init() {
        // Esperar a que el DOM esté listo
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.setupDragElements();
            });
        } else {
            this.setupDragElements();
        }
    }

    setupDragElements() {
        // Elementos que pueden ser usados para arrastrar la ventana
        const dragSelectors = [
            '.auth-brand',
            '.brand-title', 
            '.brand-tagline',
            '[data-drag="window"]'
        ];

        dragSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                this.makeDraggable(element);
            });
        });

        // Agregar área de arrastre invisible en la parte superior
        this.createTopDragArea();

        console.log('[WindowDragManager] Drag functionality initialized');
    }

    makeDraggable(element) {
        if (!element) return;

        // Agregar clase para indicar que es arrastrable
        element.classList.add('window-draggable');
        
        // Eventos de mouse
        element.addEventListener('mousedown', this.handleMouseDown.bind(this));
        
        // Eventos de touch para dispositivos táctiles
        element.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: false });

        this.dragElements.push(element);
    }

    createTopDragArea() {
        const authContainer = document.querySelector('.auth-container');
        if (!authContainer) return;

        // Crear área de arrastre en la parte superior
        const topDragArea = document.createElement('div');
        topDragArea.className = 'window-drag-area';
        topDragArea.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 30px;
            z-index: 1000;
            cursor: move;
            background: linear-gradient(to bottom, rgba(139, 125, 216, 0.1) 0%, transparent 100%);
            pointer-events: auto;
        `;

        authContainer.appendChild(topDragArea);
        this.makeDraggable(topDragArea);
    }

    handleMouseDown(event) {
        // Prevenir arrastre si se hace click en elementos interactivos
        if (this.isInteractiveElement(event.target)) {
            return;
        }

        event.preventDefault();
        
        this.isDragging = true;
        this.startX = event.clientX;
        this.startY = event.clientY;

        // Agregar listeners globales
        document.addEventListener('mousemove', this.handleMouseMove.bind(this));
        document.addEventListener('mouseup', this.handleMouseUp.bind(this));

        // Cambiar cursor
        document.body.style.cursor = 'move';
        document.body.style.userSelect = 'none';
    }

    handleTouchStart(event) {
        if (this.isInteractiveElement(event.target)) {
            return;
        }

        event.preventDefault();
        
        const touch = event.touches[0];
        this.isDragging = true;
        this.startX = touch.clientX;
        this.startY = touch.clientY;

        document.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: false });
        document.addEventListener('touchend', this.handleTouchEnd.bind(this));
    }

    handleMouseMove(event) {
        if (!this.isDragging) return;

        event.preventDefault();
        
        const deltaX = event.clientX - this.startX;
        const deltaY = event.clientY - this.startY;

        this.moveWindow(deltaX, deltaY);

        this.startX = event.clientX;
        this.startY = event.clientY;
    }

    handleTouchMove(event) {
        if (!this.isDragging) return;

        event.preventDefault();
        
        const touch = event.touches[0];
        const deltaX = touch.clientX - this.startX;
        const deltaY = touch.clientY - this.startY;

        this.moveWindow(deltaX, deltaY);

        this.startX = touch.clientX;
        this.startY = touch.clientY;
    }

    handleMouseUp(event) {
        this.stopDragging();
    }

    handleTouchEnd(event) {
        this.stopDragging();
    }

    stopDragging() {
        this.isDragging = false;

        // Remover listeners globales
        document.removeEventListener('mousemove', this.handleMouseMove.bind(this));
        document.removeEventListener('mouseup', this.handleMouseUp.bind(this));
        document.removeEventListener('touchmove', this.handleTouchMove.bind(this));
        document.removeEventListener('touchend', this.handleTouchEnd.bind(this));

        // Restaurar cursor
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
    }

    async moveWindow(deltaX, deltaY) {
        // Usar la API de Electron si está disponible
        if (window.electronAPI?.window?.move) {
            try {
                await window.electronAPI.window.move(deltaX, deltaY);
            } catch (error) {
                console.warn('[WindowDragManager] Failed to move window via Electron API:', error);
            }
        } else {
            console.warn('[WindowDragManager] Electron API not available for window movement');
        }
    }

    isInteractiveElement(element) {
        // Lista de elementos que no deben activar el arrastre
        const interactiveSelectors = [
            'input', 'button', 'a', 'select', 'textarea',
            '.btn', '.form-input', '.auth-tab', '.checkbox-label input',
            '[contenteditable="true"]'
        ];

        // Verificar si el elemento o algún padre es interactivo
        let currentElement = element;
        while (currentElement && currentElement !== document.body) {
            for (const selector of interactiveSelectors) {
                if (currentElement.matches && currentElement.matches(selector)) {
                    return true;
                }
            }
            currentElement = currentElement.parentElement;
        }

        return false;
    }

    // Método público para agregar nuevos elementos arrastrables
    addDragElement(element) {
        if (element && !this.dragElements.includes(element)) {
            this.makeDraggable(element);
        }
    }

    // Método público para remover elementos arrastrables
    removeDragElement(element) {
        const index = this.dragElements.indexOf(element);
        if (index > -1) {
            this.dragElements.splice(index, 1);
            element.classList.remove('window-draggable');
            
            // Remover event listeners (requiere clone del elemento)
            const newElement = element.cloneNode(true);
            element.parentNode.replaceChild(newElement, element);
        }
    }

    // Método para limpiar
    destroy() {
        this.dragElements.forEach(element => {
            this.removeDragElement(element);
        });
        this.dragElements = [];
        
        // Remover área de arrastre superior
        const dragArea = document.querySelector('.window-drag-area');
        if (dragArea) {
            dragArea.remove();
        }
    }
}

// Inicializar el gestor de arrastre cuando esté disponible
let windowDragManager;

if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        windowDragManager = new WindowDragManager();
    });
} else {
    windowDragManager = new WindowDragManager();
}

// Exportar para uso global
window.WindowDragManager = WindowDragManager;
window.windowDragManager = windowDragManager;
