/**
 * GlobalLogger.js
 * Simple global logger fallback for all components
 */

// Create a simple global logger that all components can use
window.Logger = window.Logger || class SimpleLogger {
    
    // Helper method to safely stringify data for logging
    static safeStringify(data) {
        if (data === undefined) return '[undefined]';
        if (data === null) return '[null]';
        if (data === 'undefined') return '[string "undefined"]';
        if (data === 'null') return '[string "null"]';
        
        try {
            if (typeof data === 'object') {
                return JSON.stringify(data, null, 2);
            }
            return String(data);
        } catch (error) {
            return `[Object - serialization failed: ${error.message}]`;
        }
    }
    
    static info(component, message, data = null) {
        if (data !== null && data !== undefined) {
            console.log(`[${component}] ${message}`, this.safeStringify(data));
        } else {
            console.log(`[${component}] ${message}`);
        }
    }
    
    static error(component, message, error = null) {
        if (error !== null && error !== undefined) {
            if (error instanceof Error) {
                console.error(`[${component}] ${message}`, error);
            } else {
                console.error(`[${component}] ${message}`, this.safeStringify(error));
            }
        } else {
            console.error(`[${component}] ${message}`);
        }
    }
    
    static warn(component, message, data = null) {
        if (data !== null && data !== undefined) {
            console.warn(`[${component}] ${message}`, this.safeStringify(data));
        } else {
            console.warn(`[${component}] ${message}`);
        }
    }
    
    static debug(component, message, data = null) {
        if (data !== null && data !== undefined) {
            console.debug(`[${component}] ${message}`, this.safeStringify(data));
        } else {
            console.debug(`[${component}] ${message}`);
        }
    }
    
    // Instance methods for compatibility
    info(component, message, data = null) {
        return SimpleLogger.info(component, message, data);
    }
    
    error(component, message, error = null) {
        return SimpleLogger.error(component, message, error);
    }
    
    warn(component, message, data = null) {
        return SimpleLogger.warn(component, message, data);
    }
    
    debug(component, message, data = null) {
        return SimpleLogger.debug(component, message, data);
    }
};

// Create a global logger instance
window.logger = new window.Logger();

// Also create GlobalLogger reference for components that expect it
window.GlobalLogger = window.Logger;

console.log('[GlobalLogger] Global logger initialized successfully with safe undefined/null handling');
