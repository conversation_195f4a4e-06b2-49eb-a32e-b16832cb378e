/**
 * Utility script to clean up corrupted localStorage data
 * This can be executed manually in the browser console if needed
 */

function cleanupCorruptedLocalStorage() {
    console.log('🧹 Iniciando limpieza de localStorage...');
    
    // Define protected keys that should NEVER be deleted
    const protectedKeys = [
        'coredesk_token',
        'coredesk_token_expiry',
        'coredesk_refresh_token',
        'coredesk_user',
        'coredesk_user_data',
        'coredesk_auth',
        'coredesk_license',
        'coredesk_activation',
        'coredesk_trial',
        'coredesk_trialFlow',
        'coredesk_device',
        'coredesk_fingerprint',
        'coredesk_config',
        'coredesk_settings',
        'coredesk_theme',
        'coredesk_language',
        'coredesk_session'
    ];
    
    let cleanedCount = 0;
    let totalCount = 0;
    let protectedCount = 0;
    
    // Get all localStorage keys
    const allKeys = Object.keys(localStorage);
    
    console.log(`📊 Total de claves en localStorage: ${allKeys.length}`);
    console.log(`🛡️ Claves protegidas (autenticación/configuración):`, protectedKeys.length);
    
    // Check each key for corruption
    for (const key of allKeys) {
        totalCount++;
        
        // Skip protected keys
        if (protectedKeys.includes(key)) {
            protectedCount++;
            console.log(`🛡️ Protegida: ${key}`);
            continue;
        }
        
        try {
            const value = localStorage.getItem(key);
            
            // Check for obviously corrupted values
            if (!value || 
                value === 'undefined' || 
                value === 'null' ||
                value.trim() === '') {
                
                console.warn(`🗑️ Eliminando clave corrupta: ${key} (valor vacío o indefinido)`);
                localStorage.removeItem(key);
                cleanedCount++;
                continue;
            }
            
            // If it's a CoreDesk key, do additional validation
            if (key.startsWith('coredesk_')) {
                try {
                    const parsed = JSON.parse(value);
                    
                    // Check for specific corruption patterns
                    if (key.includes('module_') && parsed.manifestData) {
                        if (parsed.manifestData === 'undefined' ||
                            parsed.manifestData === 'null' ||
                            (typeof parsed.manifestData === 'string' && parsed.manifestData.trim() === '')) {
                            
                            console.warn(`🗑️ Eliminando módulo corrupto: ${key}`);
                            localStorage.removeItem(key);
                            cleanedCount++;
                            continue;
                        }
                    }
                    
                    // Check registry entries
                    if (key.includes('registry') && parsed.metadata) {
                        let hasCorruption = false;
                        
                        for (const [moduleId, metadata] of Object.entries(parsed.metadata)) {
                            if (!metadata || 
                                metadata === 'undefined' ||
                                metadata === 'null' ||
                                !metadata.id ||
                                metadata.id === 'undefined' ||
                                metadata.id === 'null') {
                                
                                console.warn(`🗑️ Encontrada corrupción en registry para módulo: ${moduleId}`);
                                hasCorruption = true;
                                break;
                            }
                        }
                        
                        if (hasCorruption) {
                            console.warn(`🗑️ Eliminando registry corrupto: ${key}`);
                            localStorage.removeItem(key);
                            cleanedCount++;
                            continue;
                        }
                    }
                    
                } catch (parseError) {
                    console.warn(`🗑️ Eliminando clave con JSON inválido: ${key}`);
                    localStorage.removeItem(key);
                    cleanedCount++;
                    continue;
                }
            }
            
        } catch (error) {
            console.error(`❌ Error procesando clave ${key}:`, error);
            // If there's an error accessing the key, it might be corrupted
            try {
                localStorage.removeItem(key);
                cleanedCount++;
                console.warn(`🗑️ Eliminando clave problemática: ${key}`);
            } catch (removeError) {
                console.error(`❌ No se pudo eliminar clave corrupta ${key}:`, removeError);
            }
        }
    }
    
    console.log(`✅ Limpieza completada:`);
    console.log(`   📋 Total de claves procesadas: ${totalCount}`);
    console.log(`   🛡️ Claves protegidas (no tocadas): ${protectedCount}`);
    console.log(`   🗑️ Claves eliminadas: ${cleanedCount}`);
    console.log(`   ✨ Claves restantes: ${localStorage.length}`);
    
    if (cleanedCount > 0) {
        console.log(`🔄 Se recomienda recargar la aplicación para aplicar los cambios.`);
    }
    
    return {
        processed: totalCount,
        protected: protectedCount,
        cleaned: cleanedCount,
        remaining: localStorage.length
    };
}

// Función específica para limpiar datos de módulos específicos
function cleanupSpecificModule(moduleId) {
    console.log(`🧹 Limpiando datos específicos del módulo: ${moduleId}`);
    
    // Define protected keys that should NEVER be deleted
    const protectedKeys = [
        'coredesk_token',
        'coredesk_token_expiry',
        'coredesk_refresh_token',
        'coredesk_user',
        'coredesk_user_data',
        'coredesk_auth',
        'coredesk_license',
        'coredesk_activation',
        'coredesk_trial',
        'coredesk_trialFlow',
        'coredesk_device',
        'coredesk_fingerprint',
        'coredesk_config',
        'coredesk_settings',
        'coredesk_theme',
        'coredesk_language',
        'coredesk_session'
    ];
    
    const keysToCheck = [
        `coredesk_module_${moduleId}`,
        `coredesk_registry_module_${moduleId}`,
        `coredesk_metadata_${moduleId}`
    ];
    
    let cleanedCount = 0;
    
    for (const key of keysToCheck) {
        // NEVER delete protected keys
        if (protectedKeys.includes(key)) {
            console.log(`🛡️ Saltando clave protegida: ${key}`);
            continue;
        }
        
        if (localStorage.getItem(key)) {
            localStorage.removeItem(key);
            console.log(`🗑️ Eliminado: ${key}`);
            cleanedCount++;
        }
    }
    
    // Also clean from registry data (but check it's not a protected key first)
    const registryKey = 'coredesk_registry_registry';
    
    if (!protectedKeys.includes(registryKey)) {
        const registryData = localStorage.getItem(registryKey);
        if (registryData) {
            try {
                const parsed = JSON.parse(registryData);
                if (parsed.metadata && parsed.metadata[moduleId]) {
                    delete parsed.metadata[moduleId];
                    localStorage.setItem(registryKey, JSON.stringify(parsed));
                    console.log(`🗑️ Eliminado ${moduleId} del registry`);
                    cleanedCount++;
                }
            } catch (error) {
                console.warn(`⚠️ Error limpiando registry:`, error);
                // Only remove if not protected
                if (!protectedKeys.includes(registryKey)) {
                    localStorage.removeItem(registryKey);
                    console.log(`🗑️ Eliminado registry corrupto`);
                    cleanedCount++;
                }
            }
        }
    } else {
        console.log(`🛡️ Registry protegido, no se modifica`);
    }
    
    console.log(`✅ Limpieza del módulo ${moduleId} completada. ${cleanedCount} elementos eliminados.`);
    console.log(`🛡️ Datos de autenticación y configuración preservados`);
    return cleanedCount;
}

// Función para diagnóstico
function diagnoseCoreDesklocalStorage() {
    console.log('🔍 Diagnóstico de localStorage de CoreDesk...');
    
    // Define protected keys that should NEVER be deleted
    const protectedKeys = [
        'coredesk_token',
        'coredesk_token_expiry',
        'coredesk_refresh_token',
        'coredesk_user',
        'coredesk_user_data',
        'coredesk_auth',
        'coredesk_license',
        'coredesk_activation',
        'coredesk_trial',
        'coredesk_trialFlow',
        'coredesk_device',
        'coredesk_fingerprint',
        'coredesk_config',
        'coredesk_settings',
        'coredesk_theme',
        'coredesk_language',
        'coredesk_session'
    ];
    
    const allKeys = Object.keys(localStorage);
    const coredeskKeys = allKeys.filter(key => key.startsWith('coredesk_'));
    const protectedKeysFound = coredeskKeys.filter(key => protectedKeys.includes(key));
    
    console.log(`📊 Total de claves: ${allKeys.length}`);
    console.log(`📊 Claves de CoreDesk: ${coredeskKeys.length}`);
    console.log(`🛡️ Claves protegidas encontradas: ${protectedKeysFound.length}`);
    
    const analysis = {
        total: allKeys.length,
        coredesk: coredeskKeys.length,
        protected: protectedKeysFound,
        modules: [],
        registries: [],
        corrupted: [],
        valid: []
    };
    
    console.log('🛡️ Claves protegidas presentes:');
    protectedKeysFound.forEach(key => {
        console.log(`   ✅ ${key}`);
    });
    
    for (const key of coredeskKeys) {
        // Skip detailed analysis of protected keys - just mark as valid
        if (protectedKeys.includes(key)) {
            analysis.valid.push(key);
            continue;
        }
        
        try {
            const value = localStorage.getItem(key);
            
            if (!value || value === 'undefined' || value === 'null' || value.trim() === '') {
                analysis.corrupted.push({ key, reason: 'valor vacío o indefinido' });
                continue;
            }
            
            const parsed = JSON.parse(value);
            
            if (key.includes('module_')) {
                analysis.modules.push(key);
                
                if (!parsed.manifestData || 
                    parsed.manifestData === 'undefined' ||
                    parsed.manifestData === 'null') {
                    analysis.corrupted.push({ key, reason: 'manifestData corrupto' });
                } else {
                    analysis.valid.push(key);
                }
            } else if (key.includes('registry')) {
                analysis.registries.push(key);
                
                if (parsed.metadata) {
                    for (const [moduleId, metadata] of Object.entries(parsed.metadata)) {
                        if (!metadata || !metadata.id) {
                            analysis.corrupted.push({ key, reason: `metadata corrupto para ${moduleId}` });
                            break;
                        }
                    }
                    if (!analysis.corrupted.find(c => c.key === key)) {
                        analysis.valid.push(key);
                    }
                } else {
                    analysis.valid.push(key);
                }
            } else {
                analysis.valid.push(key);
            }
            
        } catch (error) {
            analysis.corrupted.push({ key, reason: `JSON inválido: ${error.message}` });
        }
    }
    
    console.log('📋 Análisis completo:');
    console.log('   �️ Protegidas:', analysis.protected.length);
    console.log('   �📦 Módulos:', analysis.modules.length);
    console.log('   📚 Registries:', analysis.registries.length);
    console.log('   ✅ Válidas:', analysis.valid.length);
    console.log('   ❌ Corruptas:', analysis.corrupted.length);
    
    if (analysis.corrupted.length > 0) {
        console.log('🚨 Claves corruptas encontradas:');
        analysis.corrupted.forEach(item => {
            console.log(`   - ${item.key}: ${item.reason}`);
        });
    }
    
    console.log('⚠️ IMPORTANTE: Las claves protegidas (auth, config) NUNCA se eliminarán automáticamente');
    
    return analysis;
}

// Make functions available globally for console use
window.CoreDeskCleanup = {
    cleanupCorruptedLocalStorage,
    cleanupSpecificModule,
    diagnoseCoreDesklocalStorage
};

console.log('🛠️ Utilidades de limpieza de CoreDesk cargadas.');
console.log('�️ PROTECCIÓN DE DATOS DE AUTENTICACIÓN ACTIVADA');
console.log('�💡 Uso seguro:');
console.log('   - CoreDeskCleanup.diagnoseCoreDesklocalStorage() - Diagnóstico completo');
console.log('   - CoreDeskCleanup.cleanupCorruptedLocalStorage() - Limpieza segura (preserva auth)');
console.log('   - CoreDeskCleanup.cleanupSpecificModule("lexflow") - Limpiar módulo específico');
console.log('⚠️ IMPORTANTE: Los tokens de autenticación y configuración están protegidos automáticamente');
