/**
 * AssetPathResolver.js
 * Utility for resolving asset paths in both development and packaged Electron apps
 */

class AssetPathResolver {
    static getAssetPath(relativePath) {
        // Remove leading ./ if present
        const cleanPath = relativePath.replace(/^\.\//, '');
        
        // In Electron apps (both development and production), 
        // relative paths work correctly because Electron can serve from ASAR
        return `./${cleanPath}`;
    }
    
    static getIconPath(iconName) {
        return AssetPathResolver.getAssetPath(`assets/icons/${iconName}`);
    }
    
    static getImagePath(imageName) {
        return AssetPathResolver.getAssetPath(`assets/images/${imageName}`);
    }
    
    static async loadAsset(assetPath) {
        try {
            const fullPath = AssetPathResolver.getAssetPath(assetPath);
            const response = await fetch(fullPath);
            if (!response.ok) {
                throw new Error(`Failed to load asset: ${fullPath} (${response.status})`);
            }
            return response;
        } catch (error) {
            console.error('[AssetPathResolver] Error loading asset:', assetPath, error);
            throw error;
        }
    }
    
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AssetPathResolver;
}

// Make available globally in the browser
if (typeof window !== 'undefined') {
    window.AssetPathResolver = AssetPathResolver;
}