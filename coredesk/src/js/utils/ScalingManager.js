/**
 * Scaling Detection and Auto-Adjustment
 * Automatically detects display characteristics and applies optimal scaling
 */

class ScalingManager {
    constructor() {
        this.platform = null;
        this.devicePixelRatio = window.devicePixelRatio || 1;
        this.screenWidth = window.screen.width;
        this.screenHeight = window.screen.height;
        this.viewportWidth = window.innerWidth;
        this.viewportHeight = window.innerHeight;
        this.isElectron = !!(window.electronAPI || window.coreDesk);
        this.scalingInfo = null;
        
        this.init();
    }

    async init() {
        console.log('[ScalingManager] Initializing scaling detection...');
        
        // Detect platform
        await this.detectPlatform();
        
        // Calculate optimal scaling
        this.calculateOptimalScaling();
        
        // Apply scaling adjustments
        this.applyScalingAdjustments();
        
        // Setup resize listener
        this.setupResizeListener();
        
        // Log scaling information
        this.logScalingInfo();
    }

    async detectPlatform() {
        if (this.isElectron && window.electronAPI?.system?.getInfo) {
            try {
                const systemInfo = await window.electronAPI.system.getInfo();
                this.platform = systemInfo.platform;
                console.log('[ScalingManager] Platform detected via Electron:', this.platform);
            } catch (error) {
                console.warn('[ScalingManager] Failed to get system info:', error);
                this.detectPlatformFallback();
            }
        } else {
            this.detectPlatformFallback();
        }
    }

    detectPlatformFallback() {
        const userAgent = navigator.userAgent.toLowerCase();
        if (userAgent.includes('win')) {
            this.platform = 'win32';
        } else if (userAgent.includes('mac')) {
            this.platform = 'darwin';
        } else if (userAgent.includes('linux')) {
            this.platform = 'linux';
        } else {
            this.platform = 'unknown';
        }
        console.log('[ScalingManager] Platform detected via UserAgent:', this.platform);
    }

    calculateOptimalScaling() {
        const dpr = this.devicePixelRatio;
        const screenArea = this.screenWidth * this.screenHeight;
        
        let optimalScale = 1.0;
        let fontScale = 1.0;
        let spacingScale = 1.0;
        
        // Base scaling on device pixel ratio and platform
        if (this.platform === 'win32') {
            // Windows scaling logic
            if (dpr >= 3.0) {
                optimalScale = 0.7;
                fontScale = 1.1;
                spacingScale = 1.1;
            } else if (dpr >= 2.0) {
                optimalScale = 0.8;
                fontScale = 1.05;
                spacingScale = 1.05;
            } else if (dpr >= 1.5) {
                optimalScale = 0.9;
                fontScale = 1.02;
                spacingScale = 1.02;
            } else if (dpr >= 1.25) {
                optimalScale = 0.95;
                fontScale = 1.01;
                spacingScale = 1.01;
            }
        } else if (this.platform === 'linux') {
            // Linux/WSL scaling logic (more aggressive)
            if (dpr >= 3.0) {
                optimalScale = 0.65;
                fontScale = 1.15;
                spacingScale = 1.15;
            } else if (dpr >= 2.0) {
                optimalScale = 0.75;
                fontScale = 1.1;
                spacingScale = 1.1;
            } else if (dpr >= 1.5) {
                optimalScale = 0.85;
                fontScale = 1.05;
                spacingScale = 1.05;
            } else if (dpr >= 1.25) {
                optimalScale = 0.9;
                fontScale = 1.02;
                spacingScale = 1.02;
            }
        } else {
            // macOS or other platforms
            if (dpr >= 2.0) {
                optimalScale = 0.85;
                fontScale = 1.05;
                spacingScale = 1.05;
            } else if (dpr >= 1.5) {
                optimalScale = 0.92;
                fontScale = 1.02;
                spacingScale = 1.02;
            }
        }
        
        // Adjust for screen size
        if (screenArea < 1366 * 768) {
            // Small screens - make things slightly smaller
            optimalScale *= 0.95;
            fontScale *= 0.98;
            spacingScale *= 0.95;
        } else if (screenArea > 2560 * 1440) {
            // Large screens - make things slightly bigger
            optimalScale *= 1.05;
            fontScale *= 1.02;
            spacingScale *= 1.05;
        }
        
        this.scalingInfo = {
            platform: this.platform,
            devicePixelRatio: dpr,
            screenWidth: this.screenWidth,
            screenHeight: this.screenHeight,
            screenArea,
            optimalScale,
            fontScale,
            spacingScale,
            viewportWidth: this.viewportWidth,
            viewportHeight: this.viewportHeight
        };
    }

    applyScalingAdjustments() {
        const root = document.documentElement;
        const { optimalScale, fontScale, spacingScale } = this.scalingInfo;
        
        // Apply CSS custom properties
        root.style.setProperty('--detected-scale', this.devicePixelRatio);
        root.style.setProperty('--platform-adjustment', optimalScale);
        root.style.setProperty('--font-scale', fontScale);
        root.style.setProperty('--spacing-scale', spacingScale);
        
        // Apply zoom to body if Electron and significant scaling needed
        if (this.isElectron && Math.abs(optimalScale - 1.0) > 0.05) {
            // Let Electron handle the zoom level instead of CSS transform
            // This is handled in the main process
            root.style.setProperty('--electron-zoom', optimalScale);
            
            console.log('[ScalingManager] Electron zoom factor should be:', optimalScale);
        }
        
        // Apply font size adjustments
        if (Math.abs(fontScale - 1.0) > 0.01) {
            const baseFontSize = 14 * fontScale;
            root.style.setProperty('--optimal-font-base', `${baseFontSize}px`);
        }
        
        // Add platform class for platform-specific styling
        document.body.classList.add(`platform-${this.platform}`);
        
        // Add DPR class for DPR-specific styling
        const dprClass = `dpr-${Math.floor(this.devicePixelRatio * 10)}`;
        document.body.classList.add(dprClass);
        
        console.log('[ScalingManager] Applied scaling adjustments:', {
            optimalScale,
            fontScale,
            spacingScale,
            platformClass: `platform-${this.platform}`,
            dprClass
        });
    }

    setupResizeListener() {
        let resizeTimeout;
        
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                this.viewportWidth = window.innerWidth;
                this.viewportHeight = window.innerHeight;
                
                // Recalculate if viewport changed significantly
                const widthChange = Math.abs(this.viewportWidth - this.scalingInfo.viewportWidth);
                const heightChange = Math.abs(this.viewportHeight - this.scalingInfo.viewportHeight);
                
                if (widthChange > 100 || heightChange > 100) {
                    console.log('[ScalingManager] Significant viewport change detected, recalculating...');
                    this.calculateOptimalScaling();
                    this.applyScalingAdjustments();
                }
            }, 250);
        });
    }

    logScalingInfo() {
        console.log('[ScalingManager] Final scaling configuration:', this.scalingInfo);
        
        // Add debug info to DOM if in development
        if (this.isElectron && window.location.search.includes('debug')) {
            document.documentElement.setAttribute('data-debug', 'true');
            
            // Create debug panel
            const debugPanel = document.createElement('div');
            debugPanel.style.cssText = `
                position: fixed;
                top: 10px;
                right: 10px;
                background: rgba(0, 0, 0, 0.9);
                color: white;
                padding: 10px;
                border-radius: 5px;
                font-family: monospace;
                font-size: 11px;
                z-index: 10000;
                max-width: 300px;
                line-height: 1.3;
            `;
            
            debugPanel.innerHTML = `
                <strong>Scaling Debug Info:</strong><br>
                Platform: ${this.scalingInfo.platform}<br>
                DPR: ${this.scalingInfo.devicePixelRatio}<br>
                Screen: ${this.scalingInfo.screenWidth}×${this.scalingInfo.screenHeight}<br>
                Viewport: ${this.scalingInfo.viewportWidth}×${this.scalingInfo.viewportHeight}<br>
                Optimal Scale: ${this.scalingInfo.optimalScale}<br>
                Font Scale: ${this.scalingInfo.fontScale}<br>
                Spacing Scale: ${this.scalingInfo.spacingScale}
            `;
            
            document.body.appendChild(debugPanel);
            
            // Remove debug panel after 10 seconds
            setTimeout(() => {
                if (debugPanel.parentNode) {
                    debugPanel.parentNode.removeChild(debugPanel);
                }
            }, 10000);
        }
    }

    // Public method to get current scaling info
    getScalingInfo() {
        return this.scalingInfo;
    }

    // Public method to force recalculation
    recalculate() {
        this.calculateOptimalScaling();
        this.applyScalingAdjustments();
        this.logScalingInfo();
    }
}

// Initialize scaling manager when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.scalingManager = new ScalingManager();
    });
} else {
    window.scalingManager = new ScalingManager();
}

// Export for use in other scripts
window.ScalingManager = ScalingManager;
