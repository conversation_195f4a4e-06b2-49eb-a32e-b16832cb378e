/**
 * ModuleRegistry
 * Central registry for managing installed and available modules in CoreDesk Framework v2.0.0
 * 
 * Features:
 * - Track installed modules and their metadata
 * - Manage module dependencies and relationships
 * - Provide query interface for modules
 * - Handle module state persistence
 * - Support for module categories and tagging
 * - Integration with license validation
 */

class ModuleRegistry extends EventTarget {
    constructor(options = {}) {
        super();
        
        // Configuration
        this.options = {
            persistenceEnabled: true,
            storagePrefix: 'coredesk_registry_',
            cacheTTL: 24 * 60 * 60 * 1000, // 24 hours
            autoCleanup: true,
            validateIntegrity: true,
            ...options
        };
        
        // Registry state
        this.modules = new Map();              // moduleId -> ModulePackage
        this.moduleMetadata = new Map();       // moduleId -> metadata
        this.dependencies = new Map();         // moduleId -> Set of dependent moduleIds
        this.categories = new Map();           // category -> Set of moduleIds
        this.tags = new Map();                 // tag -> Set of moduleIds
        
        // Cache and indexing
        this.searchIndex = new Map();          // search term -> Set of moduleIds
        this.lastUpdated = null;
        this.isInitialized = false;
        
        // Storage interface
        this.storage = this.createStorageInterface();
        
        // Logger
        this.logger = window.GlobalLogger || {
            info: (tag, message) => console.log(`[${tag}] ${message}`),
            error: (tag, message, error) => console.error(`[${tag}] ${message}`, error),
            warn: (tag, message) => console.warn(`[${tag}] ${message}`),
            debug: (tag, message) => console.debug(`[${tag}] ${message}`)
        };
    }

    /**
     * Initialize the module registry
     * @returns {Promise<void>}
     */
    async initialize() {
        try {
            this.logger.info('ModuleRegistry', 'Initializing module registry...');

            // Only run cleanup if user is already authenticated to avoid interfering with login
            const hasValidToken = this.checkAuthenticationStatus();
            if (hasValidToken) {
                // Clean up any corrupted data before loading (but preserve auth data)
                await this.cleanupCorruptedData();
            } else {
                this.logger.info('ModuleRegistry', 'Skipping corrupted data cleanup - user not authenticated yet');
            }

            // Load persisted registry data
            if (this.options.persistenceEnabled) {
                // Add a small delay to ensure app is initialized
                await new Promise(resolve => setTimeout(resolve, 100));
                await this.loadPersistedData();
            }

            // Build search index
            this.buildSearchIndex();

            // Setup cleanup if enabled
            if (this.options.autoCleanup) {
                this.setupAutoCleanup();
            }

            this.isInitialized = true;
            this.lastUpdated = new Date();

            this.logger.info('ModuleRegistry', `Registry initialized with ${this.modules.size} modules`);
            this.dispatchEvent(new CustomEvent('registryInitialized', {
                detail: { moduleCount: this.modules.size }
            }));

        } catch (error) {
            this.logger.error('ModuleRegistry', 'Failed to initialize registry:', error);
            throw new Error(`ModuleRegistry initialization failed: ${error.message}`);
        }
    }

    /**
     * Register a module package
     * @param {ModulePackage} modulePackage - Module package to register
     * @returns {Promise<void>}
     */
    async registerModule(modulePackage) {
        try {
            if (!modulePackage || !modulePackage.id) {
                throw new Error('Invalid module package');
            }

            const moduleId = modulePackage.id;
            this.logger.info('ModuleRegistry', `Registering module: ${moduleId}`);

            // Validate module package
            await this.validateModulePackage(modulePackage);

            // Check for conflicts
            if (this.modules.has(moduleId)) {
                const existingVersion = this.modules.get(moduleId).version;
                if (!this.isVersionNewer(modulePackage.version, existingVersion)) {
                    throw new Error(`Module ${moduleId} v${existingVersion} is already registered`);
                }
                
                // Unregister old version first
                await this.unregisterModule(moduleId, { skipValidation: true });
            }

            // Store module package
            this.modules.set(moduleId, modulePackage);

            // Store metadata
            const metadata = this.extractModuleMetadata(modulePackage);
            this.moduleMetadata.set(moduleId, metadata);

            // Update dependency tracking
            await this.updateDependencyTracking(moduleId, modulePackage);

            // Update category and tag indexing
            this.updateCategoryIndex(moduleId, metadata);
            this.updateTagIndex(moduleId, metadata);

            // Update search index
            this.updateSearchIndex(moduleId, metadata);

            // Persist changes
            if (this.options.persistenceEnabled) {
                await this.persistModuleData(moduleId, metadata);
            }

            this.lastUpdated = new Date();

            this.logger.info('ModuleRegistry', `Module ${moduleId} registered successfully`);
            this.dispatchEvent(new CustomEvent('moduleRegistered', {
                detail: { moduleId, metadata }
            }));

        } catch (error) {
            this.logger.error('ModuleRegistry', `Failed to register module:`, error);
            throw error;
        }
    }

    /**
     * Unregister a module
     * @param {string} moduleId - Module identifier
     * @param {Object} options - Unregistration options
     * @returns {Promise<void>}
     */
    async unregisterModule(moduleId, options = {}) {
        try {
            const { skipValidation = false, force = false } = options;

            this.logger.info('ModuleRegistry', `Unregistering module: ${moduleId}`);

            if (!this.modules.has(moduleId)) {
                throw new Error(`Module ${moduleId} is not registered`);
            }

            // Check dependencies if not forcing
            if (!force && !skipValidation) {
                const dependents = this.getModulesDependingOn(moduleId);
                if (dependents.length > 0) {
                    throw new Error(`Cannot unregister ${moduleId}. Required by: ${dependents.join(', ')}`);
                }
            }

            // Remove from all indexes
            this.removeFromCategoryIndex(moduleId);
            this.removeFromTagIndex(moduleId);
            this.removeFromSearchIndex(moduleId);
            this.removeDependencyTracking(moduleId);

            // Remove from main storage
            this.modules.delete(moduleId);
            this.moduleMetadata.delete(moduleId);

            // Remove persisted data
            if (this.options.persistenceEnabled) {
                await this.removePersistedModuleData(moduleId);
            }

            this.lastUpdated = new Date();

            this.logger.info('ModuleRegistry', `Module ${moduleId} unregistered successfully`);
            this.dispatchEvent(new CustomEvent('moduleUnregistered', {
                detail: { moduleId }
            }));

        } catch (error) {
            this.logger.error('ModuleRegistry', `Failed to unregister module ${moduleId}:`, error);
            throw error;
        }
    }

    /**
     * Get a registered module package
     * @param {string} moduleId - Module identifier
     * @returns {ModulePackage|null}
     */
    getModule(moduleId) {
        return this.modules.get(moduleId) || null;
    }

    /**
     * Get module metadata
     * @param {string} moduleId - Module identifier
     * @returns {Object|null}
     */
    getModuleMetadata(moduleId) {
        return this.moduleMetadata.get(moduleId) || null;
    }

    /**
     * Get all installed module IDs
     * @returns {Array<string>}
     */
    getInstalledModuleIds() {
        return Array.from(this.modules.keys());
    }

    /**
     * Get all installed modules
     * @returns {Array<ModulePackage>}
     */
    getInstalledModules() {
        return Array.from(this.modules.values());
    }

    /**
     * Get modules by category
     * @param {string} category - Category name
     * @returns {Array<string>}
     */
    getModulesByCategory(category) {
        return Array.from(this.categories.get(category) || []);
    }

    /**
     * Get modules by tag
     * @param {string} tag - Tag name
     * @returns {Array<string>}
     */
    getModulesByTag(tag) {
        return Array.from(this.tags.get(tag) || []);
    }

    /**
     * Search modules
     * @param {string} query - Search query
     * @param {Object} filters - Additional filters
     * @returns {Array<Object>}
     */
    searchModules(query, filters = {}) {
        const results = [];
        const queryLower = query.toLowerCase();

        for (const [moduleId, metadata] of this.moduleMetadata) {
            let score = 0;

            // Name match (highest score)
            if (metadata.name.toLowerCase().includes(queryLower)) {
                score += 10;
            }

            // ID match
            if (metadata.id.toLowerCase().includes(queryLower)) {
                score += 8;
            }

            // Description match
            if (metadata.description && metadata.description.toLowerCase().includes(queryLower)) {
                score += 5;
            }

            // Tag match
            if (metadata.tags && metadata.tags.some(tag => tag.toLowerCase().includes(queryLower))) {
                score += 3;
            }

            // Category match
            if (metadata.category && metadata.category.toLowerCase().includes(queryLower)) {
                score += 3;
            }

            // Apply filters
            if (score > 0 && this.matchesFilters(metadata, filters)) {
                results.push({
                    moduleId,
                    metadata,
                    score
                });
            }
        }

        // Sort by score (descending)
        return results.sort((a, b) => b.score - a.score);
    }

    /**
     * Get modules that depend on a specific module
     * @param {string} moduleId - Module identifier
     * @returns {Array<string>}
     */
    getModulesDependingOn(moduleId) {
        const dependents = [];

        for (const [currentModuleId, currentMetadata] of this.moduleMetadata) {
            if (currentMetadata.dependencies && currentMetadata.dependencies[moduleId]) {
                dependents.push(currentModuleId);
            }
        }

        return dependents;
    }

    /**
     * Get module dependency chain
     * @param {string} moduleId - Module identifier
     * @returns {Object}
     */
    getModuleDependencies(moduleId) {
        const metadata = this.moduleMetadata.get(moduleId);
        if (!metadata || !metadata.dependencies) {
            return { dependencies: [], missing: [] };
        }

        const dependencies = [];
        const missing = [];

        for (const [depId, version] of Object.entries(metadata.dependencies)) {
            if (depId === 'coredesk') continue; // Skip core dependency

            if (this.modules.has(depId)) {
                const depMetadata = this.moduleMetadata.get(depId);
                dependencies.push({
                    id: depId,
                    requiredVersion: version,
                    installedVersion: depMetadata.version,
                    compatible: this.isVersionCompatible(version, depMetadata.version)
                });
            } else {
                missing.push({
                    id: depId,
                    requiredVersion: version
                });
            }
        }

        return { dependencies, missing };
    }

    /**
     * Check if a module is registered
     * @param {string} moduleId - Module identifier
     * @returns {boolean}
     */
    isModuleRegistered(moduleId) {
        return this.modules.has(moduleId);
    }

    /**
     * Get registry statistics
     * @returns {Object}
     */
    getRegistryStats() {
        const stats = {
            totalModules: this.modules.size,
            categories: {},
            tags: {},
            versions: {},
            lastUpdated: this.lastUpdated
        };

        // Count by category
        for (const [category, moduleSet] of this.categories) {
            stats.categories[category] = moduleSet.size;
        }

        // Count by tag
        for (const [tag, moduleSet] of this.tags) {
            stats.tags[tag] = moduleSet.size;
        }

        // Count by version
        for (const metadata of this.moduleMetadata.values()) {
            const majorVersion = metadata.version.split('.')[0];
            stats.versions[majorVersion] = (stats.versions[majorVersion] || 0) + 1;
        }

        return stats;
    }

    /**
     * Export registry data
     * @returns {Object}
     */
    exportRegistry() {
        const exported = {
            version: '1.0.0',
            exportedAt: new Date().toISOString(),
            modules: {},
            metadata: Object.fromEntries(this.moduleMetadata)
        };

        // Include module data (excluding actual code for security)
        for (const [moduleId, modulePackage] of this.modules) {
            exported.modules[moduleId] = {
                manifest: modulePackage.manifest,
                isInstalled: modulePackage.isInstalled,
                installedAt: modulePackage.installedAt
            };
        }

        return exported;
    }

    /**
     * Import registry data
     * @param {Object} registryData - Registry data to import
     * @returns {Promise<void>}
     */
    async importRegistry(registryData) {
        try {
            this.logger.info('ModuleRegistry', 'Importing registry data...');

            if (!registryData.version || !registryData.modules) {
                throw new Error('Invalid registry data format');
            }

            // Clear current registry
            this.modules.clear();
            this.moduleMetadata.clear();
            this.clearAllIndexes();

            // Import metadata
            for (const [moduleId, metadata] of Object.entries(registryData.metadata)) {
                this.moduleMetadata.set(moduleId, metadata);
                this.updateCategoryIndex(moduleId, metadata);
                this.updateTagIndex(moduleId, metadata);
                this.updateSearchIndex(moduleId, metadata);
            }

            // Rebuild search index
            this.buildSearchIndex();

            this.lastUpdated = new Date();

            this.logger.info('ModuleRegistry', `Registry imported with ${this.moduleMetadata.size} modules`);

        } catch (error) {
            this.logger.error('ModuleRegistry', 'Failed to import registry:', error);
            throw error;
        }
    }

    // Private Methods

    /**
     * Check if user is authenticated before running cleanup
     * @private
     */
    checkAuthenticationStatus() {
        try {
            const token = localStorage.getItem('coredesk_token');
            const tokenExpiry = localStorage.getItem('coredesk_token_expiry');
            
            if (!token || !tokenExpiry) {
                return false;
            }
            
            // Check if token is expired
            let expiryTime;
            if (tokenExpiry.includes('T') || tokenExpiry.includes('-')) {
                expiryTime = new Date(tokenExpiry).getTime();
            } else {
                expiryTime = parseInt(tokenExpiry);
            }
            
            const now = Date.now();
            const isValidToken = !isNaN(expiryTime) && now < expiryTime;
            
            this.logger.debug('ModuleRegistry', `Authentication check: token=${!!token}, expiry=${!!tokenExpiry}, valid=${isValidToken}`);
            return isValidToken;
            
        } catch (error) {
            this.logger.warn('ModuleRegistry', 'Error checking authentication status:', error);
            return false;
        }
    }

    /**
     * Clean up corrupted data before initialization
     * @private
     */
    async cleanupCorruptedData() {
        try {
            this.logger.info('ModuleRegistry', 'Performing corrupted data cleanup...');

            // Define protected keys that should NEVER be deleted
            const protectedKeys = [
                'coredesk_token',
                'coredesk_token_expiry',
                'coredesk_refresh_token',
                'coredesk_user',
                'coredesk_user_data',
                'coredesk_auth',
                'coredesk_license',
                'coredesk_activation',
                'coredesk_trial',
                'coredesk_trialFlow',
                'coredesk_device',
                'coredesk_fingerprint',
                'coredesk_config',
                'coredesk_settings',
                'coredesk_theme',
                'coredesk_language',
                'coredesk_session',
                // Module persistence and security keys
                'coredesk_active_module',
                'coredesk_salt',
                'coredesk_encryption_key',
                'coredesk_registry_registry'
            ];

            // Clean up localStorage entries with corrupted values
            const corruptedKeys = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith('coredesk_')) {
                    
                    // NEVER delete protected keys
                    if (protectedKeys.includes(key)) {
                        this.logger.debug('ModuleRegistry', `Skipping protected key: ${key}`);
                        continue;
                    }
                    
                    // Special handling for module keys - be more careful
                    if (key.startsWith('coredesk_module_')) {
                        try {
                            const value = localStorage.getItem(key);
                            if (!value || 
                                value === 'undefined' || 
                                value === 'null' ||
                                value.trim() === '') {
                                corruptedKeys.push(key);
                            } else {
                                const parsed = JSON.parse(value);
                                // Only mark as corrupted if clearly invalid
                                if (!parsed || 
                                    !parsed.moduleId || 
                                    parsed.moduleId === 'undefined' ||
                                    parsed.moduleId === 'null' ||
                                    !parsed.name ||
                                    !parsed.version) {
                                    this.logger.warn('ModuleRegistry', `Module key ${key} has corrupted data structure`);
                                    corruptedKeys.push(key);
                                }
                            }
                        } catch (error) {
                            this.logger.warn('ModuleRegistry', `JSON parsing failed for module key ${key}:`, error);
                            corruptedKeys.push(key);
                        }
                        continue; // Skip general validation for module keys
                    }
                    
                    try {
                        const value = localStorage.getItem(key);
                        if (!value || 
                            value === 'undefined' || 
                            value === 'null' ||
                            value.trim() === '') {
                            corruptedKeys.push(key);
                        } else {
                            // Try to parse JSON to validate only for non-protected keys
                            const parsed = JSON.parse(value);
                            
                            // Additional validation for registry data ONLY
                            if (key.includes('_registry') && parsed.metadata) {
                                for (const [moduleId, metadata] of Object.entries(parsed.metadata)) {
                                    if (!metadata || 
                                        metadata === 'undefined' ||
                                        metadata === 'null' ||
                                        !metadata.id ||
                                        metadata.id === 'undefined' ||
                                        metadata.id === 'null') {
                                        this.logger.warn('ModuleRegistry', `Found corrupted metadata for ${moduleId}, marking registry for cleanup`);
                                        corruptedKeys.push(key);
                                        break;
                                    }
                                }
                            }
                        }
                    } catch (error) {
                        // JSON parsing failed for non-protected keys, mark as corrupted
                        this.logger.warn('ModuleRegistry', `JSON parsing failed for non-protected key ${key}:`, error);
                        corruptedKeys.push(key);
                    }
                }
            }

            // Remove corrupted localStorage keys (excluding protected ones)
            for (const key of corruptedKeys) {
                if (!protectedKeys.includes(key)) {
                    localStorage.removeItem(key);
                    this.logger.info('ModuleRegistry', `Removed corrupted localStorage entry: ${key}`);
                } else {
                    this.logger.warn('ModuleRegistry', `Attempted to remove protected key ${key}, skipping`);
                }
            }

            // Clean up specific corrupted module entries (but NOT auth data)
            // Only clean up modules that are clearly corrupted, not all modules
            const potentiallyCorruptedModules = ['lexflow', 'finsync', 'protocolx', 'auditpro'];
            for (const moduleId of potentiallyCorruptedModules) {
                const moduleKey = `coredesk_module_${moduleId}`;
                
                // Skip if this is somehow a protected key
                if (protectedKeys.includes(moduleKey)) {
                    continue;
                }
                
                const moduleData = localStorage.getItem(moduleKey);
                if (moduleData) {
                    try {
                        const parsed = JSON.parse(moduleData);
                        // Only remove if manifestData is clearly corrupted (not just missing)
                        if (parsed.manifestData === 'undefined' ||
                            parsed.manifestData === 'null' ||
                            (typeof parsed.manifestData === 'string' && parsed.manifestData.trim() === '') ||
                            (parsed.manifestData && typeof parsed.manifestData === 'object' && Object.keys(parsed.manifestData).length === 0)) {
                            localStorage.removeItem(moduleKey);
                            this.logger.info('ModuleRegistry', `Removed corrupted module localStorage entry: ${moduleKey}`);
                        } else if (!parsed.moduleId || !parsed.name || !parsed.version) {
                            // Remove if essential fields are missing
                            localStorage.removeItem(moduleKey);
                            this.logger.info('ModuleRegistry', `Removed module with missing essential fields: ${moduleKey}`);
                        }
                    } catch (error) {
                        // Only remove if JSON is completely unparseable
                        localStorage.removeItem(moduleKey);
                        this.logger.info('ModuleRegistry', `Removed unparseable module localStorage entry: ${moduleKey}`);
                    }
                }
            }

            // Clean up temporary files that might be causing conflicts
            if (window.electronAPI?.fileSystem) {
                try {
                    // Get proper temp path instead of hardcoded path
                    const coreDeskPathResult = await window.electronAPI.fileSystem.getCoreDeskPath();
                    const tempExtractionPath = coreDeskPathResult.success ? 
                        `${coreDeskPathResult.path}/temp/extraction` : 
                        require('os').tmpdir() + '/coredesk/extraction';
                        
                    const tempPathExists = await window.electronAPI.fileSystem.exists(tempExtractionPath);
                    if (tempPathExists.exists) {
                        this.logger.info('ModuleRegistry', 'Cleaning up temporary extraction files...');
                        // Note: Files were already cleaned in the shell command above
                    }
                } catch (tempError) {
                    this.logger.warn('ModuleRegistry', 'Failed to clean temp files:', tempError);
                }
            }

            // If persistence client is available, clean up database entries (but NOT auth data)
            if (window.modulePersistenceClient) {
                try {
                    const allModules = await window.modulePersistenceClient.getActiveModules();
                    for (const dbModule of allModules) {
                        const moduleId = dbModule.moduleId || dbModule.module_id;
                        
                        // Check for corrupted database entries
                        if (!moduleId || 
                            moduleId === 'undefined' || 
                            moduleId === 'null' ||
                            typeof moduleId !== 'string' ||
                            moduleId.trim() === '') {
                            
                            this.logger.warn('ModuleRegistry', `Found corrupted database module entry, cleaning up:`, dbModule);
                            
                            // Try to clean up using available identifiers
                            if (dbModule.id) {
                                await window.modulePersistenceClient.unregisterModule(dbModule.id);
                            }
                        } else if (moduleId === 'lexflow') {
                            // Special handling for lexflow since it's causing issues
                            this.logger.info('ModuleRegistry', `Checking lexflow database entry for corruption...`);
                            
                            // Check if the module files actually exist
                            if (window.electronAPI?.fileSystem) {
                                const modulesPathResult = await window.electronAPI.fileSystem.getModulesPath();
                                if (modulesPathResult.success) {
                                    const moduleDir = `${modulesPathResult.path}/${moduleId}`;
                                    const manifestFile = `${moduleDir}/manifest.json`;
                                    
                                    const manifestExists = await window.electronAPI.fileSystem.exists(manifestFile);
                                    if (!manifestExists.exists) {
                                        this.logger.warn('ModuleRegistry', `Lexflow database entry exists but files missing, cleaning up...`);
                                        await window.modulePersistenceClient.unregisterModule(moduleId);
                                    } else {
                                        // Check if manifest is readable
                                        const manifestContent = await window.electronAPI.fileSystem.readFile(manifestFile);
                                        if (!manifestContent.success || 
                                            !manifestContent.content ||
                                            manifestContent.content === 'undefined' ||
                                            manifestContent.content === 'null') {
                                            
                                            this.logger.warn('ModuleRegistry', `Lexflow manifest is corrupted, cleaning up database entry...`);
                                            await window.modulePersistenceClient.unregisterModule(moduleId);
                                        }
                                    }
                                }
                            }
                        }
                    }
                } catch (dbError) {
                    this.logger.warn('ModuleRegistry', 'Database cleanup failed (might not be ready yet):', dbError);
                }
            }

            this.logger.info('ModuleRegistry', 'Corrupted data cleanup completed (protected auth data preserved)');

        } catch (error) {
            this.logger.error('ModuleRegistry', 'Error during corrupted data cleanup:', error);
        }
    }

    /**
     * Create storage interface
     * @private
     */
    createStorageInterface() {
        return {
            getItem: (key) => localStorage.getItem(key),
            setItem: (key, value) => localStorage.setItem(key, value),
            removeItem: (key) => localStorage.removeItem(key),
            getAllKeys: () => Object.keys(localStorage).filter(key => 
                key.startsWith(this.options.storagePrefix)
            )
        };
    }

    /**
     * Load persisted registry data
     * @private
     */
    async loadPersistedData() {
        try {
            const registryKey = `${this.options.storagePrefix}registry`;
            const registryData = this.storage.getItem(registryKey);

            if (registryData) {
                let parsed;
                try {
                    // Defensive JSON parsing for registry data
                    if (registryData === 'undefined' || 
                        registryData === 'null' ||
                        typeof registryData !== 'string' ||
                        registryData.trim() === '') {
                        throw new Error(`Invalid registry data: ${JSON.stringify(registryData)}`);
                    }
                    parsed = JSON.parse(registryData);
                } catch (parseError) {
                    this.logger.warn('ModuleRegistry', `Corrupted registry cache data, clearing: ${parseError.message}`);
                    this.storage.removeItem(registryKey);
                    return;
                }
                
                // Check if data is still valid (TTL)
                if (this.isDataValid(parsed)) {
                    // Load metadata
                    for (const [moduleId, metadata] of Object.entries(parsed.metadata || {})) {
                        this.moduleMetadata.set(moduleId, metadata);
                        this.updateCategoryIndex(moduleId, metadata);
                        this.updateTagIndex(moduleId, metadata);
                    }

                    this.logger.debug('ModuleRegistry', 'Loaded persisted registry data');
                } else {
                    this.logger.warn('ModuleRegistry', 'Persisted registry data expired');
                }
            }

            // Also load installed modules from individual storage keys
            await this.loadInstalledModulesFromStorage();

        } catch (error) {
            this.logger.warn('ModuleRegistry', 'Failed to load persisted data:', error);
        }
    }

    /**
     * Load installed modules from their individual storage keys
     * @private
     */
    async loadInstalledModulesFromStorage() {
        try {
            this.logger.info('ModuleRegistry', 'Loading installed modules from database...');

            // Initialize persistence client if not available
            if (!window.modulePersistenceClient) {
                if (window.ModulePersistenceClient) {
                    window.modulePersistenceClient = new window.ModulePersistenceClient(this.logger);
                    await window.modulePersistenceClient.initialize();
                } else {
                    this.logger.warn('ModuleRegistry', 'ModulePersistenceClient not available, falling back to filesystem scan');
                    await this.loadInstalledModulesFromFilesystem();
                    
                    // Listen for database ready event to sync later
                    if (window.electronAPI && window.electronAPI.on) {
                        window.electronAPI.on('database:ready', async () => {
                            this.logger.info('ModuleRegistry', 'Database ready, attempting to initialize persistence client...');
                            if (window.ModulePersistenceClient && !window.modulePersistenceClient) {
                                window.modulePersistenceClient = new window.ModulePersistenceClient(this.logger);
                                const initialized = await window.modulePersistenceClient.initialize();
                                if (initialized) {
                                    this.logger.info('ModuleRegistry', 'Persistence client initialized after database ready');
                                }
                            }
                        });
                    }
                    
                    return;
                }
            }

            // Get installed modules from database
            const installedModules = await window.modulePersistenceClient.getActiveModules();
            this.logger.info('ModuleRegistry', `Found ${installedModules.length} active modules in database`);

            // Get modules path for validation
            const modulesPathResult = await window.electronAPI.fileSystem.getModulesPath();
            if (!modulesPathResult.success) {
                throw new Error(`Failed to get modules path: ${modulesPathResult.error}`);
            }
            const modulesPath = modulesPathResult.path;

            // Validate and load each module
            for (const dbModule of installedModules) {
                try {
                    // Check for moduleId from both JSON persistence (moduleId) and database (module_id)
                    const moduleId = dbModule.moduleId || dbModule.module_id;
                    
                    // Skip corrupted records with undefined/null/invalid moduleId
                    if (!moduleId || 
                        moduleId === 'undefined' || 
                        moduleId === 'null' || 
                        moduleId === undefined || 
                        moduleId === null ||
                        typeof moduleId !== 'string' ||
                        moduleId.trim() === '') {
                        
                        this.logger.warn('ModuleRegistry', `Found corrupted module record with invalid moduleId: ${JSON.stringify(moduleId)} (type: ${typeof moduleId}), cleaning up`);
                        
                        // Clean up the corrupted record using available identifier
                        if (dbModule.id) {
                            // If we have a database ID, try to clean up by that
                            await window.modulePersistenceClient.unregisterModule(dbModule.id);
                        } else if (dbModule.moduleId) {
                            // Try to clean up by moduleId if available  
                            await window.modulePersistenceClient.unregisterModule(dbModule.moduleId);
                        } else if (dbModule.module_id) {
                            // Try to clean up by module_id if available
                            await window.modulePersistenceClient.unregisterModule(dbModule.module_id);
                        }
                        continue;
                    }
                    
                    // Additional validation - moduleId should not contain special characters that would break file paths
                    if (!/^[a-zA-Z0-9._-]+$/.test(moduleId)) {
                        this.logger.warn('ModuleRegistry', `Found module with invalid characters in moduleId: ${moduleId}, cleaning up`);
                        await window.modulePersistenceClient.unregisterModule(moduleId);
                        continue;
                    }
                    
                    const moduleDir = `${modulesPath}/${moduleId}`;

                    this.logger.debug('ModuleRegistry', `Validating module: ${moduleId}`);

                    // Check if module directory and files exist
                    const moduleFile = `${moduleDir}/${moduleId}.js`;
                    const manifestFile = `${moduleDir}/manifest.json`;

                    const moduleFileExists = await window.electronAPI.fileSystem.exists(moduleFile);
                    const manifestFileExists = await window.electronAPI.fileSystem.exists(manifestFile);

                    if (!moduleFileExists.exists || !manifestFileExists.exists) {
                        this.logger.warn('ModuleRegistry', `Module ${moduleId} files missing, cleaning up database record`);
                        await window.modulePersistenceClient.unregisterModule(moduleId);
                        continue;
                    }

                    // Read and validate manifest with enhanced diagnostics
                    this.logger.info('ModuleRegistry', `About to read manifest file: ${manifestFile}`);
                    
                    // First check if file actually exists and get stats
                    try {
                        const fileStats = await window.electronAPI.fileSystem.getFileStats(manifestFile);
                        this.logger.info('ModuleRegistry', `Manifest file stats for ${moduleId}:`, {
                            exists: fileStats?.success,
                            size: fileStats?.stats?.size,
                            error: fileStats?.error
                        });
                        
                        if (!fileStats?.success) {
                            throw new Error(`Manifest file does not exist or cannot be accessed: ${fileStats?.error}`);
                        }
                        
                        if (!fileStats.stats?.size || fileStats.stats.size === 0) {
                            throw new Error(`Manifest file is empty (size: ${fileStats.stats?.size})`);
                        }
                    } catch (statError) {
                        this.logger.error('ModuleRegistry', `Cannot get file stats for ${manifestFile}: ${statError.message}`);
                        throw new Error(`File stats check failed: ${statError.message}`);
                    }
                    
                    const manifestContent = await window.electronAPI.fileSystem.readFile(manifestFile);
                    
                    // Log the full manifestContent for debugging
                    this.logger.debug('ModuleRegistry', `Manifest read result for ${moduleId}:`, manifestContent);
                    this.logger.info('ModuleRegistry', `Manifest content: ${JSON.stringify(manifestContent?.content)} (type: ${typeof manifestContent?.content})`);
                    this.logger.info('ModuleRegistry', `Manifest success: ${manifestContent?.success}, error: ${manifestContent?.error}`);
                    
                    // Defensive JSON parsing to handle corrupted manifest files
                    let manifest;
                    try {
                        if (!manifestContent || !manifestContent.success) {
                            throw new Error(`Failed to read manifest file: ${manifestContent?.error || 'Unknown error'}`);
                        }
                        
                        if (!manifestContent.content || 
                            manifestContent.content === 'undefined' || 
                            manifestContent.content === 'null' ||
                            typeof manifestContent.content !== 'string' ||
                            manifestContent.content.trim() === '') {
                            throw new Error(`Invalid manifest content: ${JSON.stringify(manifestContent.content)} (type: ${typeof manifestContent.content})`);
                        }
                        
                        // Parse JSON with additional validation
                        let parsedManifest;
                        try {
                            parsedManifest = JSON.parse(manifestContent.content);
                        } catch (jsonError) {
                            throw new Error(`JSON parsing failed: ${jsonError.message}`);
                        }
                        
                        // Validate and sanitize the manifest data
                        manifest = this.validateAndSanitizeManifest(parsedManifest, moduleId);
                        
                        if (!manifest) {
                            throw new Error('Manifest validation failed after parsing');
                        }
                        
                    } catch (parseError) {
                        this.logger.error('ModuleRegistry', `Error parsing manifest for module ${moduleId}: ${parseError.message}`);
                        this.logger.error('ModuleRegistry', `Manifest content: ${JSON.stringify(manifestContent?.content)} (type: ${typeof manifestContent?.content})`);
                        
                        // Log all recovery strategies for debugging
                        this.logger.info('ModuleRegistry', `Attempting to recover corrupted module ${moduleId}...`);
                        const recoverySuccessful = await this.attemptModuleRecovery(moduleId, dbModule, manifestFile);
                        
                        if (recoverySuccessful) {
                            this.logger.info('ModuleRegistry', `Successfully recovered module ${moduleId}`);
                            // Retry loading the recovered module with additional safety checks
                            try {
                                const recoveredManifestContent = await window.electronAPI.fileSystem.readFile(manifestFile);
                                if (recoveredManifestContent.success && 
                                    recoveredManifestContent.content &&
                                    recoveredManifestContent.content !== 'undefined' &&
                                    recoveredManifestContent.content !== 'null' &&
                                    typeof recoveredManifestContent.content === 'string' &&
                                    recoveredManifestContent.content.trim() !== '') {
                                    
                                    const parsedRecoveredManifest = JSON.parse(recoveredManifestContent.content);
                                    manifest = this.validateAndSanitizeManifest(parsedRecoveredManifest, moduleId);
                                    
                                    if (manifest) {
                                        this.logger.info('ModuleRegistry', `Module ${moduleId} recovery successful, continuing with loading`);
                                    } else {
                                        throw new Error('Recovery failed - recovered manifest validation failed');
                                    }
                                } else {
                                    throw new Error('Recovery failed - manifest still unreadable');
                                }
                            } catch (retryError) {
                                this.logger.error('ModuleRegistry', `Recovery retry failed for ${moduleId}:`, retryError);
                                this.logger.error('ModuleRegistry', `All recovery strategies failed for ${moduleId}`);
                                
                                // IMPROVED: Before deleting, check if the physical file exists and has content
                                this.logger.info('ModuleRegistry', `Before cleanup: Checking physical file integrity for ${moduleId}`);
                                try {
                                    const fileExists = await window.electronAPI.fileSystem.exists(manifestFile);
                                    if (fileExists.exists) {
                                        const fileStats = await window.electronAPI.fileSystem.getFileStats(manifestFile);
                                        if (fileStats.success && fileStats.stats?.size > 0) {
                                            this.logger.warn('ModuleRegistry', `Physical manifest file exists and has content (${fileStats.stats.size} bytes) - SKIPPING deletion for ${moduleId}`);
                                            this.logger.warn('ModuleRegistry', `Module ${moduleId} will remain in filesystem but be unregistered from active modules`);
                                            await window.modulePersistenceClient.unregisterModule(moduleId);
                                            continue;
                                        }
                                    }
                                } catch (checkError) {
                                    this.logger.error('ModuleRegistry', `Error checking physical file for ${moduleId}:`, checkError);
                                }
                                
                                // Only cleanup if file is truly corrupted or doesn't exist
                                await this.cleanupCorruptedModule(moduleId, manifestFile);
                                await window.modulePersistenceClient.unregisterModule(moduleId);
                                continue;
                            }
                        } else {
                            this.logger.warn('ModuleRegistry', `All recovery strategies failed for ${moduleId}`);
                            
                            // IMPROVED: Before deleting, check if the physical file exists and has content
                            this.logger.info('ModuleRegistry', `Before cleanup: Checking physical file integrity for ${moduleId}`);
                            try {
                                const fileExists = await window.electronAPI.fileSystem.exists(manifestFile);
                                if (fileExists.exists) {
                                    const fileStats = await window.electronAPI.fileSystem.getFileStats(manifestFile);
                                    if (fileStats.success && fileStats.stats?.size > 0) {
                                        this.logger.warn('ModuleRegistry', `Physical manifest file exists and has content (${fileStats.stats.size} bytes) - SKIPPING deletion for ${moduleId}`);
                                        this.logger.warn('ModuleRegistry', `Module ${moduleId} will remain in filesystem but be unregistered from active modules`);
                                        await window.modulePersistenceClient.unregisterModule(moduleId);
                                        continue;
                                    }
                                }
                            } catch (checkError) {
                                this.logger.error('ModuleRegistry', `Error checking physical file for ${moduleId}:`, checkError);
                            }
                            
                            // Only cleanup if file is truly corrupted or doesn't exist
                            await this.cleanupCorruptedModule(moduleId, manifestFile);
                            await window.modulePersistenceClient.unregisterModule(moduleId);
                            continue;
                        }
                    }

                    // Validate version matches database
                    if (manifest.version !== dbModule.version) {
                        this.logger.warn('ModuleRegistry', `Module ${moduleId} version mismatch (DB: ${dbModule.version}, File: ${manifest.version}), updating database`);
                        await window.modulePersistenceClient.registerInstalledModule({
                            moduleId: moduleId,
                            name: manifest.name || moduleId,
                            version: manifest.version,
                            installPath: moduleDir,
                            manifestData: manifest,
                            status: dbModule.status
                        });
                    }

                    // Read module code
                    const moduleContent = await window.electronAPI.fileSystem.readFile(moduleFile);
                    const moduleCode = moduleContent.content;

                    // Read CSS if exists
                    const cssFile = `${moduleDir}/${moduleId}.css`;
                    const cssFileExists = await window.electronAPI.fileSystem.exists(cssFile);
                    let cssContent = '';
                    if (cssFileExists.exists) {
                        const cssFileContent = await window.electronAPI.fileSystem.readFile(cssFile);
                        cssContent = cssFileContent.content;
                    }

                    // Recreate the module class
                    await this.recreateModuleClass(moduleId, { manifest, moduleCode });

                    // Create package data
                    const packageData = {
                        manifest: manifest,
                        moduleCode: moduleCode,
                        styles: cssContent,
                        assets: {}
                    };

                    // Create ModulePackage instance
                    if (window.ModulePackage) {
                        const modulePackage = new window.ModulePackage(packageData, {
                            validateManifest: true,
                            strictMode: false,
                            allowUnsafeCode: true
                        });

                        // Mark as installed and set install path
                        modulePackage.isInstalled = true;
                        modulePackage.isInitialized = true;
                        modulePackage.installPath = moduleDir;
                        modulePackage.installedAt = dbModule.installed_at;

                        // Store in registry
                        this.modules.set(moduleId, modulePackage);

                        this.logger.info('ModuleRegistry', `Successfully loaded installed module: ${moduleId}@${manifest.version}`);
                    }

                } catch (error) {
                    const safeModuleId = dbModule?.moduleId || dbModule?.module_id || 'unknown';
                    this.logger.error('ModuleRegistry', `Error loading module ${safeModuleId}:`, error);
                    
                    // If there's an error loading this module, clean it up
                    // Try multiple approaches to clean up corrupted data
                    try {
                        if (safeModuleId && safeModuleId !== 'unknown' && safeModuleId !== 'undefined' && safeModuleId !== 'null') {
                            await window.modulePersistenceClient.unregisterModule(safeModuleId);
                            this.logger.info('ModuleRegistry', `Unregistered corrupted module: ${safeModuleId}`);
                        }
                        if (dbModule?.id) {
                            await window.modulePersistenceClient.unregisterModule(dbModule.id);
                            this.logger.info('ModuleRegistry', `Unregistered corrupted module by ID: ${dbModule.id}`);
                        }
                    } catch (cleanupError) {
                        this.logger.warn('ModuleRegistry', `Failed to cleanup corrupted module ${safeModuleId}:`, cleanupError);
                        
                        // Try marking as inactive as last resort
                        try {
                            if (window.modulePersistenceClient.updateModuleStatus && safeModuleId !== 'unknown' && safeModuleId !== 'undefined' && safeModuleId !== 'null') {
                                await window.modulePersistenceClient.updateModuleStatus(safeModuleId, 'inactive');
                                this.logger.info('ModuleRegistry', `Marked corrupted module as inactive: ${safeModuleId}`);
                            }
                        } catch (statusError) {
                            this.logger.warn('ModuleRegistry', `Failed to mark module ${safeModuleId} as inactive:`, statusError);
                        }
                    }
                }
            }

            // Scan for orphaned modules (files exist but not in database)
            await this.scanForOrphanedModules(modulesPath);

            this.logger.info('ModuleRegistry', `Loaded ${this.modules.size} installed modules from database`);

        } catch (error) {
            this.logger.error('ModuleRegistry', 'Failed to load installed modules from database:', error);
            // Fallback to filesystem scan
            this.logger.info('ModuleRegistry', 'Falling back to filesystem scan...');
            await this.loadInstalledModulesFromFilesystem();
        }
    }

    /**
     * Fallback method to load modules from filesystem when database is not available
     * @private
     */
    async loadInstalledModulesFromFilesystem() {
        try {
            // Check filesystem for installed modules
            const modulesPathResult = await window.electronAPI.fileSystem.getModulesPath();
            if (!modulesPathResult.success) {
                throw new Error(`Failed to get modules path: ${modulesPathResult.error}`);
            }
            const modulesPath = modulesPathResult.path;

            // Check if modules directory exists
            const modulesDirExists = await window.electronAPI.fileSystem.exists(modulesPath);
            if (!modulesDirExists.exists) {
                this.logger.info('ModuleRegistry', 'No modulos directory found - creating it');
                await window.electronAPI.fileSystem.createDirectory(modulesPath);
                return;
            }

            // List module directories
            const moduleDirs = await window.electronAPI.fileSystem.listDirectory(modulesPath);
            const moduleIds = moduleDirs.files.filter(file => file.isDirectory).map(dir => dir.name);

            this.logger.info('ModuleRegistry', `Found ${moduleIds.length} module directories: ${moduleIds.join(', ')}`);

            // Track modules for later database sync
            const modulesToSync = [];

            for (const moduleId of moduleIds) {
                try {
                    const moduleDir = `${modulesPath}/${moduleId}`;

                    // Check if module has required files
                    const moduleFile = `${moduleDir}/${moduleId}.js`;
                    const manifestFile = `${moduleDir}/manifest.json`;

                    const moduleFileExists = await window.electronAPI.fileSystem.exists(moduleFile);
                    const manifestFileExists = await window.electronAPI.fileSystem.exists(manifestFile);

                    if (moduleFileExists.exists && manifestFileExists.exists) {
                        // Load manifest with defensive parsing
                        const manifestContent = await window.electronAPI.fileSystem.readFile(manifestFile);
                        
                        let manifest;
                        try {
                            if (!manifestContent.content || 
                                manifestContent.content === 'undefined' || 
                                manifestContent.content === 'null' ||
                                typeof manifestContent.content !== 'string' ||
                                manifestContent.content.trim() === '') {
                                throw new Error(`Invalid manifest content: ${JSON.stringify(manifestContent.content)}`);
                            }
                            manifest = JSON.parse(manifestContent.content);
                        } catch (parseError) {
                            this.logger.error('ModuleRegistry', `Error parsing manifest for module ${moduleId}: ${parseError.message}`);
                            this.logger.warn('ModuleRegistry', `Skipping corrupted module ${moduleId} due to invalid manifest`);
                            continue;
                        }

                        // Load module code
                        const moduleContent = await window.electronAPI.fileSystem.readFile(moduleFile);
                        const moduleCode = moduleContent.content;

                        // Load styles if available
                        const stylesFile = `${moduleDir}/${moduleId}.css`;
                        const stylesFileExists = await window.electronAPI.fileSystem.exists(stylesFile);
                        let styles = '';
                        if (stylesFileExists.exists) {
                            const stylesContent = await window.electronAPI.fileSystem.readFile(stylesFile);
                            styles = stylesContent.content;
                        }

                        // Recreate the module class
                        await this.recreateModuleClass(moduleId, { manifest, moduleCode });

                        // Create package data for registration
                        const packageData = {
                            manifest: manifest,
                            moduleCode: moduleCode,
                            styles: styles,
                            assets: {}
                        };

                        // Create ModulePackage instance
                        if (window.ModulePackage) {
                            const modulePackage = new window.ModulePackage(packageData, {
                                validateManifest: true,
                                strictMode: false,
                                allowUnsafeCode: true
                            });

                            // Mark as installed and set install path
                            modulePackage.isInstalled = true;
                            modulePackage.isInitialized = true;
                            modulePackage.installPath = moduleDir;
                            modulePackage.installedAt = new Date().toISOString();

                            // Store in registry
                            this.modules.set(moduleId, modulePackage);

                            this.logger.info('ModuleRegistry', `Successfully loaded installed module: ${moduleId}`);

                            // Add to sync list
                            modulesToSync.push({
                                moduleId: moduleId,
                                name: manifest.name || moduleId,
                                version: manifest.version,
                                installPath: moduleDir,
                                manifestData: manifest,
                                status: 'active'
                            });
                        }
                    } else {
                        this.logger.warn('ModuleRegistry', `Module ${moduleId} missing required files - skipping`);
                    }
                } catch (moduleError) {
                    this.logger.error('ModuleRegistry', `Failed to load module ${moduleId}:`, moduleError);
                }
            }

            this.logger.info('ModuleRegistry', `Loaded ${this.modules.size} installed modules from filesystem`);

            // Set up database sync when it becomes available
            this.setupDatabaseSync(modulesToSync);

        } catch (error) {
            this.logger.error('ModuleRegistry', 'Failed to load installed modules from filesystem:', error);
        }
    }

    /**
     * Scan for orphaned modules (files exist but not in database) and register them
     * @private
     */
    async scanForOrphanedModules(modulesPath) {
        try {
            this.logger.debug('ModuleRegistry', 'Scanning for orphaned modules...');

            // Check if modules directory exists
            const modulesDirExists = await window.electronAPI.fileSystem.exists(modulesPath);
            if (!modulesDirExists.exists) {
                return;
            }

            // List module directories
            const moduleDirs = await window.electronAPI.fileSystem.listDirectory(modulesPath);
            const moduleIds = moduleDirs.files.filter(file => file.isDirectory).map(dir => dir.name);

            for (const moduleId of moduleIds) {
                try {
                    // Check if module is already in database
                    const isInDatabase = await window.modulePersistenceClient.isModuleInstalled(moduleId);
                    if (isInDatabase) {
                        continue; // Skip modules already in database
                    }

                    const moduleDir = `${modulesPath}/${moduleId}`;

                    // Check if module has required files
                    const moduleFile = `${moduleDir}/${moduleId}.js`;
                    const manifestFile = `${moduleDir}/manifest.json`;

                    const moduleFileExists = await window.electronAPI.fileSystem.exists(moduleFile);
                    const manifestFileExists = await window.electronAPI.fileSystem.exists(manifestFile);

                    if (moduleFileExists.exists && manifestFileExists.exists) {
                        // Read manifest with defensive parsing
                        const manifestContent = await window.electronAPI.fileSystem.readFile(manifestFile);
                        
                        let manifest;
                        try {
                            if (!manifestContent.content || 
                                manifestContent.content === 'undefined' || 
                                manifestContent.content === 'null' ||
                                typeof manifestContent.content !== 'string' ||
                                manifestContent.content.trim() === '') {
                                throw new Error(`Invalid manifest content: ${JSON.stringify(manifestContent.content)}`);
                            }
                            manifest = JSON.parse(manifestContent.content);
                        } catch (parseError) {
                            this.logger.error('ModuleRegistry', `Error parsing manifest for orphaned module ${moduleId}: ${parseError.message}`);
                            this.logger.warn('ModuleRegistry', `Skipping corrupted orphaned module ${moduleId} due to invalid manifest`);
                            continue;
                        }

                        this.logger.info('ModuleRegistry', `Found orphaned module: ${moduleId}, registering in database`);

                        // Register in database
                        const moduleData = {
                            moduleId: moduleId,
                            name: manifest.name || moduleId,
                            version: manifest.version,
                            installPath: moduleDir,
                            manifestData: manifest,
                            status: 'active'
                        };

                        await window.modulePersistenceClient.registerInstalledModule(moduleData);

                        // Also load the module into registry
                        const moduleContent = await window.electronAPI.fileSystem.readFile(moduleFile);
                        const moduleCode = moduleContent.content;

                        // Load styles if available
                        const stylesFile = `${moduleDir}/${moduleId}.css`;
                        const stylesFileExists = await window.electronAPI.fileSystem.exists(stylesFile);
                        let styles = '';
                        if (stylesFileExists.exists) {
                            const stylesContent = await window.electronAPI.fileSystem.readFile(stylesFile);
                            styles = stylesContent.content;
                        }

                        // Recreate the module class
                        await this.recreateModuleClass(moduleId, { manifest, moduleCode });

                        // Create package data
                        const packageData = {
                            manifest: manifest,
                            moduleCode: moduleCode,
                            styles: styles,
                            assets: {}
                        };

                        // Create ModulePackage instance
                        if (window.ModulePackage) {
                            const modulePackage = new window.ModulePackage(packageData, {
                                validateManifest: true,
                                strictMode: false,
                                allowUnsafeCode: true
                            });

                            // Mark as installed and set install path
                            modulePackage.isInstalled = true;
                            modulePackage.isInitialized = true;
                            modulePackage.installPath = moduleDir;
                            modulePackage.installedAt = new Date().toISOString();

                            // Store in registry
                            this.modules.set(moduleId, modulePackage);

                            this.logger.info('ModuleRegistry', `Successfully registered and loaded orphaned module: ${moduleId}`);
                        }
                    }
                } catch (moduleError) {
                    this.logger.error('ModuleRegistry', `Failed to process orphaned module ${moduleId}:`, moduleError);
                }
            }

        } catch (error) {
            this.logger.error('ModuleRegistry', 'Failed to scan for orphaned modules:', error);
        }
    }

    /**
     * Recreate module class for persisted modules
     * @private
     */
    async recreateModuleClass(moduleId, moduleData) {
        const className = `${moduleId.charAt(0).toUpperCase() + moduleId.slice(1)}Module`;
        
        // Check if class already exists
        if (window[className]) {
            this.logger.debug('ModuleRegistry', `Module class ${className} already exists`);
            return;
        }
        
        // Get access to the app's createCSPSafeModuleClass method
        const app = window.coreDeskApp || window.coreDesk?.app || window.CoreDeskAppInstance;
        if (app && typeof app.createCSPSafeModuleClass === 'function') {
            const moduleType = moduleData.moduleType || 'json';
            const size = moduleData.binarySize || 0;
            
            // Recreate the module class
            const moduleCodeRef = app.createCSPSafeModuleClass(moduleId, moduleType, size);
            this.logger.info('ModuleRegistry', `Recreated module class: ${moduleCodeRef}`);
            
            // Verify the class was created
            if (!window[className]) {
                this.logger.error('ModuleRegistry', `Failed to create module class ${className} on window`);
            } else {
                this.logger.info('ModuleRegistry', `Module class ${className} successfully attached to window`);
            }
        } else {
            this.logger.warn('ModuleRegistry', `Cannot recreate module class for ${moduleId} - createCSPSafeModuleClass not available`);
            
            // Fallback: Try to recreate the module class directly
            this.logger.info('ModuleRegistry', `Attempting fallback module class creation for ${moduleId}`);
            this.createFallbackModuleClass(moduleId, moduleData);
        }
    }
    
    /**
     * Create a fallback module class when app.createCSPSafeModuleClass is not available
     * @private
     */
    createFallbackModuleClass(moduleId, moduleData) {
        const className = `${moduleId.charAt(0).toUpperCase() + moduleId.slice(1)}Module`;
        const moduleType = moduleData.moduleType || 'json';
        const size = moduleData.binarySize || 0;
        
        // Create the module constructor
        const ModuleConstructor = function(packageData) {
            this.packageData = packageData;
            this.name = moduleId;
            this.isLoaded = false;
            console.log(`${moduleType} module ${moduleId} constructor called`);
        };
        
        ModuleConstructor.prototype.initialize = async function() {
            console.log(`${moduleType} module ${moduleId} initialized successfully`);
            this.isLoaded = true;
            return true;
        };
        
        ModuleConstructor.prototype.activate = async function() {
            console.log(`${moduleType} module ${moduleId} activated`);
            this.isActive = true;
            return true;
        };
        
        ModuleConstructor.prototype.deactivate = async function() {
            console.log(`${moduleType} module ${moduleId} deactivated`);
            this.isActive = false;
            return true;
        };
        
        ModuleConstructor.prototype.render = function() {
            const moduleContent = document.createElement('div');
            moduleContent.className = 'module-content';
            
            let headerContent = '<div class="module-header">' +
                '<h2>' + (this.name.charAt(0).toUpperCase() + this.name.slice(1)) + ' Module</h2>' +
                '<p>Type: ' + moduleType + '</p>';
            
            if (moduleType === 'binary') {
                headerContent += '<p>Size: ' + size + ' bytes</p>';
            }
            headerContent += '</div>';
            
            const bodyContent = '<div class="module-body">' +
                '<p>🚀 Module ' + this.name + ' is now active and running!</p>' +
                '<div class="module-features">' +
                    '<h3>Features:</h3>' +
                    '<ul>' +
                        '<li>✅ Successfully installed</li>' +
                        '<li>✅ Persistence enabled</li>' +
                        '<li>✅ Ready for use</li>' +
                    '</ul>' +
                '</div>' +
            '</div>';
            
            moduleContent.innerHTML = headerContent + bodyContent;
            return moduleContent;
        };
        
        ModuleConstructor.prototype.getInfo = function() {
            const info = {
                name: this.name,
                type: moduleType,
                isLoaded: this.isLoaded,
                isActive: this.isActive || false
            };
            
            if (moduleType === 'binary') {
                info.size = size;
            }
            
            return info;
        };
        
        // Attach to window for CSP-safe access
        window[className] = ModuleConstructor;
        
        this.logger.info('ModuleRegistry', `Fallback module class ${className} created and attached to window`);
    }

    /**
     * Persist module data
     * @private
     */
    async persistModuleData(moduleId, metadata) {
        try {
            const registryKey = `${this.options.storagePrefix}registry`;
            const registryData = {
                version: '1.0.0',
                timestamp: Date.now(),
                metadata: Object.fromEntries(this.moduleMetadata)
            };

            this.storage.setItem(registryKey, JSON.stringify(registryData));

        } catch (error) {
            this.logger.warn('ModuleRegistry', 'Failed to persist module data:', error);
        }
    }

    /**
     * Remove persisted module data
     * @private
     */
    async removePersistedModuleData(moduleId) {
        try {
            // Update registry data without this module
            const registryKey = `${this.options.storagePrefix}registry`;
            const registryData = {
                version: '1.0.0',
                timestamp: Date.now(),
                metadata: Object.fromEntries(this.moduleMetadata)
            };

            this.storage.setItem(registryKey, JSON.stringify(registryData));

        } catch (error) {
            this.logger.warn('ModuleRegistry', 'Failed to remove persisted data:', error);
        }
    }

    /**
     * Extract module metadata
     * @private
     */
    extractModuleMetadata(modulePackage) {
        return {
            id: modulePackage.id,
            name: modulePackage.name,
            version: modulePackage.version,
            description: modulePackage.description,
            author: modulePackage.manifest.author,
            license: modulePackage.manifest.license,
            category: modulePackage.manifest.category || 'general',
            tags: modulePackage.manifest.tags || [],
            dependencies: modulePackage.dependencies,
            permissions: modulePackage.requiredPermissions,
            requiredLicense: modulePackage.requiredLicense,
            size: modulePackage.getPackageSize ? modulePackage.getPackageSize() : 0,
            isInstalled: modulePackage.isInstalled,
            installedAt: modulePackage.installedAt,
            downloadUrl: modulePackage.manifest.downloadUrl,
            homepage: modulePackage.manifest.homepage,
            repository: modulePackage.manifest.repository
        };
    }

    /**
     * Validate module package
     * @private
     */
    async validateModulePackage(modulePackage) {
        if (!modulePackage.id || !modulePackage.version) {
            throw new Error('Module package missing required fields');
        }

        if (this.options.validateIntegrity) {
            // Additional integrity checks
            if (!modulePackage.manifest) {
                throw new Error('Module package missing manifest');
            }
        }
    }

    /**
     * Update dependency tracking
     * @private
     */
    async updateDependencyTracking(moduleId, modulePackage) {
        // Track what modules this one depends on
        if (modulePackage.dependencies) {
            const deps = new Set();
            for (const depId of Object.keys(modulePackage.dependencies)) {
                if (depId !== 'coredesk') {
                    deps.add(depId);
                }
            }
            this.dependencies.set(moduleId, deps);
        }
    }

    /**
     * Remove dependency tracking
     * @private
     */
    removeDependencyTracking(moduleId) {
        this.dependencies.delete(moduleId);
    }

    /**
     * Update category index
     * @private
     */
    updateCategoryIndex(moduleId, metadata) {
        const category = metadata.category || 'general';
        if (!this.categories.has(category)) {
            this.categories.set(category, new Set());
        }
        this.categories.get(category).add(moduleId);
    }

    /**
     * Remove from category index
     * @private
     */
    removeFromCategoryIndex(moduleId) {
        for (const [category, moduleSet] of this.categories) {
            moduleSet.delete(moduleId);
            if (moduleSet.size === 0) {
                this.categories.delete(category);
            }
        }
    }

    /**
     * Update tag index
     * @private
     */
    updateTagIndex(moduleId, metadata) {
        if (metadata.tags) {
            for (const tag of metadata.tags) {
                if (!this.tags.has(tag)) {
                    this.tags.set(tag, new Set());
                }
                this.tags.get(tag).add(moduleId);
            }
        }
    }

    /**
     * Remove from tag index
     * @private
     */
    removeFromTagIndex(moduleId) {
        for (const [tag, moduleSet] of this.tags) {
            moduleSet.delete(moduleId);
            if (moduleSet.size === 0) {
                this.tags.delete(tag);
            }
        }
    }

    /**
     * Update search index
     * @private
     */
    updateSearchIndex(moduleId, metadata) {
        const searchTerms = [
            metadata.id,
            metadata.name,
            metadata.description,
            ...(metadata.tags || []),
            metadata.category
        ].filter(Boolean);

        for (const term of searchTerms) {
            const termLower = term.toLowerCase();
            if (!this.searchIndex.has(termLower)) {
                this.searchIndex.set(termLower, new Set());
            }
            this.searchIndex.get(termLower).add(moduleId);
        }
    }

    /**
     * Remove from search index
     * @private
     */
    removeFromSearchIndex(moduleId) {
        for (const [term, moduleSet] of this.searchIndex) {
            moduleSet.delete(moduleId);
            if (moduleSet.size === 0) {
                this.searchIndex.delete(term);
            }
        }
    }

    /**
     * Build complete search index
     * @private
     */
    buildSearchIndex() {
        this.searchIndex.clear();
        for (const [moduleId, metadata] of this.moduleMetadata) {
            this.updateSearchIndex(moduleId, metadata);
        }
    }

    /**
     * Clear all indexes
     * @private
     */
    clearAllIndexes() {
        this.categories.clear();
        this.tags.clear();
        this.searchIndex.clear();
        this.dependencies.clear();
    }

    /**
     * Check if filters match metadata
     * @private
     */
    matchesFilters(metadata, filters) {
        if (filters.category && metadata.category !== filters.category) {
            return false;
        }

        if (filters.tag && (!metadata.tags || !metadata.tags.includes(filters.tag))) {
            return false;
        }

        if (filters.license && metadata.license !== filters.license) {
            return false;
        }

        return true;
    }

    /**
     * Check if version is newer
     * @private
     */
    isVersionNewer(newVersion, oldVersion) {
        const newParts = newVersion.split('.').map(Number);
        const oldParts = oldVersion.split('.').map(Number);

        for (let i = 0; i < Math.max(newParts.length, oldParts.length); i++) {
            const newPart = newParts[i] || 0;
            const oldPart = oldParts[i] || 0;

            if (newPart > oldPart) return true;
            if (newPart < oldPart) return false;
        }

        return false;
    }

    /**
     * Check if version is compatible
     * @private
     */
    isVersionCompatible(required, installed) {
        // Simple version compatibility check
        if (required.startsWith('>=')) {
            return installed >= required.slice(2);
        }
        if (required.startsWith('^')) {
            const baseVersion = required.slice(1);
            const [reqMajor] = baseVersion.split('.');
            const [instMajor] = installed.split('.');
            return instMajor === reqMajor && installed >= baseVersion;
        }
        return installed === required;
    }

    /**
     * Check if persisted data is valid
     * @private
     */
    isDataValid(data) {
        if (!data.timestamp) return false;
        const age = Date.now() - data.timestamp;
        return age < this.options.cacheTTL;
    }

    /**
     * Setup automatic cleanup
     * @private
     */
    setupAutoCleanup() {
        setInterval(() => {
            this.performCleanup();
        }, 60 * 60 * 1000); // Every hour
    }

    /**
     * Perform cleanup tasks
     * @private
     */
    performCleanup() {
        try {
            // Clean up empty index entries
            this.cleanupEmptyIndexes();

            // Clean up expired cache entries
            this.cleanupExpiredCache();

            this.logger.debug('ModuleRegistry', 'Cleanup completed');

        } catch (error) {
            this.logger.warn('ModuleRegistry', 'Cleanup failed:', error);
        }
    }

    /**
     * Clean up empty indexes
     * @private
     */
    cleanupEmptyIndexes() {
        for (const [key, set] of this.categories) {
            if (set.size === 0) {
                this.categories.delete(key);
            }
        }

        for (const [key, set] of this.tags) {
            if (set.size === 0) {
                this.tags.delete(key);
            }
        }

        for (const [key, set] of this.searchIndex) {
            if (set.size === 0) {
                this.searchIndex.delete(key);
            }
        }
    }

    /**
     * Clean up expired cache
     * @private
     */
    cleanupExpiredCache() {
        // Clean up old localStorage entries
        const allKeys = this.storage.getAllKeys();
        const now = Date.now();

        for (const key of allKeys) {
            try {
                const storageValue = this.storage.getItem(key);
                
                // Defensive JSON parsing for localStorage
                if (!storageValue || 
                    storageValue === 'undefined' || 
                    storageValue === 'null' ||
                    typeof storageValue !== 'string') {
                    this.logger.warn('ModuleRegistry', `Removing invalid localStorage entry: ${key}`);
                    this.storage.removeItem(key);
                    continue;
                }
                
                const data = JSON.parse(storageValue);
                if (data.timestamp && (now - data.timestamp) > this.options.cacheTTL) {
                    this.storage.removeItem(key);
                }
            } catch {
                // Invalid data, remove it
                this.storage.removeItem(key);
            }
        }
    }

    /**
     * Set up database synchronization for modules loaded from filesystem
     * @param {Array} modulesToSync - Array of module data to sync with database
     * @private
     */
    setupDatabaseSync(modulesToSync) {
        if (!modulesToSync || modulesToSync.length === 0) {
            return;
        }

        this.logger.info('ModuleRegistry', `Setting up database sync for ${modulesToSync.length} modules`);

        // Attempt immediate sync if database is available
        this.attemptDatabaseSync(modulesToSync);

        // Also listen for database ready event in case it's not ready yet
        if (window.electronAPI && window.electronAPI.on) {
            window.electronAPI.on('database:ready', async () => {
                this.logger.info('ModuleRegistry', 'Database ready event received, syncing modules...');
                await this.attemptDatabaseSync(modulesToSync);
            });
        }
    }

    /**
     * Attempt to sync modules with database
     * @param {Array} modulesToSync - Array of module data to sync
     * @private
     */
    async attemptDatabaseSync(modulesToSync) {
        try {
            // Check if persistence client is available
            if (!window.modulePersistenceClient) {
                if (window.ModulePersistenceClient) {
                    window.modulePersistenceClient = new window.ModulePersistenceClient(this.logger);
                    const initialized = await window.modulePersistenceClient.initialize();
                    if (!initialized) {
                        this.logger.warn('ModuleRegistry', 'Failed to initialize persistence client for sync');
                        return;
                    }
                } else {
                    this.logger.warn('ModuleRegistry', 'ModulePersistenceClient not available for sync');
                    return;
                }
            }

            // Sync each module
            let syncedCount = 0;
            for (const moduleData of modulesToSync) {
                try {
                    // Check if module is already in database
                    const isInDatabase = await window.modulePersistenceClient.isModuleInstalled(moduleData.moduleId);
                    if (!isInDatabase) {
                        // Register in database
                        const registered = await window.modulePersistenceClient.registerInstalledModule(moduleData);
                        if (registered) {
                            syncedCount++;
                            this.logger.info('ModuleRegistry', `Synced module ${moduleData.moduleId} to database`);
                        }
                    }
                } catch (error) {
                    this.logger.error('ModuleRegistry', `Failed to sync module ${moduleData.moduleId}:`, error);
                }
            }

            if (syncedCount > 0) {
                this.logger.info('ModuleRegistry', `Successfully synced ${syncedCount} modules to database`);
            }

        } catch (error) {
            this.logger.error('ModuleRegistry', 'Failed to sync modules with database:', error);
        }
    }

    /**
     * Validate and sanitize manifest data to prevent corruption
     * @private
     */
    validateAndSanitizeManifest(manifest, moduleId) {
        if (!manifest || typeof manifest !== 'object') {
            this.logger.error('ModuleRegistry', `Manifest is not a valid object for module ${moduleId}:`, manifest);
            return null;
        }

        // Create a sanitized copy of the manifest
        const sanitizedManifest = {};

        // Define required fields with defaults
        const requiredFields = {
            id: moduleId || 'unknown-module',
            name: manifest.name || moduleId || 'Unknown Module',
            version: manifest.version || '1.0.0',
            description: manifest.description || 'No description provided'
        };

        // Copy required fields, using defaults if undefined/null
        for (const [key, defaultValue] of Object.entries(requiredFields)) {
            const value = manifest[key];
            if (value === undefined || value === null || value === 'undefined' || value === 'null') {
                this.logger.warn('ModuleRegistry', `Manifest field '${key}' is undefined/null for module ${moduleId}, using default: ${defaultValue}`);
                sanitizedManifest[key] = defaultValue;
            } else if (typeof value === 'string' && value.trim() !== '') {
                sanitizedManifest[key] = value;
            } else if (typeof value === 'number' || typeof value === 'boolean') {
                sanitizedManifest[key] = value;
            } else {
                this.logger.warn('ModuleRegistry', `Manifest field '${key}' has invalid type for module ${moduleId}, using default: ${defaultValue}`);
                sanitizedManifest[key] = defaultValue;
            }
        }

        // Copy optional fields, excluding undefined/null values
        const optionalFields = [
            'author', 'license', 'homepage', 'repository', 'keywords', 'category', 'tags',
            'dependencies', 'permissions', 'requiredLicense', 'browserRequirements',
            'main', 'styles', 'icon', 'screenshots', 'changelog', 'downloadUrl'
        ];

        for (const field of optionalFields) {
            const value = manifest[field];
            if (value !== undefined && value !== null && value !== 'undefined' && value !== 'null') {
                // For objects and arrays, ensure they're not empty or corrupted
                if (typeof value === 'object') {
                    if (Array.isArray(value)) {
                        if (value.length > 0) {
                            sanitizedManifest[field] = value;
                        }
                    } else if (Object.keys(value).length > 0) {
                        sanitizedManifest[field] = value;
                    }
                } else if (typeof value === 'string' && value.trim() !== '') {
                    sanitizedManifest[field] = value;
                } else if (typeof value === 'number' || typeof value === 'boolean') {
                    sanitizedManifest[field] = value;
                }
            }
        }

        // Validate the sanitized manifest can be serialized
        try {
            const testSerialization = JSON.stringify(sanitizedManifest);
            if (!testSerialization || testSerialization === 'undefined' || testSerialization === 'null') {
                this.logger.error('ModuleRegistry', `Sanitized manifest fails serialization test for module ${moduleId}`);
                return null;
            }
        } catch (error) {
            this.logger.error('ModuleRegistry', `Sanitized manifest serialization failed for module ${moduleId}:`, error);
            return null;
        }

        this.logger.info('ModuleRegistry', `Manifest validation successful for module ${moduleId}`);
        return sanitizedManifest;
    }

    /**
     * Attempt to recover a corrupted module by recreating the manifest
     * @private
     */
    async attemptModuleRecovery(moduleId, dbModule, manifestFile) {
        try {
            this.logger.info('ModuleRegistry', `Starting recovery process for module ${moduleId}`);
            this.logger.debug('ModuleRegistry', `Database module data:`, dbModule);
            this.logger.debug('ModuleRegistry', `Manifest file path: ${manifestFile}`);
            
            // Strategy 1: Try to recreate manifest from database metadata
            if (dbModule && (dbModule.manifestData || dbModule.manifest_data)) {
                this.logger.info('ModuleRegistry', `Attempting recovery using database metadata for ${moduleId}`);
                
                const manifestData = dbModule.manifestData || dbModule.manifest_data;
                this.logger.debug('ModuleRegistry', `Raw manifest data from database:`, manifestData);
                
                // Handle case where manifestData is a string (JSON)
                let parsedManifestData;
                if (typeof manifestData === 'string') {
                    try {
                        if (manifestData === 'undefined' || manifestData === 'null' || manifestData.trim() === '') {
                            this.logger.warn('ModuleRegistry', `Database manifest data is invalid string: "${manifestData}"`);
                            parsedManifestData = null;
                        } else {
                            parsedManifestData = JSON.parse(manifestData);
                            this.logger.debug('ModuleRegistry', `Successfully parsed manifest data from database:`, parsedManifestData);
                        }
                    } catch (parseError) {
                        this.logger.warn('ModuleRegistry', `Failed to parse manifest data from database for ${moduleId}: ${parseError.message}`);
                        parsedManifestData = null;
                    }
                } else if (typeof manifestData === 'object' && manifestData !== null) {
                    parsedManifestData = manifestData;
                    this.logger.debug('ModuleRegistry', `Using object manifest data from database:`, parsedManifestData);
                } else {
                    this.logger.warn('ModuleRegistry', `Invalid manifest data type from database: ${typeof manifestData}, value:`, manifestData);
                    parsedManifestData = null;
                }
                
                if (parsedManifestData && parsedManifestData.id) {
                    // Recreate manifest with validated data
                    const recoveredManifest = this.createRecoveredManifest(moduleId, dbModule, parsedManifestData);
                    
                    if (recoveredManifest) {
                        try {
                            const manifestContent = JSON.stringify(recoveredManifest, null, 2);
                            this.logger.debug('ModuleRegistry', `Writing recovered manifest content:`, manifestContent);
                            
                            const writeResult = await window.electronAPI.fileSystem.writeFile(manifestFile, manifestContent);
                            this.logger.debug('ModuleRegistry', `Write result:`, writeResult);
                            
                            if (writeResult && writeResult.success) {
                                this.logger.info('ModuleRegistry', `Successfully recreated manifest from database for ${moduleId}`);
                                
                                // Verify the recreated manifest
                                await new Promise(resolve => setTimeout(resolve, 1000)); // Wait for file system sync
                                const verifyResult = await window.electronAPI.fileSystem.readFile(manifestFile);
                                this.logger.debug('ModuleRegistry', `Verification read result:`, verifyResult);
                                
                                if (verifyResult.success && verifyResult.content === manifestContent) {
                                    this.logger.info('ModuleRegistry', `Manifest recreation verified for ${moduleId}`);
                                    return true;
                                } else {
                                    this.logger.warn('ModuleRegistry', `Manifest recreation verification failed for ${moduleId}. Expected content length: ${manifestContent.length}, actual: ${verifyResult.content?.length}`);
                                }
                            } else {
                                this.logger.error('ModuleRegistry', `Failed to write recovered manifest for ${moduleId}:`, writeResult);
                            }
                        } catch (writeError) {
                            this.logger.error('ModuleRegistry', `Exception writing recovered manifest for ${moduleId}:`, writeError);
                        }
                    } else {
                        this.logger.warn('ModuleRegistry', `Failed to create recovered manifest from database data for ${moduleId}`);
                    }
                } else {
                    this.logger.warn('ModuleRegistry', `Invalid or missing manifest data ID in database for ${moduleId}`);
                }
            } else {
                this.logger.warn('ModuleRegistry', `No manifest data found in database for ${moduleId}`);
            }
            
            // Strategy 2: Try to recreate manifest from localStorage if available
            this.logger.info('ModuleRegistry', `Attempting recovery using localStorage for ${moduleId}`);
            const storageKey = `coredesk_module_${moduleId}`;
            const localStorageData = localStorage.getItem(storageKey);
            this.logger.debug('ModuleRegistry', `LocalStorage data for key ${storageKey}:`, localStorageData);
            
            if (localStorageData && localStorageData !== 'undefined' && localStorageData !== 'null') {
                try {
                    const parsedLocalData = JSON.parse(localStorageData);
                    this.logger.debug('ModuleRegistry', `Parsed localStorage data:`, parsedLocalData);
                    
                    if (parsedLocalData.manifestData || parsedLocalData.originalPackageData?.manifest) {
                        const manifestData = parsedLocalData.manifestData || parsedLocalData.originalPackageData.manifest;
                        this.logger.debug('ModuleRegistry', `Found manifest data in localStorage:`, manifestData);
                        
                        if (manifestData && manifestData.id) {
                            const recoveredManifest = this.createRecoveredManifest(moduleId, dbModule, manifestData);
                            
                            if (recoveredManifest) {
                                try {
                                    const manifestContent = JSON.stringify(recoveredManifest, null, 2);
                                    const writeResult = await window.electronAPI.fileSystem.writeFile(manifestFile, manifestContent);
                                    
                                    if (writeResult && writeResult.success) {
                                        this.logger.info('ModuleRegistry', `Successfully recreated manifest from localStorage for ${moduleId}`);
                                        
                                        // Verify the recreated manifest
                                        await new Promise(resolve => setTimeout(resolve, 1000));
                                        const verifyResult = await window.electronAPI.fileSystem.readFile(manifestFile);
                                        
                                        if (verifyResult.success && verifyResult.content === manifestContent) {
                                            this.logger.info('ModuleRegistry', `Manifest recreation from localStorage verified for ${moduleId}`);
                                            return true;
                                        }
                                    }
                                } catch (writeError) {
                                    this.logger.error('ModuleRegistry', `Failed to write recovered manifest from localStorage for ${moduleId}:`, writeError);
                                }
                            }
                        }
                    } else {
                        this.logger.warn('ModuleRegistry', `No manifest data found in localStorage for ${moduleId}`);
                    }
                } catch (parseError) {
                    this.logger.warn('ModuleRegistry', `Failed to parse localStorage data for ${moduleId}:`, parseError);
                }
            } else {
                this.logger.warn('ModuleRegistry', `No valid localStorage data found for ${moduleId}`);
            }
            
            // Strategy 3: Create minimal manifest from available data
            this.logger.info('ModuleRegistry', `Attempting recovery with minimal manifest for ${moduleId}`);
            const minimalManifest = this.createMinimalManifest(moduleId, dbModule);
            this.logger.debug('ModuleRegistry', `Created minimal manifest:`, minimalManifest);
            
            if (minimalManifest) {
                try {
                    const manifestContent = JSON.stringify(minimalManifest, null, 2);
                    const writeResult = await window.electronAPI.fileSystem.writeFile(manifestFile, manifestContent);
                    
                    if (writeResult && writeResult.success) {
                        this.logger.info('ModuleRegistry', `Successfully created minimal manifest for ${moduleId}`);
                        
                        // Verify the minimal manifest
                        await new Promise(resolve => setTimeout(resolve, 1000));
                        const verifyResult = await window.electronAPI.fileSystem.readFile(manifestFile);
                        
                        if (verifyResult.success && verifyResult.content === manifestContent) {
                            this.logger.info('ModuleRegistry', `Minimal manifest creation verified for ${moduleId}`);
                            return true;
                        }
                    }
                } catch (writeError) {
                    this.logger.error('ModuleRegistry', `Failed to write minimal manifest for ${moduleId}:`, writeError);
                }
            } else {
                this.logger.error('ModuleRegistry', `Failed to create minimal manifest for ${moduleId}`);
            }
            
            this.logger.error('ModuleRegistry', `All recovery strategies failed for ${moduleId}`);
            return false;
            
        } catch (error) {
            this.logger.error('ModuleRegistry', `Recovery attempt failed for ${moduleId}:`, error);
            return false;
        }
    }

    /**
     * Create a recovered manifest from available data
     * @private
     */
    createRecoveredManifest(moduleId, dbModule, manifestData) {
        try {
            const recoveredManifest = {
                id: moduleId,
                name: manifestData.name || dbModule.name || moduleId,
                version: manifestData.version || dbModule.version || '1.0.0',
                description: manifestData.description || 'Recovered module',
                main: manifestData.main || `${moduleId}.js`,
                author: manifestData.author || 'Unknown',
                license: manifestData.license || 'Unknown',
                category: manifestData.category || 'general',
                tags: manifestData.tags || [],
                dependencies: manifestData.dependencies || {},
                permissions: manifestData.permissions || [],
                homepage: manifestData.homepage || '',
                repository: manifestData.repository || '',
                keywords: manifestData.keywords || [],
                // Add recovery metadata
                recovered: true,
                recoveredAt: new Date().toISOString(),
                recoverySource: 'database'
            };
            
            // Copy any additional fields from original manifest
            for (const [key, value] of Object.entries(manifestData)) {
                if (!recoveredManifest.hasOwnProperty(key) && 
                    value !== undefined && 
                    value !== null && 
                    value !== 'undefined' && 
                    value !== 'null') {
                    recoveredManifest[key] = value;
                }
            }
            
            this.logger.debug('ModuleRegistry', `Created recovered manifest for ${moduleId}:`, recoveredManifest);
            return recoveredManifest;
            
        } catch (error) {
            this.logger.error('ModuleRegistry', `Failed to create recovered manifest for ${moduleId}:`, error);
            return null;
        }
    }

    /**
     * Create a minimal manifest when no other data is available
     * @private
     */
    createMinimalManifest(moduleId, dbModule) {
        try {
            const minimalManifest = {
                id: moduleId,
                name: dbModule?.name || moduleId,
                version: dbModule?.version || '1.0.0',
                description: 'Recovered module with minimal manifest',
                main: `${moduleId}.js`,
                author: 'Unknown',
                license: 'Unknown',
                category: 'general',
                tags: [],
                dependencies: {},
                permissions: [],
                // Recovery metadata
                recovered: true,
                recoveredAt: new Date().toISOString(),
                recoverySource: 'minimal',
                recoveryNote: 'This module was recovered with minimal data due to manifest corruption'
            };
            
            this.logger.info('ModuleRegistry', `Created minimal manifest for ${moduleId}`);
            return minimalManifest;
            
        } catch (error) {
            this.logger.error('ModuleRegistry', `Failed to create minimal manifest for ${moduleId}:`, error);
            return null;
        }
    }

    /**
     * Clean up corrupted module files and data
     * @private
     */
    async cleanupCorruptedModule(moduleId, manifestFile) {
        try {
            this.logger.info('ModuleRegistry', `Evaluating cleanup for module ${moduleId}`);
            
            // IMPROVED: Before deleting anything, perform comprehensive validation
            if (manifestFile && window.electronAPI?.fileSystem?.exists) {
                const fileExists = await window.electronAPI.fileSystem.exists(manifestFile);
                if (fileExists.exists) {
                    // Check if the file has content and appears valid
                    try {
                        const fileStats = await window.electronAPI.fileSystem.getFileStats(manifestFile);
                        if (fileStats.success && fileStats.stats?.size > 0) {
                            this.logger.warn('ModuleRegistry', `PROTECTION: Manifest file exists with ${fileStats.stats.size} bytes - attempting to read before deletion`);
                            
                            // Try to read and validate the content one more time
                            const manifestContent = await window.electronAPI.fileSystem.readFile(manifestFile);
                            if (manifestContent.success && 
                                manifestContent.content && 
                                manifestContent.content !== 'undefined' &&
                                manifestContent.content.trim() !== '') {
                                
                                try {
                                    const parsedContent = JSON.parse(manifestContent.content);
                                    if (parsedContent && typeof parsedContent === 'object' && parsedContent.id === moduleId) {
                                        this.logger.warn('ModuleRegistry', `PROTECTION: Manifest is actually VALID - CANCELLING cleanup for ${moduleId}`);
                                        this.logger.warn('ModuleRegistry', `Module ${moduleId} manifest contains valid JSON with correct ID. The issue may be elsewhere.`);
                                        return; // Cancel cleanup - file is actually valid
                                    }
                                } catch (parseError) {
                                    this.logger.warn('ModuleRegistry', `Manifest exists but contains invalid JSON: ${parseError.message}`);
                                }
                            }
                        }
                        
                        // Only proceed with deletion if file is truly corrupted
                        this.logger.info('ModuleRegistry', `Proceeding with deletion of genuinely corrupted manifest: ${manifestFile}`);
                        await window.electronAPI.fileSystem.deleteFile(manifestFile);
                        this.logger.info('ModuleRegistry', `Deleted corrupted manifest file: ${manifestFile}`);
                    } catch (deleteError) {
                        this.logger.error('ModuleRegistry', `Failed to delete corrupted manifest file ${manifestFile}:`, deleteError);
                    }
                }
            }
            
            // Clean up the entire module directory if it's corrupted
            if (window.electronAPI?.fileSystem) {
                try {
                    const modulesPathResult = await window.electronAPI.fileSystem.getModulesPath();
                    if (modulesPathResult.success) {
                        const moduleDir = `${modulesPathResult.path}/${moduleId}`;
                        const moduleDirExists = await window.electronAPI.fileSystem.exists(moduleDir);
                        
                        if (moduleDirExists.exists) {
                            // Check if other essential files exist and are valid
                            const moduleFile = `${moduleDir}/${moduleId}.js`;
                            const moduleFileExists = await window.electronAPI.fileSystem.exists(moduleFile);
                            
                            if (!moduleFileExists.exists) {
                                // If module JS file doesn't exist, the whole directory is corrupt
                                this.logger.warn('ModuleRegistry', `Module directory ${moduleDir} is corrupted (missing JS file), removing entire directory`);
                                try {
                                    await window.electronAPI.fileSystem.deleteDirectory(moduleDir);
                                    this.logger.info('ModuleRegistry', `Removed corrupted module directory: ${moduleDir}`);
                                } catch (dirDeleteError) {
                                    this.logger.error('ModuleRegistry', `Failed to remove corrupted module directory ${moduleDir}:`, dirDeleteError);
                                }
                            }
                        }
                    }
                } catch (pathError) {
                    this.logger.warn('ModuleRegistry', `Failed to get modules path for cleanup: ${pathError.message}`);
                }
            }
            
            // Clean up localStorage entries for this module
            const storageKeys = [
                `coredesk_module_${moduleId}`,
                `${this.options.storagePrefix}module_${moduleId}`,
                `${this.options.storagePrefix}metadata_${moduleId}`
            ];
            
            for (const storageKey of storageKeys) {
                if (localStorage.getItem(storageKey)) {
                    localStorage.removeItem(storageKey);
                    this.logger.info('ModuleRegistry', `Cleaned up localStorage entry: ${storageKey}`);
                }
            }
            
            // Clean up any registry localStorage that might contain corrupted references
            const registryKey = `${this.options.storagePrefix}registry`;
            const registryData = localStorage.getItem(registryKey);
            if (registryData) {
                try {
                    const parsed = JSON.parse(registryData);
                    if (parsed.metadata && parsed.metadata[moduleId]) {
                        delete parsed.metadata[moduleId];
                        localStorage.setItem(registryKey, JSON.stringify(parsed));
                        this.logger.info('ModuleRegistry', `Removed ${moduleId} from registry localStorage`);
                    }
                } catch (parseError) {
                    // If registry data is corrupted, remove it entirely
                    localStorage.removeItem(registryKey);
                    this.logger.info('ModuleRegistry', `Removed corrupted registry localStorage entry`);
                }
            }
            
            // Remove from internal registry data structures
            this.modules.delete(moduleId);
            this.moduleMetadata.delete(moduleId);
            this.removeFromCategoryIndex(moduleId);
            this.removeFromTagIndex(moduleId);
            this.removeFromSearchIndex(moduleId);
            this.removeDependencyTracking(moduleId);
            
            this.logger.info('ModuleRegistry', `Cleanup completed for corrupted module ${moduleId}`);
            
        } catch (error) {
            this.logger.error('ModuleRegistry', `Error cleaning up corrupted module ${moduleId}:`, error);
        }
    }
}

// Make available globally
window.ModuleRegistry = ModuleRegistry;

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ModuleRegistry;
}

console.log('ModuleRegistry', '[ModuleRegistry] Class loaded successfully');