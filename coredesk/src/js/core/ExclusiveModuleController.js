/**
 * Exclusive Module Controller
 * Implements the one-module-at-a-time pattern as specified in PRD section 3.1
 * Only one module can be active at any given time to ensure focused work experience
 * 
 * REFACTORED FOR DYNAMIC MODULES v2.0:
 * - Integrates with DynamicModuleManager for module lifecycle
 * - Supports dynamic installation/uninstallation
 * - Unified architecture (legacy support removed)
 */

class ExclusiveModuleController {
    constructor() {
        this.currentModule = null;
        this.availableModules = []; // Will be populated dynamically
        this.moduleInstances = new Map();
        this.eventBus = window.CoreDeskEvents?.createNamespace('module') || {
            emit: () => {},
            on: () => {},
            off: () => {}
        };
        this.storageKey = window.COREDESK_CONSTANTS?.STORAGE_KEYS?.ACTIVE_MODULE || 'coredesk_active_module';
        
        // Dynamic module system integration
        this.dynamicModuleManager = null;
        
        this.initialize();
    }

    /**
     * Initialize the module controller
     */
    async initialize() {
        console.log('ModuleController', '[ExclusiveModuleController] Initializing...', );
        
        // Initialize immediately without waiting for database
        // Database integration will be added when available
        await this.completeInitialization();
        
        // Set up database integration for when it becomes available
        this.setupDatabaseIntegration();
    }

    /**
     * Complete the initialization (no longer requires database)
     * @private
     */
    async completeInitialization() {
        try {
            // Initialize dynamic module system
            await this.initializeDynamicSystem();
            
            // Initialize available modules list
            await this.initializeAvailableModules();
            
            // Detect currently active module
            this.detectActiveModule();
            
            // Set up event listeners
            this.setupEventListeners();
            
            // Initialize UI bindings
            this.initializeUIBindings();
            
            console.log('ModuleController', '[ExclusiveModuleController] Initialized successfully');
            console.log('ModuleController', '[ExclusiveModuleController] Using dynamic module system');
            
            if (this.currentModule) {
                console.log("Component", `[ExclusiveModuleController] Active module: ${this.currentModule}`);
            } else {
                console.log('ModuleController', '[ExclusiveModuleController] No active module detected', );
            }
        } catch (error) {
            console.error('ModuleController', '[ExclusiveModuleController] Failed to initialize:', error);
            // Continue anyway - don't block the application
        }
    }

    /**
     * Set up database integration for when it becomes available
     * @private
     */
    setupDatabaseIntegration() {
        // Check if database is already ready
        this.checkDatabaseReady().then(isReady => {
            if (isReady) {
                this.integrateDatabaseFeatures();
            }
        });

        // Listen for database ready event
        if (window.electronAPI && window.electronAPI.on) {
            window.electronAPI.on('database:ready', async () => {
                console.log('ModuleController', '[ExclusiveModuleController] Database ready event received, integrating database features...');
                await this.integrateDatabaseFeatures();
            });
        }
    }

    /**
     * Check if database is ready
     * @private
     */
    async checkDatabaseReady() {
        try {
            const result = await window.electronAPI.database.execute(
                "SELECT name FROM sqlite_master WHERE type='table' AND name='installed_modules'",
                []
            );
            return result && result.success;
        } catch (error) {
            return false;
        }
    }

    /**
     * Integrate database features when database becomes available
     * @private
     */
    async integrateDatabaseFeatures() {
        try {
            console.log('ModuleController', '[ExclusiveModuleController] Integrating database features...');
            
            // Refresh module registry to sync with database
            if (this.dynamicModuleManager && this.dynamicModuleManager.registry) {
                await this.dynamicModuleManager.registry.loadInstalledModulesFromStorage();
                console.log('ModuleController', '[ExclusiveModuleController] Database features integrated successfully');
            }
        } catch (error) {
            console.error('ModuleController', '[ExclusiveModuleController] Failed to integrate database features:', error);
        }
    }

    /**
     * Initialize dynamic module system
     * @private
     */
    async initializeDynamicSystem() {
        try {
            // Check if dynamic module system is available
            if (typeof window.DynamicModuleManager !== 'function') {
                throw new Error('DynamicModuleManager not available');
            }

            this.dynamicModuleManager = new window.DynamicModuleManager();
            
            // Initialize with default factory methods (let DynamicModuleManager create dependencies)
            await this.dynamicModuleManager.initialize();
            
            // Setup event listeners for dynamic module events
            this.setupDynamicModuleEventListeners();
            
            console.log('ModuleController', '[ExclusiveModuleController] Dynamic module system initialized');
            
        } catch (error) {
            console.error('ModuleController', '[ExclusiveModuleController] Failed to initialize dynamic system:', error);
            throw new Error(`Dynamic module system initialization failed: ${error.message}`);
        }
    }

    /**
     * Initialize available modules list
     * @private
     */
    async initializeAvailableModules() {
        // Ensure the dynamic module manager has loaded all installed modules
        await this.dynamicModuleManager.refreshRegistry();

        // Get installed modules from dynamic system
        this.availableModules = this.dynamicModuleManager.getInstalledModules();
        console.log('ModuleController', `[ExclusiveModuleController] Loaded ${this.availableModules.length} dynamic modules`);

        // If no modules found, try to load from filesystem directly
        if (this.availableModules.length === 0) {
            console.log('ModuleController', '[ExclusiveModuleController] No modules found, checking filesystem...');
            await this.loadModulesFromFilesystem();
        }
    }

    /**
     * Load modules directly from filesystem if not found in dynamic system
     * @private
     */
    async loadModulesFromFilesystem() {
        try {
            const registry = this.dynamicModuleManager.registry;
            if (registry && typeof registry.loadInstalledModulesFromStorage === 'function') {
                await registry.loadInstalledModulesFromStorage();

                // Refresh the dynamic module manager after loading from filesystem
                await this.dynamicModuleManager.loadInstalledModules();

                // Update available modules list
                this.availableModules = this.dynamicModuleManager.getInstalledModules();
                console.log('ModuleController', `[ExclusiveModuleController] Loaded ${this.availableModules.length} modules from filesystem`);
            }
        } catch (error) {
            console.error('ModuleController', '[ExclusiveModuleController] Failed to load modules from filesystem:', error);
        }
    }


    /**
     * Setup event listeners for dynamic module system
     * @private
     */
    setupDynamicModuleEventListeners() {
        if (!this.dynamicModuleManager) return;
        
        // Listen for module installation events
        this.dynamicModuleManager.addEventListener('moduleInstalled', (event) => {
            const { moduleId } = event.detail;
            this.availableModules.push(moduleId);
            this.eventBus.emit('moduleAvailable', { moduleId });
            console.log('ModuleController', `[ExclusiveModuleController] Module installed: ${moduleId}`);
        });
        
        // Listen for module uninstallation events
        this.dynamicModuleManager.addEventListener('moduleUninstalled', (event) => {
            const { moduleId } = event.detail;
            this.availableModules = this.availableModules.filter(id => id !== moduleId);
            
            // If this was the active module, deactivate it
            if (this.currentModule === moduleId) {
                this.currentModule = null;
                localStorage.removeItem(this.storageKey);
            }
            
            this.eventBus.emit('moduleUnavailable', { moduleId });
            console.log('ModuleController', `[ExclusiveModuleController] Module uninstalled: ${moduleId}`);
        });
        
        // Listen for module load events
        this.dynamicModuleManager.addEventListener('moduleLoaded', (event) => {
            const { moduleId, instance } = event.detail;
            this.moduleInstances.set(moduleId, instance);
            console.log('ModuleController', `[ExclusiveModuleController] Module loaded: ${moduleId}`);
        });
        
        // Listen for module unload events
        this.dynamicModuleManager.addEventListener('moduleUnloaded', (event) => {
            const { moduleId } = event.detail;
            this.moduleInstances.delete(moduleId);
            console.log('ModuleController', `[ExclusiveModuleController] Module unloaded: ${moduleId}`);
        });
    }

    /**
     * Detect the currently active module from localStorage
     * @returns {string|null} Active module code or null
     */
    detectActiveModule() {
        try {
            const savedModule = localStorage.getItem(this.storageKey);
            
            if (savedModule && this.availableModules.includes(savedModule)) {
                // Verify module is still available and active
                const moduleInfo = this.getModuleInfo(savedModule);
                
                if (moduleInfo && moduleInfo.status === 'active') {
                    this.currentModule = savedModule;
                    
                    // Update UI to reflect active module
                    this.updateUIForActiveModule(savedModule);
                    
                    return savedModule;
                } else {
                    // Module no longer available, clear storage
                    localStorage.removeItem(this.storageKey);
                }
            }
            
            return null;
            
        } catch (error) {
            console.error('ModuleController', '[ExclusiveModuleController] Error detecting active module:', error);
            return null;
        }
    }

    /**
     * Switch to the specified module with optional confirmation
     * @param {string} moduleCode - Code of the target module
     * @param {Object} options - Switch options
     * @returns {Promise<boolean>} True if switch was successful
     */
    async switchToModule(moduleCode, options = {}) {
        const {
            confirmationRequired = true,
            cleanupPreviousTabs = true,
            saveState = true,
            force = false
        } = options;

        try {
            console.log("Component", `[ExclusiveModuleController] Attempting to switch to module: ${moduleCode}`);
            
            // Validate module
            if (!this.isValidModule(moduleCode)) {
                throw new Error(`Invalid module: ${moduleCode}`);
            }
            
            // Check if module is available
            const moduleInfo = this.getModuleInfo(moduleCode);
            if (!moduleInfo || moduleInfo.status !== 'active') {
                throw new Error(`Module ${moduleCode} is not available`);
            }
            
            // If already the active module, do nothing
            if (this.currentModule === moduleCode && !force) {
                console.log("Component", `[ExclusiveModuleController] Module ${moduleCode} is already active`);
                return true;
            }
            
            // Emit loading event
            this.eventBus.emit('loading', {
                from: this.currentModule,
                to: moduleCode,
                timestamp: new Date().toISOString()
            });
            
            // Request confirmation if required and there's a current module
            if (confirmationRequired && this.currentModule && !force) {
                const confirmed = await this.showModuleSwitchConfirmation(moduleCode);
                if (!confirmed) {
                    this.eventBus.emit('switch-cancelled', {
                        from: this.currentModule,
                        to: moduleCode,
                        reason: 'user-cancelled'
                    });
                    return false;
                }
            }
            
            // Save state of current module if necessary
            if (saveState && this.currentModule) {
                await this.saveModuleState(this.currentModule);
            }
            
            // Clean up previous tabs if necessary
            if (cleanupPreviousTabs && window.simplifiedTabSystem) {
                await window.simplifiedTabSystem.closeAllTabs();
            }
            
            // Deactivate current module
            if (this.currentModule) {
                await this.deactivateModule(this.currentModule);
            }
            
            // Activate new module
            await this.activateModule(moduleCode);
            
            // Update current module
            const previousModule = this.currentModule;
            this.currentModule = moduleCode;
            
            // Persist to localStorage
            localStorage.setItem(this.storageKey, moduleCode);
            
            // Update UI
            this.updateUIForActiveModule(moduleCode);
            
            // Emit switch event
            this.eventBus.emit('switched', {
                from: previousModule,
                to: moduleCode,
                timestamp: new Date().toISOString()
            });
            
            // Also emit global event for other systems
            window.CoreDeskEvents?.emit(window.COREDESK_CONSTANTS?.EVENTS?.MODULE_SWITCHED, {
                from: previousModule,
                to: moduleCode,
                timestamp: new Date().toISOString()
            });
            
            // Emit DOM event for panel manager and other components
            window.dispatchEvent(new CustomEvent('module:switched', {
                detail: {
                    from: previousModule,
                    to: moduleCode,
                    timestamp: new Date().toISOString()
                }
            }));
            
            console.log("Component", `[ExclusiveModuleController] Successfully switched to module: ${moduleCode}`);
            
            // Log the switch
            window.Logger?.module.info('Module switched', {
                from: previousModule,
                to: moduleCode
            });
            
            // Module switch completed - notify panel manager
            if (window.panelManager && typeof window.panelManager.refreshModulesPanel === 'function') {
                window.panelManager.refreshModulesPanel();
            }
            
            return true;
            
        } catch (error) {
            console.error(`[ExclusiveModuleController] Error switching to module ${moduleCode}:`, error);
            
            this.eventBus.emit('error', {
                action: 'switch',
                module: moduleCode,
                error: error.message
            });
            
            window.Logger?.module.error('Module switch failed', {
                module: moduleCode,
                error: error.message
            });
            
            return false;
        }
    }

    /**
     * Get information about tabs in the current module
     * @returns {Object} Module tabs information
     */
    getModuleTabsInfo() {
        if (!this.currentModule) {
            return {
                currentModule: null,
                tabs: [],
                count: 0,
                hasUnsaved: false
            };
        }

        let tabs = [];
        let hasUnsaved = false;
        
        // Get tabs from SimplifiedTabSystem if available
        if (window.simplifiedTabSystem) {
            tabs = window.simplifiedTabSystem.getTabsByModule(this.currentModule);
            hasUnsaved = tabs.some(tab => tab.hasUnsavedChanges);
        }

        return {
            currentModule: this.currentModule,
            tabs: tabs.map(tab => ({
                id: tab.id,
                title: tab.title,
                module: tab.module,
                hasUnsavedChanges: tab.hasUnsavedChanges || false,
                type: tab.type
            })),
            count: tabs.length,
            hasUnsaved
        };
    }

    /**
     * Get information about a specific module
     * @param {string} moduleCode - Module code
     * @returns {Object|null} Module information
     */
    getModuleInfo(moduleCode) {
        if (this.dynamicModuleManager && this.dynamicModuleManager.registry) {
            const moduleMetadata = this.dynamicModuleManager.registry.getModuleMetadata(moduleCode);
            if (moduleMetadata) {
                return {
                    code: moduleCode,
                    name: moduleMetadata.name,
                    description: moduleMetadata.description,
                    version: moduleMetadata.version,
                    status: 'active',
                    color: moduleMetadata.color || '#007acc'
                };
            }
        }
        
        return null;
    }

    /**
     * Check if a module code is valid
     * @param {string} moduleCode - Module code to validate
     * @returns {boolean} True if valid
     */
    isValidModule(moduleCode) {
        // Check if module is in available modules list
        if (this.availableModules.includes(moduleCode)) {
            return true;
        }
        
        // Check dynamic modules
        if (this.dynamicModuleManager) {
            return this.dynamicModuleManager.isModuleInstalled(moduleCode);
        }
        
        return false;
    }

    /**
     * Get list of available modules
     * @returns {Array} Array of available modules
     */
    getAvailableModules() {
        const modules = [];
        
        if (this.dynamicModuleManager) {
            // Get modules from dynamic system
            const installedModuleIds = this.dynamicModuleManager.getInstalledModules();
            
            for (const moduleId of installedModuleIds) {
                const metadata = this.dynamicModuleManager.registry ? this.dynamicModuleManager.registry.getModuleMetadata(moduleId) : null;
                if (metadata) {
                    modules.push({
                        code: moduleId,
                        name: metadata.name,
                        description: metadata.description,
                        color: metadata.color || '#007acc',
                        isActive: this.currentModule === moduleId,
                        version: metadata.version,
                        isDynamic: true
                    });
                }
            }
        }
        
        return modules;
    }

    /**
     * Show confirmation modal for module switch
     * @param {string} targetModule - Target module code
     * @returns {Promise<boolean>} User confirmation
     * @private
     */
    async showModuleSwitchConfirmation(targetModule) {
        const tabsInfo = this.getModuleTabsInfo();
        const targetModuleInfo = this.getModuleInfo(targetModule);
        
        if (!targetModuleInfo) {
            return false;
        }
        
        return new Promise((resolve) => {
            // Create confirmation modal
            const modal = document.createElement('div');
            modal.className = 'modal-container';
            modal.innerHTML = `
                <div class="modal">
                    <div class="modal-header">
                        <h3 class="modal-title">Cambiar Módulo</h3>
                        <button class="modal-close" id="modal-close">×</button>
                    </div>
                    <div class="modal-body">
                        <p>¿Deseas cambiar del módulo <strong>${this.getModuleInfo(this.currentModule)?.name}</strong> al módulo <strong>${targetModuleInfo.name}</strong>?</p>
                        
                        ${tabsInfo.count > 0 ? `
                            <div class="tabs-info">
                                <p><strong>Pestañas abiertas:</strong> ${tabsInfo.count}</p>
                                ${tabsInfo.hasUnsaved ? `
                                    <p class="warning">⚠️ Tienes cambios sin guardar en algunas pestañas.</p>
                                ` : ''}
                            </div>
                        ` : ''}
                        
                        <p>Las pestañas del módulo actual se cerrarán automáticamente.</p>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" id="modal-cancel">Cancelar</button>
                        <button class="btn btn-primary" id="modal-confirm">Cambiar Módulo</button>
                    </div>
                </div>
            `;
            
            // Add to DOM
            document.body.appendChild(modal);
            modal.classList.remove('hidden');
            
            // Event handlers
            const cleanup = () => {
                document.body.removeChild(modal);
            };
            
            modal.querySelector('#modal-close').addEventListener('click', () => {
                cleanup();
                resolve(false);
            });
            
            modal.querySelector('#modal-cancel').addEventListener('click', () => {
                cleanup();
                resolve(false);
            });
            
            modal.querySelector('#modal-confirm').addEventListener('click', () => {
                cleanup();
                resolve(true);
            });
            
            // Close on background click
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    cleanup();
                    resolve(false);
                }
            });
            
            // Focus on confirm button
            modal.querySelector('#modal-confirm').focus();
        });
    }

    /**
     * Activate a specific module
     * @param {string} moduleCode - Module to activate
     * @returns {Promise<void>}
     * @private
     */
    async activateModule(moduleCode) {
        console.log("Component", `[ExclusiveModuleController] Activating module: ${moduleCode}`);
        
        try {
            // Use dynamic module system
            await this.activateDynamicModule(moduleCode);
            
            console.log("Component", `[ExclusiveModuleController] Module ${moduleCode} activated successfully`);
            
            // Hide welcome screen when module is activated
            const welcomeScreen = document.getElementById('welcome-screen');
            if (welcomeScreen) {
                welcomeScreen.classList.add('hidden');
                console.log("Component", `[ExclusiveModuleController] Welcome screen hidden`);
            }
            
        } catch (error) {
            console.error(`[ExclusiveModuleController] Error activating module ${moduleCode}:`, error);
            throw error;
        }
    }

    /**
     * Deactivate a specific module
     * @param {string} moduleCode - Module to deactivate
     * @returns {Promise<void>}
     * @private
     */
    async deactivateModule(moduleCode) {
        console.log("Component", `[ExclusiveModuleController] Deactivating module: ${moduleCode}`);
        
        try {
            // Use dynamic module system
            await this.deactivateDynamicModule(moduleCode);
            
            console.log("Component", `[ExclusiveModuleController] Module ${moduleCode} deactivated successfully`);
            
            // Show welcome screen if no modules are active
            if (!this.currentModule) {
                const welcomeScreen = document.getElementById('welcome-screen');
                if (welcomeScreen) {
                    welcomeScreen.classList.remove('hidden');
                    console.log("Component", `[ExclusiveModuleController] Welcome screen shown (no active modules)`);
                }
            }
            
        } catch (error) {
            console.error(`[ExclusiveModuleController] Error deactivating module ${moduleCode}:`, error);
            // Don't throw on deactivation errors, just log them
        }
    }

    /**
     * Activate module using dynamic module system
     * @param {string} moduleCode - Module to activate
     * @returns {Promise<void>}
     * @private
     */
    async activateDynamicModule(moduleCode) {
        console.log("Component", `[ExclusiveModuleController] Activating dynamic module: ${moduleCode}`);
        
        try {
            // Check if module is installed in dynamic system
            if (!this.dynamicModuleManager.isModuleInstalled(moduleCode)) {
                throw new Error(`Module ${moduleCode} is not installed`);
            }
            
            // Load module if not already loaded
            if (!this.dynamicModuleManager.isModuleLoaded(moduleCode)) {
                await this.dynamicModuleManager.loadModule(moduleCode);
            }
            
            // Get module instance from dynamic system
            const moduleInstance = this.dynamicModuleManager.getModuleInstance(moduleCode);
            if (!moduleInstance) {
                throw new Error(`Failed to get module instance for ${moduleCode}`);
            }
            
            // Store reference locally
            this.moduleInstances.set(moduleCode, moduleInstance);
            
            // Activate module instance
            if (typeof moduleInstance.activate === 'function') {
                await moduleInstance.activate();
            }
            
            // Create default tab for the module
            await this.createDefaultModuleTab(moduleCode);
            
        } catch (error) {
            console.error(`[ExclusiveModuleController] Error activating dynamic module ${moduleCode}:`, error);
            throw error;
        }
    }

    /**
     * Deactivate module using dynamic module system
     * @param {string} moduleCode - Module to deactivate
     * @returns {Promise<void>}
     * @private
     */
    async deactivateDynamicModule(moduleCode) {
        console.log("Component", `[ExclusiveModuleController] Deactivating dynamic module: ${moduleCode}`);
        
        try {
            // Get module instance
            const moduleInstance = this.moduleInstances.get(moduleCode);
            
            if (moduleInstance && typeof moduleInstance.deactivate === 'function') {
                await moduleInstance.deactivate();
            }
            
            // Remove local reference
            this.moduleInstances.delete(moduleCode);
            
            // Clear active module from dynamic module manager
            if (this.dynamicModuleManager && this.dynamicModuleManager.activeModule && 
                this.dynamicModuleManager.activeModule.moduleCode === moduleCode) {
                this.dynamicModuleManager.activeModule = null;
            }
            
            // Optionally unload module from dynamic system (keep it loaded for faster reactivation)
            // await this.dynamicModuleManager.unloadModule(moduleCode);
            
        } catch (error) {
            console.error(`[ExclusiveModuleController] Error deactivating dynamic module ${moduleCode}:`, error);
            throw error;
        }
    }



    /**
     * Save state of a module
     * @param {string} moduleCode - Module to save state for
     * @returns {Promise<void>}
     * @private
     */
    async saveModuleState(moduleCode) {
        try {
            const moduleInstance = this.moduleInstances.get(moduleCode);
            if (moduleInstance && typeof moduleInstance.saveState === 'function') {
                await moduleInstance.saveState();
            }
            
            // Also save tab state
            if (window.simplifiedTabSystem) {
                window.simplifiedTabSystem.saveState();
            }
            
        } catch (error) {
            console.error(`[ExclusiveModuleController] Error saving state for module ${moduleCode}:`, error);
        }
    }


    

    /**
     * Create default tab for a module
     * @param {string} moduleCode - Module to create tab for
     * @returns {Promise<void>}
     * @private
     */
    async createDefaultModuleTab(moduleCode) {
        if (!window.simplifiedTabSystem) {
            return;
        }
        
        const moduleInfo = this.getModuleInfo(moduleCode);
        if (!moduleInfo) {
            return;
        }
        
        // Check if module manages its own UI container via metadata
        const moduleMetadata = this.dynamicModuleManager?.registry?.getModuleMetadata(moduleCode);
        if (moduleMetadata && moduleMetadata.managesOwnUI === true) {
            console.log("Component", `${moduleCode} module manages its own UI container, skipping tab creation`);
            return;
        }
        
        // Get module instance to render content
        const moduleInstance = this.dynamicModuleManager.getModuleInstance(moduleCode);
        let moduleContent = null;
        
        if (moduleInstance && typeof moduleInstance.render === 'function') {
            try {
                moduleContent = moduleInstance.render();
            } catch (error) {
                console.warn(`[ExclusiveModuleController] Module ${moduleCode} render failed:`, error);
            }
        }
        
        // Create default dashboard tab for modules that don't manage their own UI
        window.simplifiedTabSystem.createTab({
            title: `${moduleInfo.name} - Dashboard`,
            module: moduleCode,
            content: {
                type: 'module',
                moduleCode,
                element: moduleContent // Pass the rendered element
            },
            closable: false // Default tabs are not closable
        });
    }

    /**
     * Update UI to reflect active module
     * @param {string} moduleCode - Active module code
     * @private
     */
    updateUIForActiveModule(moduleCode) {
        // Update status bar
        const statusElement = document.getElementById('current-module');
        if (statusElement) {
            const moduleInfo = this.getModuleInfo(moduleCode);
            if (moduleInfo) {
                statusElement.querySelector('.status-text').textContent = moduleInfo.name;
                statusElement.style.color = moduleInfo.color;
            }
        }
        
        // Update activity bar if needed
        // TODO [2025-06-29] [UI]: Update activity bar active state
        
        // Filter tabs by module
        if (window.simplifiedTabSystem) {
            window.simplifiedTabSystem.filterTabsByModule(moduleCode);
        }
    }

    /**
     * Set up event listeners
     * @private
     */
    setupEventListeners() {
        // Listen for license activation events
        window.addEventListener(window.COREDESK_CONSTANTS?.EVENTS?.LICENSE_ACTIVATED, (event) => {
            // Re-evaluate available modules based on license
            this.updateAvailableModules(event.detail.license);
        });
        
        // Listen for tab system events
        window.addEventListener(window.COREDESK_CONSTANTS?.EVENTS?.TAB_CLOSED, (event) => {
            // Check if all tabs are closed for current module
            const tabsInfo = this.getModuleTabsInfo();
            if (tabsInfo.count === 0 && this.currentModule) {
                // Create default tab if no tabs remain
                this.createDefaultModuleTab(this.currentModule);
            }
        });
    }

    /**
     * Initialize UI bindings
     * @private
     */
    initializeUIBindings() {
        // Bind module cards in welcome screen
        document.querySelectorAll('.module-card').forEach(card => {
            card.addEventListener('click', (e) => {
                const moduleCode = e.currentTarget.dataset.module;
                if (moduleCode && this.isValidModule(moduleCode)) {
                    this.switchToModule(moduleCode);
                }
            });
        });
    }

    /**
     * Update available modules based on license
     * @param {Object} license - License object
     * @private
     */
    updateAvailableModules(license) {
        if (!license || !license.modules) {
            return;
        }
        
        // Update module availability based on license
        license.modules.forEach(module => {
            const moduleInfo = this.getModuleInfo(module.code);
            if (moduleInfo) {
                moduleInfo.isActive = module.isActive;
            }
        });
        
        // If current module is no longer active, switch to first available
        if (this.currentModule) {
            const currentModuleInfo = this.getModuleInfo(this.currentModule);
            if (!currentModuleInfo?.isActive) {
                const firstAvailable = this.getAvailableModules()[0];
                if (firstAvailable) {
                    this.switchToModule(firstAvailable.code, { confirmationRequired: false });
                }
            }
        }
    }

    /**
     * Install a module dynamically
     * @param {string} moduleId - Module identifier
     * @param {string} version - Module version (optional, defaults to 'latest')
     * @returns {Promise<boolean>} True if installation was successful
     */
    async installModule(moduleId, version = 'latest') {
        if (!this.dynamicModuleManager) {
            throw new Error('Dynamic module system is not available');
        }
        
        try {
            console.log('ModuleController', `[ExclusiveModuleController] Installing module: ${moduleId}@${version}`);
            
            const success = await this.dynamicModuleManager.installModule(moduleId, version);
            
            if (success) {
                // Update available modules list
                await this.initializeAvailableModules();
                
                console.log('ModuleController', `[ExclusiveModuleController] Module ${moduleId} installed successfully`);
                return true;
            }
            
            return false;
            
        } catch (error) {
            console.error('ModuleController', `[ExclusiveModuleController] Failed to install module ${moduleId}:`, error);
            throw error;
        }
    }

    /**
     * Uninstall a module dynamically
     * @param {string} moduleId - Module identifier
     * @returns {Promise<boolean>} True if uninstallation was successful
     */
    async uninstallModule(moduleId) {
        if (!this.dynamicModuleManager) {
            throw new Error('Dynamic module system is not available');
        }
        
        try {
            console.log('ModuleController', `[ExclusiveModuleController] Uninstalling module: ${moduleId}`);
            
            // If this is the current module, deactivate it first
            if (this.currentModule === moduleId) {
                await this.deactivateModule(moduleId);
                this.currentModule = null;
                localStorage.removeItem(this.storageKey);
            }
            
            const success = await this.dynamicModuleManager.uninstallModule(moduleId);
            
            if (success) {
                // Update available modules list
                await this.initializeAvailableModules();
                
                console.log('ModuleController', `[ExclusiveModuleController] Module ${moduleId} uninstalled successfully`);
                return true;
            }
            
            return false;
            
        } catch (error) {
            console.error('ModuleController', `[ExclusiveModuleController] Failed to uninstall module ${moduleId}:`, error);
            throw error;
        }
    }

    /**
     * Get current module status
     * @returns {Object} Current status
     */
    getStatus() {
        return {
            currentModule: this.currentModule,
            availableModules: this.getAvailableModules(),
            tabsInfo: this.getModuleTabsInfo(),
            moduleInstances: Array.from(this.moduleInstances.keys()),
            systemType: 'dynamic',
            dynamicSystemAvailable: !!this.dynamicModuleManager
        };
    }
}

// Create global instance
window.exclusiveModuleController = new ExclusiveModuleController();

console.log('ModuleController', '[ExclusiveModuleController] Global instance created successfully', );