/**
 * app.js
 * Main application initialization and coordination
 * Simplified version without require() dependencies
 */

// NOTE: This runs in the renderer process, so we can't use require()
// All modules will be loaded via script tags in the HTML

class CoreDeskApp {
    constructor() {
        // Initialize logger first (use simple console fallback)
        this.logger = {
            info: (tag, message) => console.log(`[${tag}] ${message}`),
            error: (tag, message, error) => console.error(`[${tag}] ${message}`, error),
            warn: (tag, message) => console.warn(`[${tag}] ${message}`),
            debug: (tag, message) => console.debug(`[${tag}] ${message}`)
        };
        
        // App state
        this.isInitialized = false;
        this.initializationProgress = 0;
        
        // Make app available globally (handle read-only property)
        try {
            if (!window.coreDesk) {
                window.coreDesk = {};
            }
            // Try to set app property
            if (window.coreDesk && typeof window.coreDesk === 'object') {
                if (!window.coreDesk.app) {
                    Object.defineProperty(window.coreDesk, 'app', {
                        value: this,
                        writable: true,
                        configurable: true
                    });
                } else {
                    window.coreDesk.app = this;
                }
            }
        } catch (error) {
            this.logger.warn('CoreDeskApp', 'Could not set global app reference:', error.message);
            // Fallback: use a different global reference
            window.CoreDeskAppInstance = this;
        }
        
        // Start initialization
        this.initialize();
        
        // Setup window state management
        this.setupWindowStateManagement();
    }

    async initialize() {
        try {
            this.logger.info('CoreDeskApp', 'Starting application initialization...');
            
            // Authentication is verified synchronously before this script loads
            // If we reach this point, the user is authenticated
            console.log('CoreDeskApp', '✅ Starting dashboard initialization (auth verified synchronously)');
            console.log('*** COREDESK APP.JS - FORCED PRODUCTION URL VERSION ***');
            console.log('*** THIS MESSAGE CONFIRMS THE NEW CODE IS RUNNING ***');
            
            // Initialize AuthGuard as the central authentication authority
            // This must be done early to prevent race conditions
            if (window.authGuard) {
                console.log('CoreDeskApp', 'Initializing AuthGuard as central auth authority');
                window.authGuard.init();
            } else {
                console.warn('CoreDeskApp', 'AuthGuard not available - authentication protection disabled');
            }
            
            // Show initial loading
            this.showLoading('Inicializando CoreDesk Framework...');
            this.updateProgress(10, 'Preparando sistemas...');
            
            // Setup IPC listeners first
            this.setupIPCListeners();
            
            // Wait for DOM to be ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.initializeComponents());
            } else {
                this.initializeComponents();
            }
            
        } catch (error) {
            this.logger.error('CoreDeskApp', 'Error during initialization', error);
            this.handleInitializationError(error);
        }
    }

    setupIPCListeners() {
        // Listen for backend initialization events via electronAPI
        if (window.electronAPI && window.electronAPI.on) {
            this.logger.info('CoreDeskApp', 'Setting up electronAPI listeners');
            window.electronAPI.on('app:initialized', (event, data) => {
                this.logger.info('CoreDeskApp', 'Backend initialization completed', data);
                this.updateProgress(80, 'Conectando con servicios...');
                this.completeInitialization();
            });

            window.electronAPI.on('app:initialization-error', (event, error) => {
                this.logger.error('CoreDeskApp', 'Backend initialization failed', error);
                this.handleInitializationError(error);
            });
        } else {
            this.logger.warn('CoreDeskApp', 'electronAPI not available - will run in frontend-only mode');
            // Continue with frontend initialization
        }
    }

    initializeComponents() {
        try {
            // NOTE: Don't clear module registry on every startup - it destroys module persistence
            // this.clearModuleRegistry();
            
            this.updateProgress(30, 'Inicializando componentes UI...');
            
            // Initialize basic UI components
            this.initializeBasicUI();
            
            this.updateProgress(50, 'Configurando eventos...');
            
            // Initialize event handlers
            this.initializeEventHandlers();
            
            this.updateProgress(70, 'Esperando servicios backend...');
            
            // Wait for backend or timeout
            this.waitForBackendOrTimeout();
            
        } catch (error) {
            this.logger.error('CoreDeskApp', 'Error initializing components', error);
            this.handleInitializationError(error);
        }
    }

    /**
     * Clear module registry to force downloads from server
     */
    clearModuleRegistry() {
        try {
            // Clear the module registry localStorage
            const keys = ['coredesk_registry_registry'];
            keys.forEach(key => {
                if (localStorage.getItem(key)) {
                    localStorage.removeItem(key);
                    this.logger.info('CoreDeskApp', `Cleared localStorage key: ${key}`);
                }
            });
            
            // Also clear any other module-related storage
            Object.keys(localStorage).forEach(key => {
                if (key.startsWith('coredesk_registry_') || key.startsWith('coredesk_module_')) {
                    localStorage.removeItem(key);
                    this.logger.info('CoreDeskApp', `Cleared module storage key: ${key}`);
                }
            });
            
            this.logger.info('CoreDeskApp', 'Module registry cleared - modules must be downloaded from server');
            
        } catch (error) {
            this.logger.error('CoreDeskApp', 'Error clearing module registry:', error);
        }
    }

    waitForBackendOrTimeout() {
        // If backend doesn't respond in 5 seconds, continue in frontend-only mode
        const timeout = setTimeout(() => {
            this.logger.warn('CoreDeskApp', 'Backend not available - continuing in frontend-only mode');
            this.completeInitialization();
        }, 5000);

        // Store timeout ID to clear it if backend responds
        this.backendTimeout = timeout;
    }

    completeInitialization() {
        try {
            // Clear any pending timeout
            if (this.backendTimeout) {
                clearTimeout(this.backendTimeout);
                this.backendTimeout = null;
            }

            this.updateProgress(90, 'Finalizando...');
            
            // Mark as initialized
            this.isInitialized = true;
            
            this.updateProgress(100, 'Listo');
            
            // Hide loading after a brief delay
            setTimeout(() => {
                this.hideLoading();
                
                // Application fully initialized
                
                // Wait for database to be ready before loading modules
                this.waitForDatabaseAndLoadModules();
            }, 1000);
            
            this.logger.info('CoreDeskApp', 'Application initialized successfully');
            
        } catch (error) {
            this.logger.error('CoreDeskApp', 'Error completing initialization', error);
            this.handleInitializationError(error);
        }
    }

    initializeBasicUI() {
        // Initialize title bar
        this.initializeTitleBar();
        
        // Initialize activity bar
        this.initializeActivityBar();
        
        // Initialize panels
        this.initializePanels();
        
        // Initialize status bar
        this.initializeStatusBar();
    }

    initializeTitleBar() {
        // Window controls using electronAPI
        const minimizeBtn = document.getElementById('minimize-btn');
        const maximizeBtn = document.getElementById('maximize-btn');
        const closeBtn = document.getElementById('close-btn');
        
        if (minimizeBtn) {
            minimizeBtn.addEventListener('click', async () => {
                try {
                    if (window.electronAPI && window.electronAPI.minimizeWindow) {
                        console.log('[CoreDeskApp] Minimize button clicked');
                        const result = await window.electronAPI.minimizeWindow();
                        console.log('[CoreDeskApp] Minimize result:', result);
                        if (result && !result.success) {
                            console.error('[CoreDeskApp] Failed to minimize window:', result.error);
                        }
                    } else {
                        console.error('[CoreDeskApp] electronAPI.minimizeWindow not available');
                    }
                } catch (error) {
                    console.error('[CoreDeskApp] Error minimizing window:', error);
                }
            });
        }
        
        // Maximize button is handled by titleBar.js to avoid conflicts
        
        if (closeBtn) {
            closeBtn.addEventListener('click', async () => {
                try {
                    if (window.electronAPI && window.electronAPI.closeWindow) {
                        console.log('[CoreDeskApp] Close button clicked');
                        const result = await window.electronAPI.closeWindow();
                        console.log('[CoreDeskApp] Close result:', result);
                        if (result && !result.success) {
                            console.error('[CoreDeskApp] Failed to close window:', result.error);
                        }
                    } else {
                        console.error('[CoreDeskApp] electronAPI.closeWindow not available');
                    }
                } catch (error) {
                    console.error('[CoreDeskApp] Error closing window:', error);
                }
            });
        }
        
        console.log('[CoreDeskApp] Title bar initialized');
    }

    initializeActivityBar() {
        const activityButtons = document.querySelectorAll('.activity-button');
        
        activityButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                // Remove active class from all buttons
                activityButtons.forEach(btn => btn.classList.remove('active'));
                
                // Add active class to clicked button
                button.classList.add('active');
                
                // Handle panel switching
                const panelType = button.getAttribute('data-panel');
                if (panelType) {
                    this.showLeftPanel(panelType);
                }
            });
        });
    }

    initializePanels() {
        // Panel toggle buttons
        const panelToggles = document.querySelectorAll('.panel-toggle');
        
        panelToggles.forEach(toggle => {
            toggle.addEventListener('click', () => {
                const panelId = toggle.id.replace('toggle-', '').replace('-panel', '');
                this.togglePanel(panelId);
            });
        });
        
        // Panel close buttons
        const panelCloseButtons = document.querySelectorAll('.panel-close');
        
        panelCloseButtons.forEach(closeBtn => {
            closeBtn.addEventListener('click', () => {
                const panelType = closeBtn.getAttribute('data-panel');
                this.hidePanel(panelType);
            });
        });
    }

    initializeStatusBar() {
        // Basic status bar initialization
        this.updateConnectionStatus('offline');
        this.updateLicenseStatus('Sin licencia');
        this.updateCurrentModule('Sin módulo activo');
    }

    initializeEventHandlers() {
        // License activation
        const activateLicenseBtn = document.getElementById('activate-license-btn');
        if (activateLicenseBtn) {
            activateLicenseBtn.addEventListener('click', () => {
                this.showLicenseActivationModal();
            });
        }
        
        // Trial request
        const requestTrialBtn = document.getElementById('request-trial-btn');
        if (requestTrialBtn) {
            requestTrialBtn.addEventListener('click', () => {
                this.showTrialRequestModal();
            });
        }
        
        // Module cards
        const moduleCards = document.querySelectorAll('.module-card');
        moduleCards.forEach(card => {
            card.addEventListener('click', () => {
                const moduleCode = card.getAttribute('data-module');
                this.selectModule(moduleCode);
            });
        });
        
        // Notification center
        const notificationsBtn = document.getElementById('notifications-button');
        if (notificationsBtn) {
            notificationsBtn.addEventListener('click', () => {
                if (window.notificationCenter) {
                    window.notificationCenter.showNotificationCenter();
                }
            });
        }
        
        // Account dropdown menu
        this.setupAccountDropdown();
        
        // Dashboard quick actions
        document.addEventListener('click', (e) => {
            if (e.target.id === 'import-data-btn') {
                this.handleImportData();
            }
            
            if (e.target.id === 'sync-cloud-btn') {
                this.handleSyncCloud();
            }
            
            if (e.target.id === 'view-all-activity') {
                this.showActivityHistory();
            }
            
            if (e.target.id === 'mark-all-read') {
                this.markAllNotificationsRead();
            }
        });
    }

    showLoading(message = 'Cargando...') {
        const loadingOverlay = document.getElementById('loading-overlay');
        if (loadingOverlay) {
            const loadingText = loadingOverlay.querySelector('p');
            if (loadingText) {
                loadingText.textContent = message;
            }
            loadingOverlay.classList.remove('hidden');
        }
    }

    hideLoading() {
        const loadingOverlay = document.getElementById('loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.classList.add('hidden');
        }
    }

    updateProgress(percent, message) {
        this.initializationProgress = percent;
        this.showLoading(message);
        this.logger.debug('CoreDeskApp', `Progress: ${percent}% - ${message}`);
    }

    showLeftPanel(panelType) {
        const leftPanel = document.getElementById('left-panel');
        const leftPanelTitle = document.getElementById('left-panel-title');
        const leftPanelContent = document.getElementById('left-panel-content');
        
        if (leftPanel && leftPanelTitle && leftPanelContent) {
            leftPanel.classList.remove('hidden');
            
            // Update panel title and content based on type
            switch (panelType) {
                case 'explorer':
                    leftPanelTitle.textContent = 'Explorer';
                    leftPanelContent.innerHTML = '<p>Explorador de archivos</p>';
                    break;
                case 'cloud':
                    leftPanelTitle.textContent = 'Cloud';
                    if (window.cloudPanel) {
                        leftPanelContent.innerHTML = window.cloudPanel.renderCloudPanel();
                    } else {
                        leftPanelContent.innerHTML = '<p>Sincronización en la nube</p>';
                    }
                    break;
                case 'search':
                    leftPanelTitle.textContent = 'Search';
                    if (window.searchPanel) {
                        leftPanelContent.innerHTML = window.searchPanel.renderSearchPanel();
                    } else {
                        leftPanelContent.innerHTML = '<p>Búsqueda global</p>';
                    }
                    break;
                case 'modules':
                    leftPanelTitle.textContent = 'Modules';
                    leftPanelContent.innerHTML = '<p>Gestión de módulos</p>';
                    break;
                case 'extensions':
                    leftPanelTitle.textContent = 'Extensions';
                    leftPanelContent.innerHTML = '<p>Extensiones disponibles</p>';
                    break;
                default:
                    leftPanelTitle.textContent = 'Panel';
                    leftPanelContent.innerHTML = '<p>Panel de información</p>';
            }
        }
    }

    togglePanel(panelType) {
        const panel = document.getElementById(`${panelType}-panel`);
        if (panel) {
            panel.classList.toggle('hidden');
        }
    }

    hidePanel(panelType) {
        const panel = document.getElementById(`${panelType}-panel`);
        if (panel) {
            panel.classList.add('hidden');
        }
    }

    updateConnectionStatus(status) {
        const connectionStatus = document.getElementById('connection-status');
        if (connectionStatus) {
            const icon = connectionStatus.querySelector('.status-icon');
            const text = connectionStatus.querySelector('.status-text');
            
            if (icon && text) {
                if (status === 'online') {
                    icon.textContent = '🟢';
                    text.textContent = 'Online';
                    icon.classList.remove('offline');
                } else {
                    icon.textContent = '🔴';
                    text.textContent = 'Offline';
                    icon.classList.add('offline');
                }
            }
        }
    }

    updateLicenseStatus(status) {
        const licenseStatus = document.getElementById('license-status');
        if (licenseStatus) {
            const text = licenseStatus.querySelector('.status-text');
            if (text) {
                text.textContent = status;
            }
        }
    }

    updateCurrentModule(moduleName) {
        const currentModule = document.getElementById('current-module');
        if (currentModule) {
            const text = currentModule.querySelector('.status-text');
            if (text) {
                text.textContent = moduleName;
            }
        }
    }

    updateConnectionStatus(status) {
        const connectionStatus = document.getElementById('connection-status');
        if (connectionStatus) {
            const icon = connectionStatus.querySelector('.status-icon');
            const text = connectionStatus.querySelector('.status-text');
            
            if (icon && text) {
                if (status === 'online') {
                    icon.textContent = '🟢';
                    text.textContent = 'Online';
                    icon.classList.remove('offline');
                } else {
                    icon.textContent = '🔴';
                    text.textContent = 'Offline';
                    icon.classList.add('offline');
                }
            }
        }
    }

    selectModule(moduleCode) {
        this.logger.info('CoreDeskApp', `Module selected: ${moduleCode}`);
        
        // Use ExclusiveModuleController to actually switch to the module
        if (window.exclusiveModuleController) {
            window.exclusiveModuleController.switchToModule(moduleCode, {
                confirmationRequired: false,
                cleanupPreviousTabs: true
            }).then(success => {
                if (success) {
                    this.logger.info('CoreDeskApp', `Successfully switched to module: ${moduleCode}`);
                    this.updateCurrentModule(moduleCode);
                } else {
                    this.logger.error('CoreDeskApp', `Failed to switch to module: ${moduleCode}`);
                }
            }).catch(error => {
                this.logger.error('CoreDeskApp', `Error switching to module ${moduleCode}:`, error);
            });
        } else {
            this.logger.warn('CoreDeskApp', 'ExclusiveModuleController not available, using fallback');
            this.updateCurrentModule(moduleCode);
        }
    }

    showLicenseActivationModal() {
        this.logger.info('CoreDeskApp', 'Showing license activation modal');
        // License activation modal logic will be implemented here
    }

    showTrialRequestModal() {
        this.logger.info('CoreDeskApp', 'Showing trial request modal');
        // Trial request modal logic will be implemented here
    }
    
    handleImportData() {
        this.logger.info('CoreDeskApp', 'Import data requested');
        if (window.notificationCenter) {
            window.notificationCenter.addNotification({
                title: 'Importación de datos',
                message: 'Funcionalidad de importación próximamente disponible',
                type: 'info',
                category: 'system'
            });
        }
    }
    
    handleSyncCloud() {
        this.logger.info('CoreDeskApp', 'Cloud sync requested');
        if (window.cloudPanel) {
            window.cloudPanel.toggleSync();
        }
        if (window.notificationCenter) {
            window.notificationCenter.addNotification({
                title: 'Sincronización iniciada',
                message: 'Sincronizando datos con la nube...',
                type: 'info',
                category: 'sync'
            });
        }
    }
    
    showActivityHistory() {
        this.logger.info('CoreDeskApp', 'Activity history requested');
        if (window.notificationCenter) {
            window.notificationCenter.addNotification({
                title: 'Historial de actividad',
                message: 'Vista detallada próximamente disponible',
                type: 'info',
                category: 'system'
            });
        }
    }
    
    markAllNotificationsRead() {
        if (window.notificationCenter) {
            window.notificationCenter.markAllAsRead();
        }
    }

    handleInitializationError(error) {
        this.logger.error('CoreDeskApp', 'Initialization failed', error);
        this.hideLoading();
        
        // Show error message to user
        alert('Error al inicializar la aplicación. Por favor, reinicie la aplicación.');
    }

    /**
     * Wait for database to be ready and then load modules (optimized)
     */
    async waitForDatabaseAndLoadModules() {
        try {
            this.logger.info('CoreDeskApp', 'Loading modules with optimized approach...');
            
            // Load modules immediately from server without waiting for database
            // This provides instant feedback to user
            const moduleGrid = document.getElementById('dynamic-module-grid');
            if (moduleGrid) {
                this.logger.info('CoreDeskApp', 'Loading modules directly from server for immediate display');
                await this.showServerModules(moduleGrid);
            }
            
            this.setupModuleDropzone();
            
            // Check database in background without blocking UI
            this.checkDatabaseInBackground();
            
        } catch (error) {
            this.logger.error('CoreDeskApp', 'Error loading modules:', error);
            
            // Fallback: show server modules anyway
            const moduleGrid = document.getElementById('dynamic-module-grid');
            if (moduleGrid) {
                await this.showServerModules(moduleGrid);
            }
        }
    }

    /**
     * Check database status in background without blocking module loading
     */
    async checkDatabaseInBackground() {
        try {
            const isDatabaseReady = await this.checkDatabaseReady();
            
            if (isDatabaseReady) {
                this.logger.info('CoreDeskApp', 'Database is ready (background check)');
                return;
            }
            
            // Listen for database ready event but don't block module loading
            if (window.electronAPI && window.electronAPI.onDatabaseReady) {
                window.electronAPI.onDatabaseReady(() => {
                    this.logger.info('CoreDeskApp', 'Database ready event received (background)');
                    // Database is now ready, modules are already loaded from server
                });
            }
            
            // Set a much shorter timeout for background check
            setTimeout(async () => {
                const isReady = await this.checkDatabaseReady();
                if (isReady) {
                    this.logger.info('CoreDeskApp', 'Database became ready (delayed check)');
                } else {
                    this.logger.debug('CoreDeskApp', 'Database still not ready, but modules work from server');
                }
            }, 3000); // Only 3 seconds instead of 30
            
        } catch (error) {
            this.logger.debug('CoreDeskApp', 'Background database check failed (non-critical):', error);
        }
    }

    /**
     * Check if database is ready
     */
    async checkDatabaseReady() {
        try {
            if (!window.electronAPI || !window.electronAPI.database) {
                return false;
            }
            
            // Try a simple database query to check if it's ready
            const result = await window.electronAPI.database.execute('SELECT 1', []);
            return result && result.success;
            
        } catch (error) {
            this.logger.debug('CoreDeskApp', 'Database not ready yet:', error);
            return false;
        }
    }

    /**
     * Load dynamic modules into the welcome screen grid (OPTIMIZED)
     */
    async loadDynamicModulesGrid(retryCount = 0) {
        console.log('[CoreDeskApp] *** loadDynamicModulesGrid() CALLED (OPTIMIZED) ***');
        console.log('[CoreDeskApp] *** DIRECT SERVER LOADING - NO DELAYS ***');
        
        try {
            this.logger.info('CoreDeskApp', `Loading modules directly from server for instant display`);
            
            const moduleGrid = document.getElementById('dynamic-module-grid');
            if (!moduleGrid) {
                this.logger.warn('CoreDeskApp', 'Dynamic module grid not found');
                return;
            }

            // Show brief loading indicator
            this.showModuleLoadingProgress(0);

            // Load directly from server without waiting for dynamic manager
            this.logger.info('CoreDeskApp', 'Bypassing dynamic manager delays - loading from server');
            await this.showServerModules(moduleGrid);
            
        } catch (error) {
            this.logger.error('CoreDeskApp', 'Failed to load modules grid:', error);
            
            // Show error in UI
            const moduleGrid = document.getElementById('dynamic-module-grid');
            if (moduleGrid) {
                this.showOfflineNotification(moduleGrid, error);
            }
        }
    }

    /**
     * Handle module card click in welcome screen
     */
    handleModuleCardClick(moduleId) {
        try {
            const exclusiveController = window.exclusiveModuleController;
            if (!exclusiveController) {
                this.logger.warn('CoreDeskApp', 'ExclusiveModuleController not available');
                return;
            }

            this.logger.info('CoreDeskApp', `Attempting to activate module ${moduleId} from welcome screen`);

            // Use the exclusive module controller to switch to the module
            // This will properly handle activation and trigger all necessary updates
            exclusiveController.switchToModule(moduleId, {
                confirmationRequired: false,
                cleanupPreviousTabs: true
            }).then((success) => {
                if (success) {
                    this.logger.info('CoreDeskApp', `Module ${moduleId} activated from welcome screen`);
                    
                    // Force panel refresh as additional safety measure
                    if (window.panelManager && typeof window.panelManager.refreshModulesPanel === 'function') {
                        window.panelManager.refreshModulesPanel();
                        this.logger.info('CoreDeskApp', 'Panel refresh triggered after welcome screen activation');
                    }
                } else {
                    this.logger.warn('CoreDeskApp', `Failed to activate module ${moduleId} from welcome screen`);
                }
            }).catch(error => {
                this.logger.error('CoreDeskApp', `Error activating module ${moduleId} from welcome screen:`, error);
            });
            
        } catch (error) {
            this.logger.error('CoreDeskApp', 'Error handling module card click:', error);
        }
    }

    /**
     * Show enhanced loading progress for module loading
     */
    showModuleLoadingProgress(retryCount) {
        if (retryCount === 0) {
            this.logger.info('CoreDeskApp', 'Starting module loading process...');
        }
        
        // Update progress bar in the module grid
        const moduleGrid = document.getElementById('dynamic-module-grid');
        if (moduleGrid) {
            const progressFill = moduleGrid.querySelector('.progress-fill');
            const progressText = moduleGrid.querySelector('.loading-progress small');
            
            if (progressFill) {
                const progressPercent = Math.min((retryCount / 10) * 100, 100);
                progressFill.style.width = `${progressPercent}%`;
            }
            
            if (progressText) {
                if (retryCount === 0) {
                    progressText.textContent = 'Inicializando sistema de módulos...';
                } else {
                    progressText.textContent = `Esperando módulos... (${retryCount}/10)`;
                }
            }
        }
    }

    /**
     * Load modules from server - Clean implementation for server-only architecture
     */
    async showServerModules(moduleGrid) {
        console.log('[CoreDeskApp] *** showServerModules() CALLED ***');
        console.log('[CoreDeskApp] *** STARTING SERVER MODULE LOAD ***');
        this.logger.info('CoreDeskApp', 'Loading modules from server (clean architecture)');
        
        try {
            // Show loading state
            moduleGrid.innerHTML = `
                <div class="no-modules-message">
                    <div class="loading-spinner"></div>
                    <div class="module-icon-large">📦</div>
                    <p>Conectando con el servidor...</p>
                    <small>Obteniendo módulos disponibles</small>
                </div>
            `;

            // Fetch modules from portal server
            const moduleUrl = this.getModuleRepositoryUrl();
            this.logger.info('CoreDeskApp', `Fetching modules from URL: ${moduleUrl}`);
            console.log('[CoreDeskApp] Debug: Attempting to fetch modules from:', moduleUrl);
            console.log('[CoreDeskApp] Debug: User agent:', navigator.userAgent);
            console.log('[CoreDeskApp] Debug: Location hostname:', window.location.hostname);
            console.log('[CoreDeskApp] Debug: *** FORCING PRODUCTION URLS ***');
            
            // Force cache bypass
            const cacheBuster = Date.now();
            const finalUrl = `${moduleUrl}?cb=${cacheBuster}`;
            console.log('[CoreDeskApp] Debug: Cache-busted URL:', finalUrl);
            
            const response = await fetch(finalUrl, {
                cache: 'no-cache',
                headers: {
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache'
                }
            });
            console.log('[CoreDeskApp] Debug: Response status:', response.status);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            console.log('[CoreDeskApp] Debug: API response:', data);
            
            if (!data.success || !data.modules) {
                throw new Error('Invalid API response');
            }
            
            const modules = data.modules;
            this.logger.info('CoreDeskApp', `Loaded ${modules.length} modules from server`);
            console.log('[CoreDeskApp] Debug: Modules loaded:', modules);
            console.log('[CoreDeskApp] Debug: *** SUCCESS - MODULES LOADED FROM PRODUCTION ***');
            
            // Update connection status to online
            this.updateConnectionStatus('online');
            
            if (modules.length === 0) {
                moduleGrid.innerHTML = `
                    <div class="no-modules-message">
                        <div class="module-icon-large">📦</div>
                        <p>No hay módulos disponibles en el servidor</p>
                        <small>Contacte al administrador</small>
                    </div>
                `;
                return;
            }
            
            // Show modules from server
            console.log('[CoreDeskApp] *** CALLING displayServerModules ***');
            this.displayServerModules(moduleGrid, modules);
            console.log('[CoreDeskApp] *** displayServerModules COMPLETED ***');
            
        } catch (error) {
            this.logger.error('CoreDeskApp', 'Failed to load modules from server:', error);
            
            // Update connection status to offline
            this.updateConnectionStatus('offline');
            
            // Show offline notification
            this.showOfflineNotification(moduleGrid, error);
        }
    }

    /**
     * Display modules fetched from server
     */
    displayServerModules(moduleGrid, modules) {
        console.log('[CoreDeskApp] *** displayServerModules() CALLED ***');
        console.log('[CoreDeskApp] *** MODULES TO DISPLAY:', modules);
        
        const moduleCards = modules.map(module => {
            const isDownloadable = module.downloadUrl && module.downloadUrl.trim() !== '';
            const isInstalled = this.isModuleInstalled(module.id);
            
            console.log(`[CoreDeskApp] Module ${module.id}: downloadable=${isDownloadable}, installed=${isInstalled}`);
            
            let statusText, statusClass, actionButton;
            
            if (isInstalled) {
                statusText = 'Instalado';
                statusClass = 'status-installed';
                actionButton = `
                    <button class="uninstall-btn" data-module="${module.id}">
                        🗑️ Desinstalar
                    </button>
                `;
            } else if (isDownloadable) {
                statusText = 'Disponible';
                statusClass = 'status-available';
                // Use the package endpoint for JSON module data instead of tar.gz download
                const packageUrl = `https://coredeskpro.com/api/modules/${module.id}/latest/package`;
                actionButton = `
                    <button class="install-btn" data-module="${module.id}" data-url="${packageUrl}">
                        📥 Instalar
                    </button>
                `;
            } else {
                statusText = 'No disponible';
                statusClass = 'status-unavailable';
                actionButton = '';
            }
            
            return `
                <div class="module-card ${isInstalled ? 'installed' : (isDownloadable ? 'available' : 'unavailable')}" data-module="${module.id}">
                    <div class="module-icon">📦</div>
                    <h4>${module.name}</h4>
                    <p>${module.description}</p>
                    <div class="module-details">
                        <small>Versión: ${module.version || 'N/A'}</small>
                        <small>Tamaño: ${module.size || 'N/A'}</small>
                        <small>Descargas: ${module.downloads || 0}</small>
                    </div>
                    <div class="module-status">
                        <span class="${statusClass}">${statusText}</span>
                    </div>
                    ${actionButton ? `
                        <div class="module-actions">
                            ${actionButton}
                        </div>
                    ` : ''}
                </div>
            `;
        }).join('');

        moduleGrid.innerHTML = `
            <div class="modules-container">
                <div class="module-cards-container">
                    ${moduleCards}
                </div>
                <div class="module-actions-footer">
                    <button id="install-third-party-btn" class="install-third-party-btn-modern">
                        <span class="btn-icon">+</span>
                        <span class="btn-text">Instalar de archivo</span>
                    </button>
                </div>
            </div>
        `;
        
        console.log('[CoreDeskApp] *** MODULE GRID UPDATED WITH PRODUCTION DATA ***');
        console.log('[CoreDeskApp] *** THIRD-PARTY BUTTON SHOULD BE VISIBLE ***');
        
        // Add click handlers for install/uninstall buttons
        this.setupModuleInstallHandlers(moduleGrid);
    }

    /**
     * Check if a module is installed
     */
    isModuleInstalled(moduleId) {
        const dynamicManager = window.exclusiveModuleController?.dynamicModuleManager;
        if (dynamicManager) {
            return dynamicManager.isModuleInstalled(moduleId);
        }
        return false;
    }

    /**
     * Setup event handlers for module install buttons
     */
    setupModuleInstallHandlers(moduleGrid) {
        const installButtons = moduleGrid.querySelectorAll('.install-btn');
        installButtons.forEach(button => {
            button.addEventListener('click', async (e) => {
                e.preventDefault();
                const moduleId = button.getAttribute('data-module');
                const downloadUrl = button.getAttribute('data-url');
                await this.handleModuleInstall(moduleId, downloadUrl, button);
            });
        });

        const uninstallButtons = moduleGrid.querySelectorAll('.uninstall-btn');
        uninstallButtons.forEach(button => {
            button.addEventListener('click', async (e) => {
                e.preventDefault();
                const moduleId = button.getAttribute('data-module');
                await this.handleModuleUninstall(moduleId, button);
            });
        });

        // Handle third-party install button
        const thirdPartyBtn = moduleGrid.querySelector('#install-third-party-btn');
        if (thirdPartyBtn) {
            thirdPartyBtn.addEventListener('click', () => {
                this.toggleModuleDropzoneVisibility();
            });
        }
    }

    /**
     * Get the module repository URL based on configuration or environment
     */
    getModuleRepositoryUrl() {
        // Check if there's a configured URL in CoreDeskAuth
        if (window.CoreDeskAuth?.api?.moduleRepositoryUrl) {
            return window.CoreDeskAuth.api.moduleRepositoryUrl;
        }
        
        // FORCED PRODUCTION URL - Always use production server for module loading
        // This ensures consistent behavior regardless of environment
        console.log('[CoreDeskApp] FORCE PRODUCTION: Using production module repository');
        return 'https://coredeskpro.com/api/modules';
    }

    /**
     * Show offline notification when server is not available
     */
    showOfflineNotification(moduleGrid, error) {
        this.logger.info('CoreDeskApp', 'Showing offline notification for modules');
        
        const moduleUrl = this.getModuleRepositoryUrl();
        const serverName = moduleUrl.includes('localhost') ? 'servidor local' : 'servidor remoto';
        
        moduleGrid.innerHTML = `
            <div class="no-modules-message error offline">
                <div class="module-icon-large">🌐</div>
                <h3>Sin conexión al ${serverName}</h3>
                <p>No se pueden cargar los módulos disponibles</p>
                <div class="error-details">
                    <small>Servidor: ${moduleUrl}</small>
                    <small>Error: ${error.message}</small>
                </div>
                <div class="offline-actions">
                    <button class="retry-btn" onclick="window.app.showServerModules(document.getElementById('dynamic-module-grid'))">
                        🔄 Reintentar conexión
                    </button>
                </div>
                <div class="offline-info">
                    <p>💡 Los módulos se cargarán automáticamente cuando se restablezca la conexión</p>
                    ${moduleUrl.includes('localhost') ? 
                        '<p>🔧 Asegúrate de que el servidor portal esté ejecutándose localmente</p>' : 
                        '<p>🔧 Verifica tu conexión a internet y que el servidor esté disponible</p>'
                    }
                </div>
            </div>
        `;
    }

    /**
     * Handle module installation from backend
     */
    async handleModuleInstall(moduleId, downloadUrl, button) {
        // Store original button text before try block
        const originalText = button.innerHTML;
        
        try {
            // Update button state
            button.disabled = true;
            button.innerHTML = '⏳ Instalando...';
            
            this.logger.info('CoreDeskApp', `Installing module ${moduleId} from ${downloadUrl}`);
            
            // Fetch module data from the portal API
            this.logger.info('CoreDeskApp', `Attempting to fetch from: ${downloadUrl}`);
            
            let response = await fetch(downloadUrl, {
                headers: {
                    'Accept': 'application/json',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Content-Type': 'application/json'
                }
            });
            
            if (!response.ok) {
                // Try alternative URL patterns if the first one fails
                if (response.status === 404) {
                    this.logger.warn('CoreDeskApp', `Primary URL failed with 404, trying alternative formats...`);
                    
                    // Try without "latest" in the path
                    const alternativeUrl = downloadUrl.replace('/latest/package', '/download');
                    this.logger.info('CoreDeskApp', `Trying alternative URL: ${alternativeUrl}`);
                    
                    const altResponse = await fetch(alternativeUrl, {
                        headers: {
                            'Accept': 'application/json',
                            'Accept-Encoding': 'gzip, deflate, br'
                        }
                    });
                    
                    if (altResponse.ok) {
                        // Use the alternative response
                        response = altResponse;
                    } else {
                        throw new Error(`Module not available. Tried multiple endpoints. Last error: ${response.status} ${response.statusText}`);
                    }
                } else {
                    throw new Error(`Failed to download module: ${response.status} ${response.statusText}`);
                }
            }
            
            // Check if response is actually JSON and handle accordingly
            const contentType = response.headers.get('content-type');
            let moduleData;
            
            if (!contentType || !contentType.includes('application/json')) {
                // If not JSON, treat as binary/file download
                const blob = await response.blob();
                moduleData = {
                    name: moduleId,
                    version: '1.0.0',
                    description: `Module ${moduleId}`,
                    size: blob.size,
                    type: contentType || 'application/octet-stream',
                    data: blob
                };
                
                // Store the blob for installation
                console.log(`[CoreDeskApp] Downloaded binary module: ${moduleData.name} (${moduleData.size} bytes)`);
            } else {
                moduleData = await response.json();
            }
            
            // Create module package from API response using proper ModulePackage constructor
            let modulePackage;
            
            if (moduleData.data && moduleData.data instanceof Blob) {
                // Handle binary module
                console.log(`[CoreDeskApp] Processing binary module: ${moduleId}`);
                
                // For binary modules, create a proper ModulePackage instance
                const packageData = {
                    manifest: {
                        id: moduleId,
                        name: moduleData.name || moduleId,
                        version: moduleData.version || '1.0.0',
                        description: moduleData.description || `Module ${moduleId}`,
                        main: `${moduleId.charAt(0).toUpperCase() + moduleId.slice(1)}Module`,
                        author: 'Portal',
                        category: 'general'
                    },
                    // For binary modules, create a CSP-safe module class
                    moduleCode: this.createCSPSafeModuleClass(moduleId, 'binary', moduleData.size || 0),
                    styles: '',
                    assets: {},
                    binaryData: moduleData.data,
                    isBinary: true
                };
                
                // Create proper ModulePackage instance
                modulePackage = new window.ModulePackage(packageData, {
                    validateManifest: true,
                    strictMode: false, // Allow binary modules with basic validation
                    allowUnsafeCode: true // Binary modules need special handling
                });
            } else {
                // Handle JSON module  
                const packageData = {
                    manifest: {
                        id: moduleId,
                        name: moduleData.name || moduleId,
                        version: moduleData.version || '1.0.0',
                        description: moduleData.description || `Module ${moduleId}`,
                        main: `${moduleId.charAt(0).toUpperCase() + moduleId.slice(1)}Module`,
                        author: moduleData.author || 'Portal',
                        category: moduleData.category || 'general'
                    },
                    moduleCode: moduleData.code || this.createCSPSafeModuleClass(moduleId, 'json', 0),
                    styles: moduleData.styles || '',
                    assets: moduleData.assets || {}
                };
                
                // Create proper ModulePackage instance
                modulePackage = new window.ModulePackage(packageData, {
                    validateManifest: true,
                    strictMode: false,
                    allowUnsafeCode: true
                });
            }
            
            // Install via dynamic module manager
            const dynamicManager = window.exclusiveModuleController?.dynamicModuleManager;
            if (dynamicManager) {
                await dynamicManager.installModulePackage(modulePackage);
                
                // Update UI to show installed
                button.innerHTML = '🗑️ Desinstalar';
                button.classList.remove('install-btn');
                button.classList.add('uninstall-btn', 'installed');
                button.disabled = false;
                
                // Update status in the card
                const card = button.closest('.module-card');
                const statusSpan = card.querySelector('.status-available, .status-unavailable');
                if (statusSpan) {
                    statusSpan.textContent = 'Instalado';
                    statusSpan.className = 'status-installed';
                }
                
                // Update card classes
                card.classList.remove('available', 'unavailable');
                card.classList.add('installed');
                
                this.logger.info('CoreDeskApp', `Module ${moduleId} installation completed`);
                
                // Refresh module grid to show updated state
                setTimeout(async () => {
                    await this.loadDynamicModulesGrid();
                }, 1000);
                
            } else {
                // Dynamic module manager not available yet - try to initialize it
                this.logger.warn('CoreDeskApp', 'Dynamic Module Manager not available, attempting to initialize...');
                
                if (window.exclusiveModuleController) {
                    // Try to initialize the dynamic system
                    try {
                        await window.exclusiveModuleController.initializeDynamicSystem();
                        const retryManager = window.exclusiveModuleController?.dynamicModuleManager;
                        
                        if (retryManager) {
                            await retryManager.installModulePackage(modulePackage);
                            
                            // Update button to show success
                            button.innerHTML = '✅ Instalado';
                            button.classList.add('installed');
                            
                            // Update status in the card
                            const card = button.closest('.module-card');
                            const statusSpan = card.querySelector('.status-available, .status-unavailable');
                            if (statusSpan) {
                                statusSpan.textContent = 'Instalado';
                                statusSpan.className = 'status-installed';
                            }
                            
                            this.logger.info('CoreDeskApp', `Module ${moduleId} installed successfully after retry`);
                            return;
                        }
                    } catch (initError) {
                        this.logger.error('CoreDeskApp', 'Failed to initialize dynamic module manager:', initError);
                    }
                }
                
                throw new Error('Dynamic Module Manager not available and could not be initialized');
            }
            
        } catch (error) {
            this.logger.error('CoreDeskApp', `Failed to install module ${moduleId}:`, error);
            
            // Reset button
            button.disabled = false;
            button.innerHTML = '❌ Error';
            
            // Show error dialog
            alert(`Error instalando ${moduleId}: ${error.message}`);
            
            // Reset to original state after 3 seconds
            setTimeout(() => {
                button.innerHTML = originalText;
            }, 3000);
        }
    }

    /**
     * Handle module uninstall from server modules
     */
    async handleModuleUninstall(moduleId, button) {
        try {
            // Update button state
            const originalText = button.innerHTML;
            button.disabled = true;
            button.innerHTML = '⏳ Desinstalando...';
            
            this.logger.info('CoreDeskApp', `Uninstalling module ${moduleId}`);
            
            // Uninstall via dynamic module manager
            const dynamicManager = window.exclusiveModuleController?.dynamicModuleManager;
            if (dynamicManager) {
                await dynamicManager.uninstallModule(moduleId);
                
                // Update UI to show uninstalled state
                button.innerHTML = '✅ Desinstalado';
                button.classList.add('uninstalled');
                
                // Update status in the card
                const card = button.closest('.module-card');
                const statusSpan = card.querySelector('.status-installed, .status-available');
                if (statusSpan) {
                    statusSpan.textContent = 'Disponible';
                    statusSpan.className = 'status-available';
                }
                
                // Change button to install button after a delay
                setTimeout(() => {
                    button.innerHTML = '📥 Instalar';
                    button.classList.remove('uninstalled', 'uninstall-btn');
                    button.classList.add('install-btn');
                    button.disabled = false;
                    button.setAttribute('data-url', `https://coredeskpro.com/api/modules/${moduleId}/download`);
                }, 2000);
                
                this.logger.info('CoreDeskApp', `Module ${moduleId} uninstalled successfully`);
                
            } else {
                throw new Error('Dynamic Module Manager not available');
            }
            
        } catch (error) {
            this.logger.error('CoreDeskApp', `Failed to uninstall module ${moduleId}:`, error);
            
            // Reset button
            button.disabled = false;
            button.innerHTML = '❌ Error';
            
            // Show error dialog
            alert(`Error desinstalando ${moduleId}: ${error.message}`);
            
            // Reset to original state after 3 seconds
            setTimeout(() => {
                button.innerHTML = originalText;
            }, 3000);
        }
    }

    /**
     * Install module from portal by downloading and extracting it
     */
    async installModuleFromPortal(moduleId, downloadUrl) {
        try {
            this.logger.info('CoreDeskApp', `Starting installation of module ${moduleId}`);
            
            // Use ModuleDownloader if available, or fetch directly
            if (window.ModuleDownloader) {
                // Use the dedicated downloader service
                const downloader = new window.ModuleDownloader();
                const downloadResult = await downloader.downloadModule(moduleId);
                
                if (downloadResult.success) {
                    this.logger.info('CoreDeskApp', `Module ${moduleId} downloaded successfully`);
                    return await this.extractAndInstallModule(moduleId, downloadResult.data);
                } else {
                    throw new Error(downloadResult.error || 'Download failed');
                }
            } else {
                // Fallback to direct download
                const response = await fetch(downloadUrl);
                if (!response.ok) {
                    throw new Error(`Download failed: ${response.status} ${response.statusText}`);
                }
                
                const moduleData = await response.arrayBuffer();
                this.logger.info('CoreDeskApp', `Module ${moduleId} downloaded via fetch (${moduleData.byteLength} bytes)`);
                
                return await this.extractAndInstallModule(moduleId, moduleData);
            }
            
        } catch (error) {
            this.logger.error('CoreDeskApp', `Failed to install module ${moduleId}:`, error);
            throw error;
        }
    }

    /**
     * Extract and install module package
     */
    async extractAndInstallModule(moduleId, moduleData) {
        try {
            this.logger.info('CoreDeskApp', `Extracting and installing module ${moduleId}`);

            // Create ModulePackage from downloaded data
            let modulePackage;

            if (moduleData.manifest && moduleData.moduleCode) {
                // Handle complete module package
                modulePackage = new window.ModulePackage(moduleData, {
                    validateManifest: true,
                    strictMode: false,
                    allowUnsafeCode: true
                });
            } else {
                // Handle JSON module data
                const packageData = {
                    manifest: {
                        id: moduleId,
                        name: moduleData.name || moduleId,
                        version: moduleData.version || '1.0.0',
                        description: moduleData.description || `Module ${moduleId}`,
                        main: `${moduleId.charAt(0).toUpperCase() + moduleId.slice(1)}Module`,
                        author: moduleData.author || 'Portal',
                        category: moduleData.category || 'general'
                    },
                    moduleCode: moduleData.code || this.createCSPSafeModuleClass(moduleId, 'json', 0),
                    styles: moduleData.styles || '',
                    assets: moduleData.assets || {}
                };

                modulePackage = new window.ModulePackage(packageData, {
                    validateManifest: true,
                    strictMode: false,
                    allowUnsafeCode: true
                });
            }

            // Install via dynamic module manager
            const dynamicManager = window.exclusiveModuleController?.dynamicModuleManager;
            if (dynamicManager) {
                await dynamicManager.installModulePackage(modulePackage);

                this.logger.info('CoreDeskApp', `Module ${moduleId} installed successfully`);
                await this.refreshModuleRegistry();

                return {
                    success: true,
                    path: modulePackage.installPath || `modulos/${moduleId}`,
                    source: 'portal'
                };
            } else {
                throw new Error('Dynamic Module Manager not available');
            }

        } catch (error) {
            this.logger.error('CoreDeskApp', `Failed to extract/install module ${moduleId}:`, error);
            throw error;
        }
    }

    /**
     * Refresh the module registry after installation
     */
    async refreshModuleRegistry() {
        try {
            // Refresh the dynamic module system
            if (this.exclusiveModuleController?.dynamicModuleManager) {
                await this.exclusiveModuleController.dynamicModuleManager.refreshRegistry();
                this.logger.info('CoreDeskApp', 'Module registry refreshed');
            }
            
            // Reload the modules grid to show updated status
            if (this.dynamicModuleGrid) {
                await this.loadDynamicModulesGrid();
            }
            
        } catch (error) {
            this.logger.warn('CoreDeskApp', 'Failed to refresh module registry:', error);
        }
    }

    /**
     * Setup drag-and-drop functionality for third-party modules
     */
    setupModuleDropzone() {
        const dropzone = document.getElementById('module-dropzone');
        const fileInput = document.getElementById('module-file-input');
        const browseBtn = document.getElementById('browse-modules-btn');

        if (!dropzone || !fileInput || !browseBtn) {
            this.logger.warn('CoreDeskApp', 'Module dropzone elements not found');
            return;
        }

        this.logger.info('CoreDeskApp', 'Setting up module dropzone');

        // Hide dropzone by default
        const dropzoneSection = document.querySelector('.module-dropzone-section');
        if (dropzoneSection) {
            dropzoneSection.style.display = 'none';
        }

        // Prevent default drag behaviors
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropzone.addEventListener(eventName, this.preventDefaults.bind(this), false);
            document.body.addEventListener(eventName, this.preventDefaults.bind(this), false);
        });

        // Highlight drop area when item is dragged over it
        ['dragenter', 'dragover'].forEach(eventName => {
            dropzone.addEventListener(eventName, () => {
                dropzone.classList.add('dragover');
            }, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropzone.addEventListener(eventName, () => {
                dropzone.classList.remove('dragover');
            }, false);
        });

        // Handle dropped files
        dropzone.addEventListener('drop', this.handleDrop.bind(this), false);

        // Handle browse button click
        browseBtn.addEventListener('click', () => {
            fileInput.click();
        });

        // Handle file input change
        fileInput.addEventListener('change', (e) => {
            this.handleFiles(e.target.files);
        });

        this.logger.info('CoreDeskApp', 'Module dropzone setup completed');
    }

    /**
     * Toggle visibility of the module dropzone section
     */
    toggleModuleDropzoneVisibility() {
        const dropzoneSection = document.querySelector('.module-dropzone-section');
        if (dropzoneSection) {
            const isVisible = dropzoneSection.style.display !== 'none';
            dropzoneSection.style.display = isVisible ? 'none' : 'block';
            
            // Update button text
            const thirdPartyBtn = document.getElementById('install-third-party-btn');
            if (thirdPartyBtn) {
                thirdPartyBtn.textContent = isVisible ? '📥 Instalar Módulo de Terceros' : '❌ Cerrar';
            }
            
            this.logger.info('CoreDeskApp', `Module dropzone ${isVisible ? 'hidden' : 'shown'}`);
        }
    }

    /**
     * Prevent default drag behaviors
     */
    preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    /**
     * Handle drop event
     */
    handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        this.handleFiles(files);
    }

    /**
     * Handle files for module installation
     */
    async handleFiles(files) {
        if (!files || files.length === 0) {
            return;
        }

        const validExtensions = ['.tar.gz', '.tgz', '.zip'];
        const moduleFiles = Array.from(files).filter(file => {
            const fileName = file.name.toLowerCase();
            return validExtensions.some(ext => fileName.endsWith(ext));
        });

        if (moduleFiles.length === 0) {
            alert('Por favor selecciona archivos con extensiones válidas (.tar.gz, .zip)');
            return;
        }

        for (const file of moduleFiles) {
            await this.installThirdPartyModule(file);
        }
    }

    /**
     * Install third-party module from file
     */
    async installThirdPartyModule(file) {
        const dropzone = document.getElementById('module-dropzone');
        let progressOverlay = null;

        try {
            this.logger.info('CoreDeskApp', `Installing third-party module: ${file.name}`);

            // Show upload progress
            progressOverlay = this.showUploadProgress(dropzone, file.name);

            // Read file as ArrayBuffer
            const fileData = await this.readFileAsArrayBuffer(file);
            
            // Extract module ID from filename
            const moduleId = this.extractModuleIdFromFilename(file.name);

            // Try to extract and create a module package
            let modulePackage;
            
            if (file.name.endsWith('.zip')) {
                // For ZIP files, we'd need a ZIP extraction library
                // For now, simulate the installation
                modulePackage = {
                    id: moduleId,
                    name: moduleId.charAt(0).toUpperCase() + moduleId.slice(1),
                    version: '1.0.0',
                    description: `Third-party module from ${file.name}`,
                    manifest: {
                        id: moduleId,
                        name: moduleId.charAt(0).toUpperCase() + moduleId.slice(1),
                        version: '1.0.0',
                        description: `Third-party module from ${file.name}`,
                        main: 'index.js',
                        author: 'Third Party',
                        category: 'third-party'
                    },
                    moduleCode: `// Third-party module: ${moduleId}\nconsole.log('Third-party module ${moduleId} loaded');`,
                    styles: '',
                    assets: {}
                };
            } else {
                // For .tar.gz files, we'd need a tar extraction library
                // For now, simulate the installation
                modulePackage = {
                    id: moduleId,
                    name: moduleId.charAt(0).toUpperCase() + moduleId.slice(1),
                    version: '1.0.0',
                    description: `Third-party module from ${file.name}`,
                    manifest: {
                        id: moduleId,
                        name: moduleId.charAt(0).toUpperCase() + moduleId.slice(1),
                        version: '1.0.0',
                        description: `Third-party module from ${file.name}`,
                        main: 'index.js',
                        author: 'Third Party',
                        category: 'third-party'
                    },
                    moduleCode: `// Third-party module: ${moduleId}\nconsole.log('Third-party module ${moduleId} loaded');`,
                    styles: '',
                    assets: {}
                };
            }

            // Install via dynamic module manager
            const dynamicManager = window.exclusiveModuleController?.dynamicModuleManager;
            if (dynamicManager) {
                await dynamicManager.installModulePackage(modulePackage);
                
                // Show success
                this.showUploadSuccess(progressOverlay, file.name);
                
                // Hide dropzone after successful installation
                setTimeout(async () => {
                    this.toggleModuleDropzoneVisibility();
                    // Refresh the module grid to show the new module
                    await this.loadDynamicModulesGrid();
                }, 2000);
                
                this.logger.info('CoreDeskApp', `Third-party module ${file.name} installed successfully`);
            } else {
                throw new Error('Dynamic Module Manager not available');
            }

        } catch (error) {
            this.logger.error('CoreDeskApp', `Failed to install third-party module ${file.name}:`, error);
            this.showUploadError(progressOverlay, file.name, error.message);
        }
    }

    /**
     * Show upload progress overlay
     */
    showUploadProgress(dropzone, fileName) {
        const progressOverlay = document.createElement('div');
        progressOverlay.className = 'upload-progress active';
        progressOverlay.innerHTML = `
            <div class="progress-circle"></div>
            <div class="progress-text">
                <div>Instalando ${fileName}</div>
                <small>Por favor espera...</small>
            </div>
        `;
        dropzone.appendChild(progressOverlay);
        dropzone.classList.add('uploading');
        return progressOverlay;
    }

    /**
     * Show upload success
     */
    showUploadSuccess(progressOverlay, fileName) {
        if (progressOverlay) {
            progressOverlay.innerHTML = `
                <div style="font-size: 48px; color: #10b981;">✅</div>
                <div class="progress-text">
                    <div>${fileName} instalado</div>
                    <small>Módulo disponible en el sistema</small>
                </div>
            `;
            setTimeout(() => {
                progressOverlay.remove();
                document.getElementById('module-dropzone').classList.remove('uploading');
            }, 3000);
        }
    }

    /**
     * Show upload error
     */
    showUploadError(progressOverlay, fileName, errorMessage) {
        if (progressOverlay) {
            progressOverlay.innerHTML = `
                <div style="font-size: 48px; color: #ef4444;">❌</div>
                <div class="progress-text">
                    <div>Error instalando ${fileName}</div>
                    <small>${errorMessage}</small>
                </div>
            `;
            setTimeout(() => {
                progressOverlay.remove();
                document.getElementById('module-dropzone').classList.remove('uploading');
            }, 5000);
        }
    }

    /**
     * Read file as ArrayBuffer
     */
    readFileAsArrayBuffer(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result);
            reader.onerror = () => reject(new Error('Failed to read file'));
            reader.readAsArrayBuffer(file);
        });
    }

    /**
     * Extract module ID from filename
     */
    extractModuleIdFromFilename(fileName) {
        // Remove extensions and extract module name
        const baseName = fileName.replace(/\.(tar\.gz|tgz|zip)$/i, '');
        // Remove version numbers if present (e.g., "modulename-1.0.0" -> "modulename")
        const moduleId = baseName.replace(/-[\d\.]+$/, '').toLowerCase();
        return moduleId || 'unknown-module';
    }

    /**
     * Setup window state management for maximized/restored states
     */
    setupWindowStateManagement() {
        // Listen for window state changes from main process
        if (window.electronAPI && window.electronAPI.on) {
            window.electronAPI.on('window:maximized', (event, isMaximized) => {
                this.handleWindowStateChange(isMaximized);
            });
        }
    }

    /**
     * Handle window state changes (maximized/restored)
     */
    handleWindowStateChange(isMaximized) {
        this.logger.info('CoreDeskApp', `Window state changed: ${isMaximized ? 'maximized' : 'restored'}`);
        
        const body = document.body;
        
        if (isMaximized) {
            // Add maximized class to adjust layout
            body.classList.add('window-maximized');
            
            // Remove any padding/margins that might cause offset
            body.style.margin = '0';
            body.style.padding = '0';
            
            // Ensure the app container takes full space
            const appContainer = document.querySelector('.app-container');
            if (appContainer) {
                appContainer.style.margin = '0';
                appContainer.style.padding = '0';
                appContainer.style.borderRadius = '0';
            }
            
            // Adjust titlebar for maximized state
            const titlebar = document.querySelector('.titlebar');
            if (titlebar) {
                titlebar.style.borderRadius = '0';
            }
            
        } else {
            // Remove maximized class to restore normal layout
            body.classList.remove('window-maximized');
            
            // Restore normal styling
            body.style.margin = '';
            body.style.padding = '';
            
            const appContainer = document.querySelector('.app-container');
            if (appContainer) {
                appContainer.style.margin = '';
                appContainer.style.padding = '';
                appContainer.style.borderRadius = '';
            }
            
            const titlebar = document.querySelector('.titlebar');
            if (titlebar) {
                titlebar.style.borderRadius = '';
            }
        }
    }

    /**
     * Setup account dropdown menu functionality
     */
    setupAccountDropdown() {
        const accountButton = document.getElementById('account-button');
        const accountDropdown = document.getElementById('account-dropdown');
        const myAccountOption = document.getElementById('my-account-option');
        const logoutOption = document.getElementById('logout-option');

        if (!accountButton || !accountDropdown) {
            this.logger.warn('CoreDeskApp', 'Account dropdown elements not found');
            return;
        }

        // Toggle dropdown on account button click
        accountButton.addEventListener('click', (e) => {
            e.stopPropagation();
            const isVisible = accountDropdown.style.display === 'block';
            
            if (isVisible) {
                this.hideAccountDropdown();
            } else {
                this.showAccountDropdown();
            }
        });

        // Handle my account option click
        if (myAccountOption) {
            myAccountOption.addEventListener('click', () => {
                this.handleMyAccountClick();
                this.hideAccountDropdown();
            });
        }

        // Handle logout option click
        if (logoutOption) {
            logoutOption.addEventListener('click', () => {
                this.handleLogoutClick();
                this.hideAccountDropdown();
            });
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => {
            if (!accountButton.contains(e.target) && !accountDropdown.contains(e.target)) {
                this.hideAccountDropdown();
            }
        });

        // Close dropdown on escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.hideAccountDropdown();
            }
        });

        this.logger.info('CoreDeskApp', 'Account dropdown initialized');
    }

    /**
     * Show account dropdown menu
     */
    showAccountDropdown() {
        const accountDropdown = document.getElementById('account-dropdown');
        const dropdownArrow = document.querySelector('.dropdown-arrow');
        
        if (accountDropdown) {
            accountDropdown.style.display = 'block';
            accountDropdown.classList.add('show');
        }
        
        if (dropdownArrow) {
            dropdownArrow.style.transform = 'rotate(180deg)';
        }
    }

    /**
     * Hide account dropdown menu
     */
    hideAccountDropdown() {
        const accountDropdown = document.getElementById('account-dropdown');
        const dropdownArrow = document.querySelector('.dropdown-arrow');
        
        if (accountDropdown) {
            accountDropdown.style.display = 'none';
            accountDropdown.classList.remove('show');
        }
        
        if (dropdownArrow) {
            dropdownArrow.style.transform = 'rotate(0deg)';
        }
    }

    /**
     * Handle my account option click
     */
    handleMyAccountClick() {
        this.logger.info('CoreDeskApp', 'My Account clicked');
        
        // TODO: Open account modal or settings panel
        if (window.notificationCenter) {
            window.notificationCenter.showToast('Mi Cuenta', 'Funcionalidad próximamente disponible', 'info');
        } else {
            alert('Mi Cuenta - Funcionalidad próximamente disponible');
        }
    }

    /**
     * Handle logout option click
     */
    async handleLogoutClick() {
        this.logger.info('CoreDeskApp', 'Logout clicked');
        
        try {
            // Show confirmation dialog
            const confirmed = confirm('¿Estás seguro de que quieres cerrar sesión?');
            if (!confirmed) {
                return;
            }

            this.logger.info('CoreDeskApp', 'User confirmed logout, proceeding...');

            // Perform logout through authentication manager
            if (window.unifiedAuthManager && typeof window.unifiedAuthManager.logout === 'function') {
                this.logger.info('CoreDeskApp', 'Using UnifiedAuthManager for logout');
                
                const logoutResult = await window.unifiedAuthManager.logout();
                this.logger.info('CoreDeskApp', 'Logout result:', logoutResult);
                
                if (logoutResult !== false) {
                    this.logger.info('CoreDeskApp', 'Logout successful, redirecting to login');
                    
                    // Show success message
                    if (window.notificationCenter) {
                        window.notificationCenter.showToast('Sesión Cerrada', 'Has cerrado sesión exitosamente', 'success');
                    }
                    
                    // Set logout flag to prevent immediate redirect back
                    sessionStorage.setItem('_justLoggedOut', 'true');
                    
                    // Redirect to login page after short delay
                    setTimeout(() => {
                        window.location.href = 'login.html';
                    }, 500);
                } else {
                    throw new Error('Logout returned false');
                }
            } else {
                // Fallback logout - clear local storage and redirect
                this.logger.warn('CoreDeskApp', 'UnifiedAuthManager not available, performing manual logout');
                this.performManualLogout();
            }
        } catch (error) {
            this.logger.error('CoreDeskApp', 'Error during logout:', error);
            
            // Try manual logout as fallback
            this.logger.info('CoreDeskApp', 'Attempting manual logout as fallback');
            try {
                this.performManualLogout();
            } catch (fallbackError) {
                this.logger.error('CoreDeskApp', 'Manual logout also failed:', fallbackError);
                
                // Show error message
                if (window.notificationCenter) {
                    window.notificationCenter.showToast('Error', 'Error al cerrar sesión. Inténtalo de nuevo.', 'error');
                } else {
                    alert('Error al cerrar sesión. Inténtalo de nuevo.');
                }
            }
        }
    }

    /**
     * Perform manual logout when auth manager fails
     */
    performManualLogout() {
        this.logger.info('CoreDeskApp', 'Performing manual logout');
        
        // Clear authentication data manually
        if (window.CoreDeskAuth && window.CoreDeskAuth.utils && window.CoreDeskAuth.utils.clearAuthData) {
            window.CoreDeskAuth.utils.clearAuthData();
        }
        
        // Clear token manager data
        if (window.tokenManager && window.tokenManager.clearTokens) {
            window.tokenManager.clearTokens();
        }
        
        // Clear all known auth keys manually
        const authKeys = [
            'coredesk_token',
            'coredesk_refresh_token',
            'coredesk_token_expiry',
            'coredesk_user',
            'coredesk_encryption_key',
            'auth_token',
            'auth_user'
        ];
        
        authKeys.forEach(key => {
            try {
                localStorage.removeItem(key);
                sessionStorage.removeItem(key);
            } catch (e) {
                this.logger.warn('CoreDeskApp', `Failed to clear ${key}:`, e);
            }
        });
        
        this.logger.info('CoreDeskApp', 'Manual logout completed, redirecting to login');
        
        // Show success message
        if (window.notificationCenter) {
            window.notificationCenter.showToast('Sesión Cerrada', 'Has cerrado sesión exitosamente', 'success');
        }
        
        // Set logout flag to prevent immediate redirect back
        sessionStorage.setItem('_justLoggedOut', 'true');
        
        // Force redirect
        setTimeout(() => {
            window.location.href = 'login.html';
        }, 500);
    }

    /**
     * Create a CSP-safe module class by executing code immediately and creating global reference
     * @param {string} moduleId - Module identifier
     * @param {string} moduleType - Module type (binary or json)
     * @param {number} size - Module size in bytes
     * @returns {string} Global reference to the module class
     */
    createCSPSafeModuleClass(moduleId, moduleType, size) {
        const className = `${moduleId.charAt(0).toUpperCase() + moduleId.slice(1)}Module`;
        
        // Execute the module constructor code immediately and attach to window
        // This avoids CSP issues with dynamic code evaluation
        const ModuleConstructor = function(packageData) {
            this.packageData = packageData;
            this.name = moduleId;
            this.isLoaded = false;
            console.log(`${moduleType} module ${moduleId} constructor called`);
        };
        
        ModuleConstructor.prototype.initialize = async function() {
            console.log(`${moduleType} module ${moduleId} initialized successfully`);
            this.isLoaded = true;
            return true;
        };
        
        ModuleConstructor.prototype.activate = async function() {
            console.log(`${moduleType} module ${moduleId} activated`);
            this.isActive = true;
            return true;
        };
        
        ModuleConstructor.prototype.deactivate = async function() {
            console.log(`${moduleType} module ${moduleId} deactivated`);
            this.isActive = false;
            return true;
        };
        
        ModuleConstructor.prototype.render = function() {
            // Create basic module content
            const moduleContent = document.createElement('div');
            moduleContent.className = 'module-content';
            
            // Build content using string concatenation to avoid template literal issues
            let headerContent = '<div class="module-header">' +
                '<h2>' + (this.name.charAt(0).toUpperCase() + this.name.slice(1)) + ' Module</h2>' +
                '<p>Type: ' + moduleType + '</p>';
            
            if (moduleType === 'binary') {
                headerContent += '<p>Size: ' + size + ' bytes</p>';
            }
            headerContent += '</div>';
            
            const bodyContent = '<div class="module-body">' +
                '<p>🚀 Module ' + this.name + ' is now active and running!</p>' +
                '<div class="module-features">' +
                    '<h3>Features:</h3>' +
                    '<ul>' +
                        '<li>✅ Successfully installed</li>' +
                        '<li>✅ CSP-compliant execution</li>' +
                        '<li>✅ Dynamic loading capability</li>' +
                        '<li>✅ Persistent storage</li>' +
                    '</ul>' +
                '</div>' +
                '<div class="module-actions">' +
                    '<button onclick="console.log(\'' + this.name + ' action executed!\')">Test Action</button>' +
                '</div>' +
            '</div>';
            
            moduleContent.innerHTML = headerContent + bodyContent;
            return moduleContent;
        };
        
        ModuleConstructor.prototype.getInfo = function() {
            const info = {
                name: this.name,
                type: moduleType,
                isLoaded: this.isLoaded,
                isActive: this.isActive || false
            };
            
            if (moduleType === 'binary') {
                info.size = size;
            }
            
            return info;
        };
        
        // Attach to window for CSP-safe access
        window[className] = ModuleConstructor;
        
        // Return the global reference that ModulePackage can safely access
        return `window.${className}`;
    }

}

// Initialize the application when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    try {
        window.coreDeskApp = new CoreDeskApp();
    } catch (error) {
        console.error('Failed to initialize CoreDesk application:', error);
    }
});

// Export for other modules if needed
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CoreDeskApp;
}