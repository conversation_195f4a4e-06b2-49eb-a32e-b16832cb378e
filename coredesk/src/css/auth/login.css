/* Login Page Styles */

/* Base Authentication Layout */
.auth-body {
    margin: 0;
    padding: 0;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #1a1f2e 0%, #2a1b3d 50%, #44318d 100%);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    overflow: hidden;
}

.auth-body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 50%, rgba(120, 80, 200, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(80, 120, 200, 0.2) 0%, transparent 50%),
        radial-gradient(circle at 40% 20%, rgba(200, 80, 120, 0.2) 0%, transparent 50%);
    pointer-events: none;
}

.auth-container {
    width: 100%;
    max-width: 1400px;
    height: 90vh;
    max-height: 900px;
    display: flex;
    background: rgba(22, 27, 40, 0.98);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 20px;
    box-shadow: 
        0 25px 50px rgba(0, 0, 0, 0.7),
        0 0 100px rgba(120, 80, 200, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    overflow: hidden;
    position: relative;
    z-index: 1;
}

/* Expanded drag area - toda la barra superior */
.auth-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 60px; /* Área más grande para mejor usabilidad */
    -webkit-app-region: drag;
    cursor: move;
    z-index: 10;
    pointer-events: auto;
    background: linear-gradient(to bottom, 
        rgba(139, 125, 216, 0.08) 0%, 
        rgba(139, 125, 216, 0.04) 50%, 
        transparent 100%
    );
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
}

/* Visual drag indicator mejorado */
.auth-container::after {
    content: '═══ ⋮⋮ ═══';
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    color: rgba(255, 255, 255, 0.5);
    font-size: 14px;
    letter-spacing: 3px;
    z-index: 11;
    pointer-events: none;
    opacity: 0.7;
    transition: all 0.3s ease;
    font-weight: 500;
    text-shadow: 0 0 10px rgba(139, 125, 216, 0.3);
}

.auth-container:hover::after {
    opacity: 1;
    color: rgba(255, 255, 255, 0.8);
    text-shadow: 0 0 15px rgba(139, 125, 216, 0.5);
}

/* Left Side - Login Form */
.auth-left {
    flex: 1;
    padding: 80px 60px 60px 60px; /* Top padding aumentado para acomodar la nueva área de arrastre */
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    background: rgba(255, 255, 255, 0.04);
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    overflow-y: auto;
    overflow-x: hidden;
    min-height: 0; /* Permite que flexbox funcione correctamente con scroll */
    position: relative;
}

/* Drag area expandida en el panel izquierdo */
.auth-left::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 60px; /* Coincide con la barra superior */
    -webkit-app-region: drag;
    cursor: move;
    z-index: 5;
    background: linear-gradient(to bottom, 
        rgba(139, 125, 216, 0.06) 0%, 
        rgba(139, 125, 216, 0.03) 50%, 
        transparent 100%
    );
    pointer-events: auto;
    border-top-left-radius: 20px;
}

/* Custom scrollbar styling for left panel */
.auth-left::-webkit-scrollbar {
    width: 6px;
}

.auth-left::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
}

.auth-left::-webkit-scrollbar-thumb {
    background: rgba(139, 125, 216, 0.3);
    border-radius: 3px;
}

.auth-left::-webkit-scrollbar-thumb:hover {
    background: rgba(139, 125, 216, 0.5);
}

.auth-brand {
    margin-bottom: 40px;
    flex-shrink: 0; /* Evita que se encoja demasiado */
    -webkit-app-region: drag; /* Permite arrastrar la ventana desde el brand */
    cursor: move;
}

.brand-title {
    font-size: 36px;
    font-weight: 700;
    color: #ffffff;
    margin: 0 0 10px 0;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
    -webkit-app-region: drag; /* Permite arrastrar desde el título */
    cursor: move;
}

.brand-pro {
    background: linear-gradient(135deg, #7c4dff 0%, #d0c4f7 50%, #e573db 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: inline;
    text-shadow: 0 0 40px rgba(139, 125, 216, 0.5);
    filter: brightness(1.1) contrast(1.2);
}

.brand-tagline {
    color: rgba(255, 255, 255, 0.85);
    font-size: 16px;
    margin: 0;
    text-shadow: 0 1px 4px rgba(0, 0, 0, 0.4);
    -webkit-app-region: drag; /* Permite arrastrar desde el tagline */
    cursor: move;
}

/* Tab Navigation */
.auth-tabs {
    display: flex;
    gap: 0;
    margin-bottom: 30px;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 10px;
    padding: 4px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

.auth-tab {
    flex: 1;
    padding: 12px 24px;
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.75);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 8px;
    position: relative;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    -webkit-app-region: no-drag; /* Evita interferencia con el arrastre */
}

.auth-tab:hover {
    color: rgba(255, 255, 255, 0.95);
    background: rgba(255, 255, 255, 0.05);
}

.auth-tab.active {
    background: rgba(139, 125, 216, 0.3);
    color: #ffffff;
    box-shadow: 0 2px 8px rgba(139, 125, 216, 0.3);
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
}

.auth-tab.active::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 3px;
    background: #8b7dd8;
    border-radius: 2px;
}

/* Tab Content */
.auth-tab-content {
    display: none;
    animation: fadeIn 0.3s ease;
}

.auth-tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.auth-form-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0; /* Permite scroll cuando sea necesario */
}

/* Form Styles */
.auth-form {
    max-width: 400px;
    width: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.form-header {
    margin-bottom: 30px;
}

.form-header h3 {
    color: #ffffff;
    font-size: 24px;
    margin: 0 0 10px 0;
    text-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
}

.form-header p {
    color: rgba(255, 255, 255, 0.75);
    font-size: 14px;
    margin: 0;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 8px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.form-input {
    width: 100%;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-size: 15px;
    transition: all 0.3s ease;
    box-sizing: border-box;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    -webkit-app-region: no-drag; /* Evita interferencia con el arrastre */
}

.form-input:focus {
    outline: none;
    background: rgba(255, 255, 255, 0.12);
    border-color: #9c88e8;
    box-shadow: 
        inset 0 1px 3px rgba(0, 0, 0, 0.2),
        0 0 0 3px rgba(139, 125, 216, 0.2);
}

.form-input::placeholder {
    color: rgba(255, 255, 255, 0.4);
}

.license-input {
    font-family: 'Courier New', monospace;
    letter-spacing: 2px;
    text-transform: uppercase;
}

.form-error {
    display: block;
    color: #ff6b6b;
    font-size: 12px;
    margin-top: 5px;
    min-height: 16px;
}

/* Form Options */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.85);
    font-size: 14px;
    cursor: pointer;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.checkbox-label input[type="checkbox"] {
    margin-right: 8px;
    width: 16px;
    height: 16px;
    cursor: pointer;
    -webkit-app-region: no-drag; /* Evita interferencia con el arrastre */
}

.forgot-password {
    color: #9c88e8;
    font-size: 14px;
    text-decoration: none;
    transition: color 0.3s ease;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    -webkit-app-region: no-drag; /* Evita interferencia con el arrastre */
}

.forgot-password:hover {
    color: #b8a8f2;
    text-shadow: 0 0 8px rgba(156, 136, 232, 0.4);
}

/* Buttons */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 15px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    position: relative;
    overflow: hidden;
    -webkit-app-region: no-drag; /* Evita interferencia con el arrastre */
}

.btn-block {
    width: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, #9c88e8 0%, #7c62d8 100%);
    color: #ffffff;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
    box-shadow: 0 4px 12px rgba(139, 125, 216, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-primary:hover {
    transform: translateY(-2px);
    background: linear-gradient(135deg, #a898ec 0%, #8870dc 100%);
    box-shadow: 0 8px 25px rgba(139, 125, 216, 0.4);
    border-color: rgba(255, 255, 255, 0.2);
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 4px 12px rgba(139, 125, 216, 0.3);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.08);
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.2);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.12);
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.btn-link {
    background: transparent;
    border: none;
    color: #9c88e8;
    font-size: 14px;
    cursor: pointer;
    padding: 8px 12px;
    transition: all 0.3s ease;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.btn-link:hover {
    color: #b8a8f2;
    background: rgba(139, 125, 216, 0.15);
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(139, 125, 216, 0.2);
}

.btn-link .icon {
    margin-right: 6px;
}

/* Cancel Button */
.auth-cancel-container {
    margin-top: 30px;
    text-align: center;
    padding-top: 20px;
    padding-bottom: 20px; /* Asegura espacio en la parte inferior */
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    flex-shrink: 0; /* Evita que se encoja */
}

.btn-cancel {
    background: rgba(255, 69, 58, 0.9);
    color: #ffffff;
    border: 1px solid rgba(255, 69, 58, 0.4);
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    box-shadow: 0 2px 8px rgba(255, 69, 58, 0.3);
}

.btn-cancel:hover {
    background: rgba(255, 69, 58, 1);
    border-color: rgba(255, 69, 58, 0.6);
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(255, 69, 58, 0.4);
}

.btn-cancel:active {
    transform: translateY(0);
}

.btn-cancel .btn-icon {
    font-size: 12px;
    line-height: 1;
}

/* Button Loading State */
.btn-loader {
    display: none;
}

.spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top-color: #ffffff;
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Form Divider */
.form-divider {
    text-align: center;
    margin: 20px 0;
    position: relative;
}

.form-divider::before,
.form-divider::after {
    content: '';
    position: absolute;
    top: 50%;
    width: 45%;
    height: 1px;
    background: rgba(255, 255, 255, 0.2);
}

.form-divider::before {
    left: 0;
}

.form-divider::after {
    right: 0;
}

.form-divider span {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    padding: 0 10px;
    background: rgba(22, 27, 40, 0.98);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* License Options */
.license-options {
    display: flex;
    justify-content: space-between;
    margin: 20px 0;
}

/* Form Footer */
.form-footer {
    text-align: center;
    margin-top: 30px;
}

.form-footer p {
    color: rgba(255, 255, 255, 0.75);
    font-size: 14px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.form-footer a {
    color: #9c88e8;
    text-decoration: none;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.form-footer a:hover {
    color: #b8a8f2;
    text-shadow: 0 0 8px rgba(156, 136, 232, 0.4);
}

/* Registration Steps */
.register-step {
    display: none;
    flex: 1;
    overflow-y: auto;
}

.register-step.active {
    display: flex;
    flex-direction: column;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.form-actions {
    display: flex;
    gap: 10px;
    margin-top: 30px;
}

.form-actions .btn {
    flex: 1;
}

/* Password Strength */
.password-strength {
    margin-top: 10px;
}

.strength-bar {
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    overflow: hidden;
}

.strength-fill {
    height: 100%;
    transition: all 0.3s ease;
    border-radius: 2px;
}

.strength-fill[data-strength="0"] { width: 0%; }
.strength-fill[data-strength="1"] { width: 25%; background: #ff6b6b; }
.strength-fill[data-strength="2"] { width: 50%; background: #ffd93d; }
.strength-fill[data-strength="3"] { width: 75%; background: #6bcf7f; }
.strength-fill[data-strength="4"] { width: 100%; background: #4caf50; }

.strength-text {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.75);
    margin-top: 5px;
    display: block;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Terms Container */
.terms-container {
    background: rgba(255, 255, 255, 0.06);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

.terms-container h4 {
    color: #ffffff;
    font-size: 18px;
    margin: 0 0 15px 0;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
}

.terms-content {
    color: rgba(255, 255, 255, 0.85);
    font-size: 14px;
    line-height: 1.6;
    margin-bottom: 20px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.terms-content ul {
    margin: 10px 0;
    padding-left: 20px;
}

.terms-content li {
    margin-bottom: 5px;
}

/* Progress Indicator */
.register-progress {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 30px;
    margin-bottom: 20px; /* Espacio adicional en la parte inferior */
    flex-shrink: 0; /* Evita que se encoja */
}

.progress-step {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.15);
    color: rgba(255, 255, 255, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.progress-step.active {
    background: #9c88e8;
    color: #ffffff;
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0 2px 8px rgba(139, 125, 216, 0.4);
}

.progress-line {
    width: 60px;
    height: 2px;
    background: rgba(255, 255, 255, 0.2);
    margin: 0 10px;
}

/* Right Side - Feature Showcase */
.auth-right {
    flex: 1.2;
    padding: 40px;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    background: linear-gradient(135deg, rgba(139, 125, 216, 0.15) 0%, rgba(107, 91, 199, 0.25) 100%);
    position: relative;
    overflow-y: auto;
    overflow-x: hidden;
    border-left: 1px solid rgba(255, 255, 255, 0.1);
}

/* Custom scrollbar styling */
.auth-right::-webkit-scrollbar {
    width: 6px;
}

.auth-right::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.auth-right::-webkit-scrollbar-thumb {
    background: rgba(139, 125, 216, 0.5);
    border-radius: 3px;
}

.auth-right::-webkit-scrollbar-thumb:hover {
    background: rgba(139, 125, 216, 0.7);
}

.auth-right::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: 
        radial-gradient(circle at 30% 30%, rgba(139, 125, 216, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 70% 70%, rgba(107, 91, 199, 0.1) 0%, transparent 50%);
    animation: rotate 30s linear infinite;
}

.auth-right::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        linear-gradient(135deg, transparent 0%, rgba(139, 125, 216, 0.05) 50%, transparent 100%);
    pointer-events: none;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.feature-showcase {
    position: relative;
    z-index: 1;
    max-width: 500px;
    width: 100%;
    margin-top: 20px;
}

.showcase-header {
    margin-bottom: 25px;
}

.feature-showcase h2 {
    /* font-size: 48px; */
    font-weight: 700;
    color: #ffffff;
    margin: 0;
    line-height: 0.95;
    text-align: left;
}

.main-title {
    font-size: 40px;
    font-weight: 800;
    color: #ffffff;
    display: block;
    letter-spacing: -1px;
    margin-bottom: 0px;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
}

.sub-title {
    font-size: 40px;
    font-weight: 700;
    color: #ffffff;
    display: block;
    margin-top: -5px;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
}

.highlight {
    background: linear-gradient(135deg, #7c4dff 0%, #d0c4f7 50%, #e573db 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: inline;
    text-shadow: 0 0 40px rgba(139, 125, 216, 0.5);
    filter: brightness(1.1) contrast(1.2);
}

.showcase-description {
    color: rgba(255, 255, 255, 0.9);
    font-size: 16px;
    line-height: 1.6;
    margin-bottom: 40px;
    text-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
}

/* Agent Cards */
.agent-cards {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-bottom: 30px;
    margin-top: 25px;
}

.agent-card {
    background: rgba(255, 255, 255, 0.12);
    border: 1px solid rgba(255, 255, 255, 0.25);
    border-radius: 12px;
    padding: 18px;
    transition: all 0.3s ease;
    backdrop-filter: blur(15px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.agent-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(139, 125, 216, 0.4);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.4);
}

.agent-icon {
    width: 44px;
    height: 44px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 22px;
    margin-bottom: 12px;
    box-shadow: 0 6px 18px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.agent-card:hover .agent-icon {
    transform: scale(1.05);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.3);
}

.agent-icon.legal {
    background: linear-gradient(135deg, #5c8dc4 0%, #4c78a8 100%);
    box-shadow: 0 8px 24px rgba(76, 120, 168, 0.4);
}

.agent-icon.financial {
    background: linear-gradient(135deg, #8b7dd8 0%, #6b5bc7 100%);
    box-shadow: 0 6px 18px rgba(139, 125, 216, 0.4);
}

.agent-icon.audit {
    background: linear-gradient(135deg, #e07a5f 0%, #c9594a 100%);
    box-shadow: 0 6px 18px rgba(224, 122, 95, 0.4);
}

.agent-icon.protocol {
    background: linear-gradient(135deg, #81b29a 0%, #6d9d85 100%);
    box-shadow: 0 6px 18px rgba(129, 178, 154, 0.4);
}

.agent-card h3 {
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 8px 0;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
}

.agent-card p {
    color: rgba(255, 255, 255, 0.85);
    font-size: 12px;
    line-height: 1.4;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Security Badges */
.showcase-footer {
    text-align: center;
}

.security-badges {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.badge {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 13px;
    color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    gap: 6px;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.modal-content {
    background: #2a2f3f;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    max-width: 90%;
    animation: modalIn 0.3s ease;
}

.modal-sm {
    width: 400px;
}

@keyframes modalIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h3 {
    margin: 0;
    color: #ffffff;
    font-size: 20px;
}

.modal-body {
    padding: 20px;
}

.modal-body.text-center {
    text-align: center;
}

.success-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: #4caf50;
    color: #ffffff;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 30px;
    margin-bottom: 20px;
}

.modal-body p {
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.loading-content {
    text-align: center;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 3px solid rgba(255, 255, 255, 0.1);
    border-top-color: #8b7dd8;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

.loading-content p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
}

/* Drag-related styles */
.window-draggable {
    cursor: move;
}

.window-draggable:hover {
    background: rgba(139, 125, 216, 0.05);
    transition: background 0.2s ease;
}

/* Prevent text selection during drag */
.no-select, 
body.dragging,
body.dragging * {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

/* Style for the top drag area */
.window-drag-area {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.window-drag-area:hover,
.auth-container:hover .window-drag-area {
    opacity: 1;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .auth-container {
        max-width: 100%;
        height: 100vh;
        border-radius: 0;
    }
    
    .auth-left,
    .auth-right {
        padding: 50px 40px 40px 40px; /* Top padding ajustado */
    }
    
    .auth-left {
        /* Asegurar scroll en pantallas medianas */
        overflow-y: auto;
        justify-content: flex-start;
        padding-top: 70px; /* Espacio para área de arrastre */
        padding-bottom: 30px;
    }
    
    .brand-title {
        font-size: clamp(28px, 4vw, 32px);
    }
    
    .main-title,
    .sub-title {
        font-size: clamp(32px, 5vw, 36px);
    }
}

@media (max-width: 768px) {
    .auth-container {
        flex-direction: column;
        height: 100vh;
    }
    
    /* Área de arrastre expandida para móviles */
    .auth-container::before {
        height: 50px; /* Área más grande para móviles */
    }
    
    .auth-container::after {
        top: 16px;
        font-size: 12px;
        letter-spacing: 2px;
    }
    
    .auth-right {
        display: none;
    }
    
    .auth-left {
        border-right: none;
        padding: clamp(20px, 4vw, 30px);
        justify-content: flex-start;
        padding-top: clamp(60px, 8vh, 70px); /* Espacio para área de arrastre expandida */
        padding-bottom: clamp(20px, 4vh, 30px);
        /* Asegurar scroll en móviles */
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    /* Área de arrastre expandida en el panel izquierdo para móviles */
    .auth-left::before {
        height: 50px;
    }
    
    .auth-brand {
        margin-bottom: clamp(20px, 4vh, 30px);
        /* Mejor área de arrastre en móviles */
        padding: 10px 0;
    }
    
    .brand-title {
        font-size: clamp(24px, 6vw, 28px);
    }
    
    .brand-tagline {
        font-size: clamp(14px, 3vw, 16px);
    }
    
    .auth-tabs {
        flex-direction: column;
        gap: 4px;
    }
    
    .auth-tab {
        padding: clamp(10px, 2.5vw, 14px) clamp(16px, 4vw, 24px);
        font-size: clamp(13px, 3vw, 15px);
    }
    
    .form-input {
        padding: clamp(10px, 2.5vw, 14px) clamp(12px, 3vw, 16px);
        font-size: clamp(14px, 3.5vw, 16px);
    }
    
    .btn {
        padding: clamp(10px, 2.5vw, 14px) clamp(16px, 4vw, 24px);
        font-size: clamp(14px, 3.5vw, 16px);
    }
    
    .form-actions {
        flex-direction: column;
        gap: clamp(8px, 2vw, 12px);
    }
    
    .form-actions .btn {
        width: 100%;
    }
    
    /* Asegurar que los elementos no se corten */
    .register-progress {
        margin: 20px 0;
    }
    
    .auth-cancel-container {
        margin-top: 20px;
        padding-bottom: 30px;
    }
}

/* High DPI optimizations for login screen */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .auth-body {
        /* Better font smoothing for high DPI */
        -webkit-font-smoothing: subpixel-antialiased;
        font-feature-settings: "liga" 1, "kern" 1;
    }
    
    .form-input {
        /* Slightly larger inputs for better touch/click targets on high DPI */
        padding: 14px 18px;
        font-size: 15px;
    }
    
    .btn {
        padding: 14px 26px;
        font-size: 15px;
    }
    
    .brand-title {
        /* Better scaling for high DPI displays */
        font-size: clamp(32px, 4vw, 40px);
    }
    
    .main-title,
    .sub-title {
        font-size: clamp(36px, 5vw, 44px);
    }
}

/* Windows specific optimizations */
@media screen and (-ms-high-contrast: none), (-ms-high-contrast: active) {
    .form-input,
    .btn {
        /* Better ClearType rendering on Windows */
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
}

/* Linux/WSL specific optimizations */
@supports (font-variant-ligatures: normal) {
    .auth-body {
        /* Improved font rendering for Linux */
        font-variant-ligatures: normal;
        text-rendering: geometricPrecision;
    }
}

/* Very small screens (mobile) */
@media (max-width: 480px) {
    .auth-left {
        padding: clamp(15px, 3vw, 20px);
        padding-top: clamp(55px, 8vh, 65px); /* Espacio para área de arrastre expandida */
        padding-bottom: clamp(20px, 4vh, 30px);
        /* Mejorar scroll en pantallas muy pequeñas */
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
        height: 100vh; /* Altura completa para permitir scroll */
    }
    
    .auth-brand {
        margin-bottom: clamp(15px, 3vh, 25px);
    }
    
    .brand-title {
        font-size: clamp(20px, 5vw, 24px);
        margin-bottom: 8px;
    }
    
    .brand-tagline {
        font-size: clamp(12px, 3vw, 14px);
        margin-bottom: 20px;
    }
    
    .auth-form {
        max-width: 100%;
    }
    
    .form-group {
        margin-bottom: 16px;
    }
    
    .form-header h3 {
        font-size: clamp(18px, 4vw, 22px);
    }
    
    .form-header p {
        font-size: clamp(12px, 3vw, 14px);
    }
    
    /* Asegurar que todos los elementos sean visibles */
    .auth-tabs {
        margin-bottom: 20px;
    }
    
    .auth-form-container {
        min-height: 0;
        flex: 1;
    }
    
    .register-progress {
        margin: 15px 0 20px 0;
    }
    
    .auth-cancel-container {
        margin-top: 15px;
        padding-bottom: 25px;
    }
}

/* Ultra-wide displays */
@media (min-width: 2560px) {
    .auth-container {
        max-width: 1600px;
        max-height: 1000px;
    }
    
    .auth-left {
        padding: 80px;
    }
    
    .auth-right {
        padding: 60px;
    }
    
    .brand-title {
        font-size: 42px;
    }
    
    .main-title,
    .sub-title {
        font-size: 48px;
    }
}