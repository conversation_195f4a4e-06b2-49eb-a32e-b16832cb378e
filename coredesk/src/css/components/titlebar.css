/* Title bar styling */
.titlebar {
    height: var(--titlebar-height);
    background: var(--titlebar-background);
    border-bottom: 1px solid var(--border-primary);
    color: var(--titlebar-foreground);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--spacing-sm);
    user-select: none;
    -webkit-app-region: drag;
    z-index: var(--z-titlebar);
    transition: background-color var(--transition-normal), color var(--transition-normal), border-color var(--transition-normal);
}

.titlebar * {
    -webkit-app-region: no-drag;
}

/* Title bar sections */
.titlebar-left,
.titlebar-center,
.titlebar-right {
    display: flex;
    align-items: center;
    height: 100%;
}

.titlebar-left {
    flex-shrink: 0;
}

.titlebar-center {
    flex: 1;
    justify-content: center;
}

.titlebar-right {
    flex-shrink: 0;
    gap: var(--spacing-md);
}

/* App logo section */
.app-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
}

.app-icon {
    width: var(--icon-size-sm);
    height: var(--icon-size-sm);
}

.app-name {
    color: var(--titlebar-foreground);
}

.app-version {
    color: var(--foreground-muted);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-normal);
}

/* Country indicator */
.country-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    font-size: var(--font-size-xs);
    cursor: pointer;
    transition: background var(--transition-fast);
}

.country-indicator:hover {
    background: var(--background-quaternary);
}

.flag-icon {
    font-size: 18px;
}

#country-name {
    color: var(--foreground-secondary);
}

/* Panel toggles */
.panel-toggles {
    display: flex;
    gap: 4px;
}

.panel-toggle {
    width: 36px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius);
    transition: background var(--transition-fast);
    /* Reset button styles */
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
}

.panel-toggle:hover {
    background: var(--background-quaternary);
}

.panel-toggle.active {
    background: var(--accent-primary);
}

.panel-icon {
    width: var(--icon-size-sm);
    height: var(--icon-size-sm);
    background-color: var(--foreground-muted);
    mask-repeat: no-repeat;
    mask-position: center;
    mask-size: 100%;
    position: relative; /* Necesario para posicionar pseudo-elementos */
}

.panel-toggle:hover .panel-icon,
.panel-toggle.active .panel-icon {
    background-color: var(--foreground-primary);
}

.icon-layout-sidebar-left {
    mask-image: url('../../assets/icons/leftPanel-mask.svg');
}
.icon-layout-sidebar-right {
    mask-image: url('../../assets/icons/rightPanel-mask.svg');
}
.icon-layout-panel {
    mask-image: url('../../assets/icons/bottomPanel-mask.svg');
}

/* Fallback para iconos de panel usando pseudo-elementos */
.icon-layout-sidebar-left::after {
    content: '⊞';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 18px;
    display: none;
    color: var(--foreground-muted);
    font-weight: bold;
}

.icon-layout-sidebar-right::after {
    content: '⊟';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 18px;
    display: none;
    color: var(--foreground-muted);
    font-weight: bold;
}

.icon-layout-panel::after {
    content: '⊡';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 18px;
    display: none;
    color: var(--foreground-muted);
    font-weight: bold;
}

/* Fallback activado por JavaScript */
.icon-fallback-active .panel-icon {
    background-color: transparent !important;
    mask-image: none !important;
}

.icon-fallback-active .icon-layout-sidebar-left::after,
.icon-fallback-active .icon-layout-sidebar-right::after,
.icon-fallback-active .icon-layout-panel::after {
    display: block !important;
    color: var(--foreground-muted) !important;
}

.icon-fallback-active .panel-toggle:hover .icon-layout-sidebar-left::after,
.icon-fallback-active .panel-toggle:hover .icon-layout-sidebar-right::after,
.icon-fallback-active .panel-toggle:hover .icon-layout-panel::after,
.icon-fallback-active .panel-toggle.active .icon-layout-sidebar-left::after,
.icon-fallback-active .panel-toggle.active .icon-layout-sidebar-right::after,
.icon-fallback-active .panel-toggle.active .icon-layout-panel::after {
    color: var(--foreground-primary) !important;
}

/* Activar fallback si mask-image no funciona */
@supports not (mask-image: url('data:image/svg+xml,<svg></svg>')) {
    .panel-icon {
        background-color: transparent !important;
    }

    .icon-layout-sidebar-left::after,
    .icon-layout-sidebar-right::after,
    .icon-layout-panel::after {
        display: block;
        color: var(--foreground-muted);
    }

    .panel-toggle:hover .icon-layout-sidebar-left::after,
    .panel-toggle:hover .icon-layout-sidebar-right::after,
    .panel-toggle:hover .icon-layout-panel::after,
    .panel-toggle.active .icon-layout-sidebar-left::after,
    .panel-toggle.active .icon-layout-sidebar-right::after,
    .panel-toggle.active .icon-layout-panel::after {
        color: var(--foreground-primary);
    }
}

/* Notifications menu */
.notifications-menu {
    margin-right: 10px;
    position: relative;
}

.notifications-button {
    background: none;
    border: none;
    color: var(--foreground-secondary);
    cursor: pointer;
    padding: 8px 12px;
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    position: relative;
}

.notifications-button:hover {
    background: var(--background-quaternary);
    color: var(--foreground-primary);
}

.notifications-icon {
    font-size: 16px;
}

/* Account menu */
.account-menu {
    position: relative;
}

.account-button {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    font-size: var(--font-size-xs);
    transition: background var(--transition-fast);
    /* Reset button styles */
    background: none;
    border: none;
    cursor: pointer;
}

.account-button:hover {
    background: var(--background-quaternary);
}

.account-text {
    color: var(--foreground-secondary);
}

.account-icon {
    font-size: var(--icon-size-sm);
    transition: color var(--transition-normal);
}

.dropdown-arrow {
    font-size: 10px;
    transition: transform var(--transition-fast);
    margin-left: 2px;
}

.account-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 4px;
    min-width: 180px;
    background: var(--background-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    overflow: hidden;
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    cursor: pointer;
    transition: background var(--transition-fast);
    font-size: var(--font-size-sm);
}

.dropdown-item:hover {
    background: var(--background-secondary);
}

.dropdown-icon {
    font-size: var(--icon-size-sm);
    width: 16px;
    text-align: center;
}

.dropdown-text {
    color: var(--foreground-primary);
    flex: 1;
}

.dropdown-separator {
    height: 1px;
    background: var(--border-primary);
    margin: 4px 0;
}

/* Dropdown animation */
.account-dropdown.show {
    animation: dropdownSlideIn 0.2s ease-out;
}

@keyframes dropdownSlideIn {
    from {
        opacity: 0;
        transform: translateY(-8px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Window controls */
.window-controls {
    display: flex;
    height: 100%;
}

.window-control {
    width: 46px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background var(--transition-fast);
    /* Reset button styles */
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
}

.window-control:hover {
    background: var(--background-quaternary);
}

.window-control.close:hover {
    background: #e74c3c;
}

.control-icon {
    position: relative;
    width: 12px;
    height: 12px;
}

/* Iconos de controles de ventana mejorados */
.icon-minimize {
    position: relative;
    width: 12px;
    height: 12px;
}

.icon-minimize::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background-color: var(--foreground-muted);
}

.window-control:hover .icon-minimize::after {
    background-color: var(--foreground-primary);
}

.icon-maximize {
    position: relative;
    width: 12px;
    height: 12px;
    border: 1px solid var(--foreground-muted);
    background: transparent;
}

.window-control:hover .icon-maximize {
    border-color: var(--foreground-primary);
}

.icon-close {
    position: relative;
    width: 12px;
    height: 12px;
}

.icon-close::before,
.icon-close::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 12px;
    height: 1px;
    background-color: var(--foreground-muted);
    transform-origin: center;
}

.icon-close::before {
    transform: translate(-50%, -50%) rotate(45deg);
}

.icon-close::after {
    transform: translate(-50%, -50%) rotate(-45deg);
}

.window-control:hover .icon-close::before,
.window-control:hover .icon-close::after {
    background-color: var(--foreground-primary);
}

.window-control.close:hover .icon-close::before,
.window-control.close:hover .icon-close::after {
    background-color: #ffffff;
}

.icon-close::before {
    transform: rotate(45deg);
}

.icon-close::after {
    transform: rotate(-45deg);
}

/* macOS specific adjustments */
.platform-darwin .window-controls {
    order: -1;
}

.platform-darwin .titlebar-left {
    padding-left: 70px;
}

/* Windows specific adjustments */
.platform-win32 .window-control {
    font-family: "Segoe Fluent Icons", "Segoe MDL2 Assets", sans-serif;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .titlebar-center {
        display: none;
    }
    
    .account-text {
        display: none;
    }
    
    .app-version {
        display: none;
    }
}

@media (max-width: 480px) {
    .panel-toggles {
        display: none;
    }
    
    .window-control {
        width: 40px;
    }
}

/* Explicit theme support for title bar */
[data-theme="light"] .titlebar {
    background: var(--titlebar-background);
    color: var(--titlebar-foreground);
    border-bottom-color: var(--border-primary);
}

[data-theme="dark"] .titlebar {
    background: var(--titlebar-background);
    color: var(--titlebar-foreground);
    border-bottom-color: var(--border-primary);
}

/* Ensure theme colors are applied to title bar elements */
[data-theme="light"] .app-name {
    color: var(--titlebar-foreground);
}

[data-theme="dark"] .app-name {
    color: var(--titlebar-foreground);
}

[data-theme="light"] .panel-icon {
    background-color: var(--foreground-muted);
}

[data-theme="dark"] .panel-icon {
    background-color: var(--foreground-muted);
}

[data-theme="light"] .panel-toggle:hover .panel-icon,
[data-theme="light"] .panel-toggle.active .panel-icon {
    background-color: var(--foreground-primary);
}

[data-theme="dark"] .panel-toggle:hover .panel-icon,
[data-theme="dark"] .panel-toggle.active .panel-icon {
    background-color: var(--foreground-primary);
}

/* Theme support for account button */
[data-theme="light"] .account-button {
    color: var(--foreground-primary);
}

[data-theme="dark"] .account-button {
    color: var(--foreground-primary);
}

[data-theme="light"] .account-button:hover {
    background: var(--background-quaternary);
}

[data-theme="dark"] .account-button:hover {
    background: var(--background-quaternary);
}

[data-theme="light"] .account-text {
    color: var(--foreground-secondary);
}

[data-theme="dark"] .account-text {
    color: var(--foreground-secondary);
}

[data-theme="light"] .account-icon {
    color: var(--foreground-muted);
}

[data-theme="dark"] .account-icon {
    color: var(--foreground-muted);
}