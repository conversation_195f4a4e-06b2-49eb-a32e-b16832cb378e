/* Panel Content Styles */

/* Explorer Content */
.explorer-content {
    padding: var(--spacing-sm);
    height: 100%;
    overflow-y: auto;
    /* Eliminar cualquier margen no deseado */
    margin: 0;
    border: none;
    outline: none;
}

.explorer-section {
    margin-bottom: 16px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 8px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    color: var(--foreground-secondary);
    border-bottom: 1px solid var(--border-secondary);
    margin-bottom: 8px;
}

.section-title {
    letter-spacing: 0.5px;
}

.section-action {
    background: none;
    border: none;
    color: var(--foreground-secondary);
    cursor: pointer;
    padding: 2px 4px;
    border-radius: 2px;
    font-size: 12px;
}

.section-action:hover {
    background: var(--background-hover);
    color: var(--foreground-primary);
}

.module-tree {
    padding-left: 4px;
}

.module-tree-item {
    display: flex;
    align-items: center;
    padding: 4px 8px;
    cursor: pointer;
    border-radius: 4px;
    font-size: 13px;
    gap: 8px;
}

.module-tree-item:hover {
    background: var(--background-hover);
}

.module-tree-item.active {
    background: var(--background-selection);
    color: var(--foreground-selection);
}

.tree-icon {
    font-size: 14px;
}

.tree-label {
    flex: 1;
}

.active-indicator {
    color: var(--accent-primary);
    font-size: 8px;
}

.recent-files {
    padding-left: 4px;
}

.file-item {
    display: flex;
    align-items: center;
    padding: 4px 8px;
    cursor: pointer;
    border-radius: 4px;
    font-size: 13px;
    gap: 8px;
}

.file-item:hover {
    background: var(--background-hover);
}

.file-icon {
    font-size: 14px;
}

.file-name {
    color: var(--foreground-primary);
}

/* Search Content */
.search-content {
    padding: 12px;
    height: 100%;
    overflow-y: auto;
}

.search-box {
    display: flex;
    margin-bottom: 12px;
    border: 1px solid var(--border-primary);
    border-radius: 4px;
    overflow: hidden;
}

.search-input {
    flex: 1;
    padding: 8px 12px;
    border: none;
    background: var(--input-background);
    color: var(--input-foreground);
    font-size: 13px;
}

.search-input:focus {
    outline: none;
    background: var(--input-background-focus);
}

.search-btn {
    padding: 8px 12px;
    border: none;
    background: var(--button-background);
    color: var(--button-foreground);
    cursor: pointer;
    font-size: 13px;
}

.search-btn:hover {
    background: var(--button-background-hover);
}

.search-filters {
    margin-bottom: 16px;
    padding: 8px;
    background: var(--background-secondary);
    border-radius: 4px;
}

.search-filters label {
    display: block;
    margin-bottom: 4px;
    font-size: 12px;
    color: var(--foreground-secondary);
    cursor: pointer;
}

.search-filters input[type="checkbox"] {
    margin-right: 6px;
}

.search-results {
    border-top: 1px solid var(--border-secondary);
    padding-top: 12px;
}

.no-results {
    color: var(--foreground-secondary);
    font-style: italic;
    text-align: center;
    font-size: 13px;
}

/* Cloud Content */
.cloud-content {
    padding: 12px;
    height: 100%;
    overflow-y: auto;
}

.cloud-status {
    margin-bottom: 16px;
    padding: 12px;
    background: var(--background-secondary);
    border-radius: 6px;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
}

.status-indicator.offline {
    color: var(--error);
}

.status-indicator.online {
    color: var(--success);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: currentColor;
}

.cloud-actions {
    margin-bottom: 16px;
}

.cloud-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    padding: 12px;
    margin-bottom: 8px;
    border: 1px solid var(--border-primary);
    background: var(--button-background);
    color: var(--button-foreground);
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
}

.cloud-btn:hover {
    background: var(--button-background-hover);
    border-color: var(--border-hover);
}

.btn-icon {
    font-size: 16px;
}

.cloud-storage {
    padding: 12px;
    background: var(--background-secondary);
    border-radius: 6px;
}

.storage-info {
    font-size: 12px;
    color: var(--foreground-secondary);
    margin-bottom: 8px;
}

.storage-bar {
    height: 6px;
    background: var(--background-tertiary);
    border-radius: 3px;
    overflow: hidden;
}

.storage-used {
    height: 100%;
    background: var(--accent-primary);
    transition: width 0.3s ease;
}

/* Modules Content */
.modules-content {
    padding: 12px;
    height: 100%;
    overflow-y: auto;
}

.modules-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--border-secondary);
}

.modules-header h4 {
    font-size: 14px;
    font-weight: 600;
    color: var(--foreground-primary);
    margin: 0;
}

.module-refresh-btn {
    background: none;
    border: none;
    color: var(--foreground-secondary);
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 14px;
}

.module-refresh-btn:hover {
    background: var(--background-hover);
    color: var(--foreground-primary);
}

.modules-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 16px;
}

.available-module-item {
    display: flex;
    gap: 12px;
    padding: 12px;
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    background: var(--background-secondary);
}

.available-module-item.available {
    border-color: var(--success);
    background: rgba(var(--success-rgb), 0.05);
}

.available-module-item.unavailable {
    opacity: 0.6;
    background: var(--background-tertiary);
}

.available-module-item.active {
    border-color: var(--accent-primary);
    background: rgba(var(--accent-primary-rgb), 0.1);
    border-width: 2px;
}

.module-icon-large {
    font-size: 24px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--background-tertiary);
    border-radius: 6px;
    flex-shrink: 0;
}

.module-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.module-name {
    font-size: 13px;
    font-weight: 600;
    color: var(--foreground-primary);
    margin: 0;
}

.module-description {
    font-size: 12px;
    color: var(--foreground-secondary);
    margin: 0;
    line-height: 1.3;
}

.module-actions {
    margin-top: 8px;
}

.activate-module-btn {
    padding: 4px 12px;
    border: 1px solid var(--accent-primary);
    background: var(--accent-primary);
    color: #ffffff;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    font-weight: 500;
}

.activate-module-btn:hover {
    background: var(--accent-primary-hover);
    border-color: var(--accent-primary-hover);
}

.deactivate-module-btn {
    padding: 4px 12px;
    border: 1px solid var(--error);
    background: var(--error);
    color: #ffffff;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    font-weight: 500;
}

.deactivate-module-btn:hover {
    background: #dc3545;
    border-color: #dc3545;
}

.coming-soon {
    font-size: 11px;
    color: var(--warning);
    font-weight: 500;
    padding: 4px 8px;
    background: rgba(var(--warning-rgb), 0.1);
    border-radius: 4px;
}

.modules-info {
    padding: 12px;
    background: var(--background-tertiary);
    border-radius: 6px;
    border-left: 4px solid var(--info);
}

.modules-info .info-text {
    font-size: 12px;
    color: var(--foreground-secondary);
    margin: 0;
    line-height: 1.4;
}

/* Module Sections */
.modules-section {
    margin-bottom: 20px;
}

.modules-section .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 8px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    color: var(--foreground-secondary);
    border-bottom: 1px solid var(--border-secondary);
    margin-bottom: 12px;
}

.browse-repository-btn {
    background: none;
    border: none;
    color: var(--foreground-secondary);
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 14px;
}

.browse-repository-btn:hover {
    background: var(--background-hover);
    color: var(--foreground-primary);
}

/* Repository Module Styles */
.repository-modules-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    min-height: 100px;
}

.repository-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 24px;
    background: var(--background-tertiary);
    border-radius: 6px;
    border: 2px dashed var(--border-secondary);
    text-align: center;
}

.repository-message .module-icon-large {
    font-size: 32px;
    margin-bottom: 12px;
    opacity: 0.7;
}

.repository-message p {
    font-size: 12px;
    color: var(--foreground-secondary);
    margin: 0;
    line-height: 1.4;
    max-width: 200px;
}

.repository-module-item {
    display: flex;
    gap: 12px;
    padding: 12px;
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    background: var(--background-secondary);
}

.repository-module-item.downloading {
    border-color: var(--accent-primary);
    background: rgba(var(--accent-primary-rgb), 0.05);
}

.install-module-btn {
    padding: 4px 12px;
    border: 1px solid var(--success);
    background: var(--success);
    color: #ffffff;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    font-weight: 500;
}

.install-module-btn:hover {
    background: #28a745;
    border-color: #28a745;
}

/* Compact module cards for left panel */
.repository-module-item.compact {
    padding: 8px;
    gap: 8px;
    margin-bottom: 8px;
}

.repository-module-item.compact .module-icon-small {
    width: 24px;
    height: 24px;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--background-tertiary);
    border-radius: 4px;
    flex-shrink: 0;
}

.repository-module-item.compact .module-details {
    flex: 1;
    min-width: 0;
}

.repository-module-item.compact .module-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 4px;
}

.repository-module-item.compact .module-name {
    font-size: 12px;
    font-weight: 600;
    margin: 0;
    line-height: 1.2;
    color: var(--foreground-primary);
}

.repository-module-item.compact .module-version {
    font-size: 10px;
    color: var(--foreground-secondary);
    background: var(--background-tertiary);
    padding: 2px 4px;
    border-radius: 3px;
    flex-shrink: 0;
}

.repository-module-item.compact .module-description {
    font-size: 10px;
    color: var(--foreground-secondary);
    margin: 0 0 6px 0;
    line-height: 1.3;
}

.repository-module-item.compact .module-actions {
    display: flex;
    justify-content: flex-end;
}

.repository-module-item.compact .install-module-btn.compact {
    padding: 3px 8px;
    font-size: 10px;
    border-radius: 3px;
}

/* Module content container styles */
.module-content-container {
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.module-content-container .module-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--border-primary);
}

.module-content-container .module-icon {
    width: 48px;
    height: 48px;
    font-size: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--background-tertiary);
    border-radius: 8px;
}

.module-content-container .module-title h2 {
    font-size: 18px;
    margin: 0 0 4px 0;
    color: var(--foreground-primary);
}

.module-content-container .module-title .module-description {
    font-size: 14px;
    color: var(--foreground-secondary);
    margin: 0;
}

.module-placeholder {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.module-placeholder .placeholder-content {
    text-align: center;
    max-width: 300px;
}

.module-placeholder .placeholder-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.6;
}

.module-placeholder h3 {
    font-size: 16px;
    margin: 0 0 8px 0;
    color: var(--foreground-primary);
}

.module-placeholder p {
    font-size: 14px;
    color: var(--foreground-secondary);
    margin: 0 0 8px 0;
}

.module-placeholder small {
    font-size: 12px;
    color: var(--foreground-tertiary);
}

.install-module-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: var(--foreground-secondary);
    border-color: var(--foreground-secondary);
}

.installing-btn {
    background: var(--accent-primary) !important;
    border-color: var(--accent-primary) !important;
}

.loading-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 6px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Module metadata styles */
.module-meta {
    display: flex;
    gap: 8px;
    margin: 6px 0;
    flex-wrap: wrap;
}

.module-version,
.module-size,
.module-category {
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 3px;
    background: var(--background-tertiary);
    color: var(--foreground-secondary);
    border: 1px solid var(--border-secondary);
}

.module-version {
    background: rgba(var(--accent-primary-rgb), 0.1);
    color: var(--accent-primary);
    border-color: var(--accent-primary);
}

.module-category {
    background: rgba(var(--info-rgb), 0.1);
    color: var(--info);
    border-color: var(--info);
}

.installed-btn {
    background: var(--success) !important;
    border-color: var(--success) !important;
    color: #ffffff !important;
}

/* Extensions Content */
.extensions-content {
    padding: 12px;
    height: 100%;
    overflow-y: auto;
}

.extension-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--border-secondary);
}

.extension-header h4 {
    font-size: 14px;
    font-weight: 600;
    color: var(--foreground-primary);
    margin: 0;
}

.extension-btn {
    background: none;
    border: none;
    color: var(--foreground-secondary);
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 14px;
}

.extension-btn:hover {
    background: var(--background-hover);
    color: var(--foreground-primary);
}

.extension-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.extension-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    background: var(--background-secondary);
}

.extension-item.enabled {
    border-color: var(--success);
    background: rgba(var(--success-rgb), 0.1);
}

.extension-item.disabled {
    opacity: 0.7;
}

.extension-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.extension-name {
    font-size: 13px;
    font-weight: 500;
    color: var(--foreground-primary);
}

.extension-version {
    font-size: 11px;
    color: var(--foreground-secondary);
}

.extension-toggle {
    background: none;
    border: none;
    font-size: 16px;
    cursor: pointer;
    padding: 4px;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.extension-toggle:hover {
    background: var(--background-hover);
}

/* Terminal Content */
.terminal-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: var(--background-tertiary);
}

.terminal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    border-bottom: 1px solid var(--border-primary);
    background: var(--background-secondary);
}

.terminal-title {
    font-size: 13px;
    font-weight: 500;
    color: var(--foreground-primary);
}

.terminal-actions {
    display: flex;
    gap: 4px;
}

.terminal-btn {
    background: none;
    border: none;
    color: var(--foreground-secondary);
    cursor: pointer;
    padding: 4px 6px;
    border-radius: 3px;
    font-size: 12px;
}

.terminal-btn:hover {
    background: var(--background-hover);
    color: var(--foreground-primary);
}

.terminal-output {
    flex: 1;
    padding: 12px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
    overflow-y: auto;
}

.terminal-line {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
}

.terminal-prompt {
    color: var(--success);
    font-weight: 500;
}

.terminal-cursor {
    color: var(--foreground-primary);
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .search-box {
        flex-direction: column;
    }
    
    .search-btn {
        border-top: 1px solid var(--border-primary);
    }
    
    .cloud-btn {
        padding: 10px;
        font-size: 12px;
    }
    
    .extension-item {
        padding: 10px;
    }
}