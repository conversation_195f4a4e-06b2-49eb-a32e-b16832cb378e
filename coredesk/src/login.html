<!DOCTYPE html>
<html lang="es" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; connect-src 'self' https://localhost:3010 https://*.coredeskpro.com https://api.coredeskpro.com https://portal.coredeskpro.com http://localhost:3010;">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=0.5, maximum-scale=2.0, user-scalable=yes">
    <meta name="format-detection" content="telephone=no">
    <meta name="theme-color" content="#1e1e1e">
    <title>CoreDesk Pro - Iniciar Sesión</title>
    
    <!-- Base CSS -->
    <link rel="stylesheet" href="css/base/reset.css">
    <link rel="stylesheet" href="css/base/variables.css">
    <link rel="stylesheet" href="css/base/scaling.css">
    <link rel="stylesheet" href="css/base/typography.css">
    
    <!-- Auth specific CSS -->
    <link rel="stylesheet" href="css/auth/login.css">
    
    <!-- Component CSS -->
    <link rel="stylesheet" href="css/components/modals.css">
    
    <!-- Theme CSS -->
    <link rel="stylesheet" href="css/themes/dark.css">
</head>
<body class="auth-body">
    <div class="auth-container">
        <!-- Left side - Login Form -->
        <div class="auth-left">
            <div class="auth-brand">
                <h1 class="brand-title">CoreDesk <span class="brand-pro">Pro</span></h1>
                <p class="brand-tagline">Tu ecosistema de agentes virtuales inteligentes.</p>
            </div>
            
            <div class="auth-form-container">
                <!-- Tab Navigation -->
                <div class="auth-tabs">
                    <button class="auth-tab active" data-tab="login">Iniciar Sesión</button>
                    <button class="auth-tab" data-tab="activate">Activar</button>
                    <button class="auth-tab" data-tab="register">Registro</button>
                </div>
                
                <!-- Login Tab Content -->
                <div class="auth-tab-content active" id="login-tab">
                    <form id="login-form" class="auth-form">
                        <div class="form-group">
                            <label for="login-email">Correo Electrónico</label>
                            <input type="email" id="login-email" name="email" class="form-input" required autocomplete="email">
                            <span class="form-error" id="login-email-error"></span>
                        </div>
                        
                        <div class="form-group">
                            <label for="login-password">Contraseña</label>
                            <input type="password" id="login-password" name="password" class="form-input" required autocomplete="current-password">
                            <span class="form-error" id="login-password-error"></span>
                        </div>
                        
                        <div class="form-options">
                            <label class="checkbox-label">
                                <input type="checkbox" id="remember-me" name="remember">
                                <span>Recordarme</span>
                            </label>
                            <a href="#" class="forgot-password">¿Olvidaste tu contraseña?</a>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-block" id="login-submit">
                            <span class="btn-text">Iniciar Sesión</span>
                            <span class="btn-loader" style="display: none;">
                                <span class="spinner"></span>
                            </span>
                        </button>
                        
                        <div class="form-divider">
                            <span>o</span>
                        </div>
                        
                        <button type="button" class="btn btn-secondary btn-block" id="login-with-license">
                            Iniciar con Licencia
                        </button>
                    </form>
                </div>
                
                <!-- Activate Tab Content -->
                <div class="auth-tab-content" id="activate-tab">
                    <form id="activate-form" class="auth-form">
                        <div class="form-header">
                            <h3>Activar Licencia</h3>
                            <p>Ingresa tu clave de licencia para activar CoreDesk Pro</p>
                        </div>
                        
                        <div class="form-group">
                            <label for="license-key">Clave de Licencia</label>
                            <input type="text" id="license-key" name="licenseKey" class="form-input license-input" 
                                   placeholder="XXXX-XXXX-XXXX-XXXX" maxlength="19" required>
                            <span class="form-error" id="license-key-error"></span>
                        </div>
                        
                        <div class="license-options">
                            <button type="button" class="btn-link" id="scan-qr">
                                <span class="icon">📷</span> Escanear código QR
                            </button>
                            <button type="button" class="btn-link" id="paste-license">
                                <span class="icon">📋</span> Pegar desde portapapeles
                            </button>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-block" id="activate-submit">
                            <span class="btn-text">Activar Licencia</span>
                            <span class="btn-loader" style="display: none;">
                                <span class="spinner"></span>
                            </span>
                        </button>
                        
                        <div class="form-footer">
                            <p>¿No tienes una licencia? <a href="#" id="request-trial-link">Solicita una prueba gratuita</a></p>
                        </div>
                    </form>
                </div>
                
                <!-- Register Tab Content -->
                <div class="auth-tab-content" id="register-tab">
                    <form id="register-form" class="auth-form">
                        <div class="form-header">
                            <h3>Crear Cuenta</h3>
                            <p>Regístrate para comenzar tu prueba gratuita</p>
                        </div>
                        
                        <!-- Step 1: Basic Info -->
                        <div class="register-step active" data-step="1">
                            <div class="form-group">
                                <label for="register-name">Nombre Completo</label>
                                <input type="text" id="register-name" name="name" class="form-input" required>
                                <span class="form-error" id="register-name-error"></span>
                            </div>
                            
                            <div class="form-group">
                                <label for="register-email">Correo Electrónico</label>
                                <input type="email" id="register-email" name="email" class="form-input" required>
                                <span class="form-error" id="register-email-error"></span>
                            </div>
                            
                            <div class="form-group">
                                <label for="register-company">Empresa (Opcional)</label>
                                <input type="text" id="register-company" name="company" class="form-input">
                            </div>
                            
                            <button type="button" class="btn btn-primary btn-block" id="register-next-1">
                                Siguiente
                            </button>
                        </div>
                        
                        <!-- Step 2: Password -->
                        <div class="register-step" data-step="2">
                            <div class="form-group">
                                <label for="register-password">Contraseña</label>
                                <input type="password" id="register-password" name="password" class="form-input" required>
                                <div class="password-strength">
                                    <div class="strength-bar">
                                        <div class="strength-fill" id="password-strength"></div>
                                    </div>
                                    <span class="strength-text" id="strength-text">Débil</span>
                                </div>
                                <span class="form-error" id="register-password-error"></span>
                            </div>
                            
                            <div class="form-group">
                                <label for="register-confirm">Confirmar Contraseña</label>
                                <input type="password" id="register-confirm" name="confirmPassword" class="form-input" required>
                                <span class="form-error" id="register-confirm-error"></span>
                            </div>
                            
                            <div class="form-actions">
                                <button type="button" class="btn btn-secondary" id="register-back-2">
                                    Atrás
                                </button>
                                <button type="button" class="btn btn-primary" id="register-next-2">
                                    Siguiente
                                </button>
                            </div>
                        </div>
                        
                        <!-- Step 3: Terms & Submit -->
                        <div class="register-step" data-step="3">
                            <div class="terms-container">
                                <h4>Términos y Condiciones</h4>
                                <div class="terms-content">
                                    <p>Al registrarte, aceptas nuestros términos de servicio y política de privacidad.</p>
                                    <ul>
                                        <li>Prueba gratuita de 30 días</li>
                                        <li>Sin tarjeta de crédito requerida</li>
                                        <li>Acceso completo a todas las funciones</li>
                                        <li>Soporte técnico incluido</li>
                                    </ul>
                                </div>
                                
                                <label class="checkbox-label">
                                    <input type="checkbox" id="accept-terms" name="acceptTerms" required>
                                    <span>Acepto los términos y condiciones</span>
                                </label>
                                <span class="form-error" id="terms-error"></span>
                            </div>
                            
                            <div class="form-actions">
                                <button type="button" class="btn btn-secondary" id="register-back-3">
                                    Atrás
                                </button>
                                <button type="submit" class="btn btn-primary" id="register-submit">
                                    <span class="btn-text">Crear Cuenta</span>
                                    <span class="btn-loader" style="display: none;">
                                        <span class="spinner"></span>
                                    </span>
                                </button>
                            </div>
                        </div>
                        
                        <!-- Progress Indicator -->
                        <div class="register-progress">
                            <div class="progress-step active" data-step="1">1</div>
                            <div class="progress-line"></div>
                            <div class="progress-step" data-step="2">2</div>
                            <div class="progress-line"></div>
                            <div class="progress-step" data-step="3">3</div>
                        </div>
                    </form>
                </div>
                
                <!-- Cancel Button (visible across all tabs) -->
                <div class="auth-cancel-container">
                    <button type="button" class="btn btn-cancel" id="cancel-login" title="Cerrar aplicación">
                        <span class="btn-icon">✕</span>
                        <span class="btn-text">Cancelar</span>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Right side - Feature Showcase -->
        <div class="auth-right">
            <div class="feature-showcase">
                <div class="showcase-header">
                    <h2>
                        <span class="main-title">Potencia tu Profesión</span><br>
                        <span class="sub-title">con <span class="highlight">Agentes IA<br>Especializados</span></span>
                    </h2>
                </div>
                <p class="showcase-description">
                    Nuestros agentes virtuales están entrenados por expertos para ofrecerte asistencia 
                    precisa y eficiente en tu campo. Aumenta tu productividad y toma decisiones más 
                    informadas.
                </p>
                
                <div class="agent-cards">
                    <div class="agent-card">
                        <div class="agent-icon legal">⚖️</div>
                        <h3>Agente Legal Pro</h3>
                        <p>Análisis de jurisprudencia, redacción de documentos y consultas legales con precisión y rapidez.</p>
                    </div>
                    
                    <div class="agent-card">
                        <div class="agent-icon financial">📊</div>
                        <h3>Agente Financiero Pro</h3>
                        <p>Análisis de estados financieros, proyecciones, y cumplimiento normativo contable.</p>
                    </div>
                    
                    <div class="agent-card">
                        <div class="agent-icon audit">🔍</div>
                        <h3>Agente Auditor Pro</h3>
                        <p>Evaluación de riesgos, procedimientos de auditoría y generación de informes detallados.</p>
                    </div>
                    
                    <div class="agent-card">
                        <div class="agent-icon protocol">📋</div>
                        <h3>Agente Protocolo Pro</h3>
                        <p>Gestión de protocolos empresariales, firma digital y control de versiones documentales.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Success Modal -->
    <div class="modal" id="success-modal" style="display: none;">
        <div class="modal-content modal-sm">
            <div class="modal-header">
                <h3>¡Éxito!</h3>
            </div>
            <div class="modal-body text-center">
                <div class="success-icon">✓</div>
                <p id="success-message">Operación completada exitosamente</p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" id="success-ok">Aceptar</button>
            </div>
        </div>
    </div>
    
    <!-- Error Modal -->
    <div class="modal" id="error-modal" style="display: none;">
        <div class="modal-content modal-sm">
            <div class="modal-header">
                <h3>Error</h3>
            </div>
            <div class="modal-body">
                <p id="error-message">Ha ocurrido un error</p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="error-ok">Cerrar</button>
            </div>
        </div>
    </div>
    
    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loading-overlay" style="display: none;">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <p>Cargando...</p>
        </div>
    </div>
    
    <!-- Scripts -->
    <!-- Scaling manager (load first for early scaling detection) -->
    <script src="js/utils/ScalingManager.js"></script>
    
    <!-- Window drag functionality -->
    <script src="js/utils/WindowDragManager.js"></script>
    
    <!-- External dependencies (local copy for Electron compatibility) -->
    <script src="js/vendor/axios.min.js"></script>
    
    <!-- Axios availability check -->
    <script>
        if (typeof window.axios === 'undefined') {
            console.error('[Critical] Axios failed to load locally, trying CDN fallback...');
            document.write('<script src="https://cdn.jsdelivr.net/npm/axios@1.6.0/dist/axios.min.js"><\/script>');
        } else {
            console.log('[Success] Axios loaded successfully from local file');
        }
    </script>
    
    <!-- Core utilities (load first) -->
    <script src="js/utils/ErrorHandler.js"></script>
    <script src="js/utils/InputValidator.js"></script>
    
    <!-- Core authentication configuration -->
    <script src="js/config/authConfig.js"></script>
    
    <!-- Security management -->
    <script src="js/security/SecurityManager.js"></script>
    
    <!-- API Client (must be loaded before AuthApiService) -->
    <script src="js/services/api/ApiClient.js"></script>
    
    <!-- Authentication services -->
    <script src="js/auth/SecureTokenManager.js"></script>
    <script src="js/auth/TokenManager.js"></script>
    <script src="js/services/api/AuthApiService.js"></script>
    
    <!-- Device fingerprinting -->
    <script src="js/license/DeviceFingerprint.js"></script>
    
    <!-- Authentication manager -->
    <script src="js/auth/UnifiedAuthManager.js"></script>
    
    <!-- Authentication test script (for debugging) -->
    <script src="js/test/testAuth.js"></script>
    
    <!-- Login controller -->
    <script src="js/auth/login.js"></script>
    
    <!-- Debug script to verify dependencies loaded -->
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🔍 Login Page Dependencies Check:');
            console.log('✅ axios:', !!window.axios);
            console.log('✅ apiClient:', !!window.apiClient);
            console.log('✅ CoreDeskAuth:', !!window.CoreDeskAuth);
            console.log('✅ tokenManager:', !!window.tokenManager);
            console.log('✅ authApiService:', !!window.authApiService);
            console.log('✅ unifiedAuthManager:', !!window.unifiedAuthManager);
            console.log('✅ securityManager:', !!window.securityManager);
            console.log('✅ errorHandler:', !!window.errorHandler);
            console.log('✅ inputValidator:', !!window.inputValidator);
            console.log('✅ deviceFingerprint:', !!window.deviceFingerprint);
            console.log('✅ authTester:', !!window.authTester);
            
            // Also show any initialization errors
            if (window.unifiedAuthManager) {
                console.log('🔧 Initializing auth dependencies...');
                window.unifiedAuthManager.initializeDependencies();
            }
            
            // Initialize AuthApiService dependencies
            if (window.authApiService) {
                console.log('🔧 Initializing AuthApiService dependencies...');
                window.authApiService.initializeDependencies();
                
                // Quick test after initialization - DISABLED to prevent automatic login
                // NOTE: This automatic test was causing tokens to be recreated after logout
                // Run manually if needed using: authTester.quickTest()
                /*
                setTimeout(() => {
                    console.log('🧪 Running quick auth test...');
                    if (window.authTester) {
                        window.authTester.quickTest().then(success => {
                            if (success) {
                                console.log('✅ Authentication system is working!');
                            } else {
                                console.log('❌ Authentication test failed - check console for details');
                            }
                        }).catch(err => {
                            console.log('❌ Authentication test error:', err);
                        });
                    }
                }, 2000);
                */
            }
        });
    </script>
</body>
</html>