{"name": "coredesk", "version": "0.0.2", "description": "CoreDesk Framework v0.0.2 - Professional legal and accounting desktop application", "main": "main.js", "scripts": {"start": "node scripts/start.js", "start-direct": "electron .", "setup-wsl2": "node scripts/setup-wsl2.js", "dev": "node scripts/start.js --dev", "dev-direct": "electron . --dev", "build": "electron-builder", "build:win": "electron-builder --win", "build:win:test": "node build-windows.js", "build:mac": "electron-builder --mac", "build:linux": "electron-builder --linux", "icons": "node generate-icons.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:security": "jest --testPathPattern=security", "validate": "node validate-clean-architecture.js"}, "keywords": ["legal", "accounting", "desktop", "electron", "framework", "modular", "professional"], "author": "CoreDesk Team", "license": "Proprietary", "homepage": "https://github.com/coredesk/coredesk-framework", "build": {"appId": "com.coredesk.framework", "productName": "CoreDesk Framework", "electronVersion": "31.7.7", "directories": {"output": "dist"}, "files": ["src/**/*", "!src/js/packages/**/*", "!src/js/modules/**/*", "!Screenshots/**/*", "!Screenshots", "main.js", "preload.js", "package.json"], "publish": [{"provider": "generic", "url": "https://coredeskpro.com/download"}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "icon": "src/assets/icons/app.ico", "publisherName": "CoreDesk Team"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "src/assets/icons/app.icns", "category": "public.app-category.productivity"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}], "icon": "src/assets/icons/app.png", "category": "Office"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}, "buildDependenciesFromSource": false, "nodeGypRebuild": false, "npmRebuild": false}, "dependencies": {"axios": "^1.7.2", "dotenv": "^16.4.5", "electron-updater": "^6.6.2", "firebase": "^10.12.5", "jsonwebtoken": "^9.0.2", "sqlite3": "^5.1.6"}, "devDependencies": {"@types/jest": "^29.5.12", "electron": "^31.7.7", "electron-builder": "^24.13.3", "electron-icon-builder": "^2.0.1", "icns": "^0.1.0", "jest": "^29.7.0", "png-to-ico": "^2.1.8", "png2icons": "^2.0.1", "sharp": "^0.34.2"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/js/**/*.js", "!src/js/test/**", "!src/js/**/node_modules/**"], "testMatch": ["**/__tests__/**/*.js", "**/?(*.)+(spec|test).js"], "setupFilesAfterEnv": ["<rootDir>/src/js/__tests__/setup.js"], "testPathIgnorePatterns": ["/node_modules/", "/src/js/__tests__/setup.js"]}}