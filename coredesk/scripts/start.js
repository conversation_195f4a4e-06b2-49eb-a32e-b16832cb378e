#!/usr/bin/env node
/**
 * CoreDesk Start Script
 * Handles both development and production startup modes
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Parse command line arguments
const isDev = process.argv.includes('--dev');
const isVerbose = process.argv.includes('--verbose') || process.argv.includes('-v');

console.log('🚀 CoreDesk Framework v0.0.2 - Starting...');
console.log(`📦 Mode: ${isDev ? 'Development' : 'Production'}`);
console.log(`📍 Directory: ${__dirname}`);

// Verify electron is installed
function checkElectron() {
    try {
        require.resolve('electron');
        return true;
    } catch (error) {
        console.error('❌ Electron not found. Please run: npm install');
        return false;
    }
}

// Verify main files exist
function checkMainFiles() {
    const requiredFiles = [
        path.join(__dirname, '..', 'main.js'),
        path.join(__dirname, '..', 'preload.js'),
        path.join(__dirname, '..', 'src', 'index.html')
    ];

    for (const file of requiredFiles) {
        if (!fs.existsSync(file)) {
            console.error(`❌ Required file not found: ${path.relative(__dirname, file)}`);
            return false;
        }
    }
    
    console.log('✅ All required files found');
    return true;
}

// Start Electron application
function startElectron() {
    console.log('🔧 Starting Electron application...');
    
    const electronPath = require('electron');
    const appPath = path.join(__dirname, '..');
    
    // Prepare electron arguments
    const electronArgs = [appPath];
    
    if (isDev) {
        electronArgs.push('--dev');
        console.log('🛠️ Development mode enabled');
    }
    
    if (isVerbose) {
        electronArgs.push('--verbose');
        console.log('📝 Verbose logging enabled');
    }
    
    // Environment variables for development
    const env = { ...process.env };
    if (isDev) {
        env.NODE_ENV = 'development';
        env.ELECTRON_IS_DEV = '1';
    }
    
    console.log(`⚡ Executing: electron ${electronArgs.join(' ')}`);
    
    // Spawn electron process
    const electronProcess = spawn(electronPath, electronArgs, {
        stdio: 'inherit',
        env: env,
        cwd: appPath
    });
    
    // Handle process events
    electronProcess.on('error', (error) => {
        console.error('❌ Failed to start Electron:', error.message);
        process.exit(1);
    });
    
    electronProcess.on('close', (code) => {
        if (code !== 0) {
            console.error(`❌ Electron process exited with code ${code}`);
            process.exit(code);
        } else {
            console.log('✅ Electron application closed normally');
        }
    });
    
    // Handle termination signals
    process.on('SIGINT', () => {
        console.log('\n🛑 Received SIGINT, shutting down...');
        electronProcess.kill('SIGINT');
    });
    
    process.on('SIGTERM', () => {
        console.log('\n🛑 Received SIGTERM, shutting down...');
        electronProcess.kill('SIGTERM');
    });
}

// Main execution
function main() {
    console.log('🔍 Performing pre-flight checks...');
    
    if (!checkElectron()) {
        process.exit(1);
    }
    
    if (!checkMainFiles()) {
        process.exit(1);
    }
    
    console.log('✅ Pre-flight checks completed');
    console.log('');
    
    startElectron();
}

// Execute if this script is run directly
if (require.main === module) {
    main();
}

module.exports = {
    startElectron,
    checkElectron,
    checkMainFiles
};
