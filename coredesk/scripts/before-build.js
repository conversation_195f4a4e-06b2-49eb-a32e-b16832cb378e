#!/usr/bin/env node

/**
 * Before Build Script for CoreDesk
 * Handles platform-specific dependencies before building
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔧 Running before-build script...');

// Get target platform from environment or command line
const targetPlatform = process.env.npm_config_target_platform || 
                      process.argv.find(arg => arg.includes('--win')) ? 'win32' : 
                      process.platform;

console.log(`📦 Target platform: ${targetPlatform}`);

if (targetPlatform === 'win32' || process.argv.includes('--win')) {
    console.log('🪟 Preparing Windows build...');
    
    try {
        // For Windows builds, we need to handle sqlite3 specially
        console.log('🗄️ Installing sqlite3 for Windows...');
        
        // Try to install sqlite3 with specific configuration for Windows
        execSync('npm install sqlite3 --build-from-source --target_platform=win32 --target_arch=x64', {
            stdio: 'inherit',
            cwd: __dirname + '/..'
        });
        
        console.log('✅ sqlite3 configured for Windows build');
        
    } catch (error) {
        console.log('⚠️ sqlite3 installation failed, trying alternative approach...');
        
        try {
            // Alternative: Use better-sqlite3 or remove sqlite3 temporarily
            console.log('🔄 Using alternative database approach for Windows build...');
            
            // Create a temporary package.json without sqlite3
            const packagePath = path.join(__dirname, '..', 'package.json');
            const packageContent = fs.readFileSync(packagePath, 'utf8');
            const packageJson = JSON.parse(packageContent);
            
            // Backup original dependencies
            const originalDeps = { ...packageJson.dependencies };
            
            // Remove sqlite3 temporarily
            delete packageJson.dependencies.sqlite3;
            
            // Write temporary package.json
            fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2));
            
            console.log('📝 Temporarily removed sqlite3 from dependencies for Windows build');
            console.log('⚠️ Note: Database functionality will be limited in Windows build');
            
            // Store original dependencies for restoration
            fs.writeFileSync(
                path.join(__dirname, '..', 'package.json.backup'),
                JSON.stringify({ dependencies: originalDeps }, null, 2)
            );
            
        } catch (altError) {
            console.error('❌ Failed to prepare Windows build:', altError.message);
            process.exit(1);
        }
    }
} else {
    console.log('🐧 Non-Windows build, no special preparation needed');
}

console.log('✅ Before-build script completed');
