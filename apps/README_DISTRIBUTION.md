# 📦 CoreDesk Distribution Files

Este directorio contiene los scripts y configuraciones para generar los archivos de distribución de CoreDesk.

## 🚀 Archivos Disponibles

Los archivos binarios de distribución no están incluidos en el repositorio Git debido a su gran tamaño (>100MB). Puedes encontrarlos en:

### 📥 Descargas Oficiales
- **GitHub Releases**: https://github.com/magnum15/CoreDesk/releases
- **Portal Oficial**: [Próximamente]

### 🏗️ Generar Localmente

Para generar los archivos de distribución en tu entorno local:

```bash
# Desde el directorio raíz del proyecto
cd coredesk
npm run build
npm run dist
```

Esto generará:
- `CoreDesk-0.0.2-linux.AppImage` (~124MB)
- `CoreDesk-0.0.2-mac.tar.gz` (~121MB) 
- `CoreDesk-0.0.2-win.exe` (~88MB)

### 📋 Scripts Incluidos

- `generate-latest-yml.js` - Genera archivo de actualización automática
- `copy-dist-files.js` - Copia archivos de distribución
- `package.json` - Configuración de empaquetado

### 🔧 Configuración

Los archivos de distribución se generan usando **Electron Builder** con las siguientes configuraciones:

- **Linux**: AppImage para máxima compatibilidad
- **macOS**: Archivo comprimido para distribución manual
- **Windows**: Ejecutable instalador con firma digital

### 📝 Notas

- Los archivos son excluidos automáticamente por `.gitignore`
- Use Git LFS si necesita versionar archivos binarios grandes
- Los releases oficiales están disponibles en GitHub Releases

---

**¿Necesitas ayuda?** Consulta la [documentación principal](../README.md) o abre un [issue](https://github.com/magnum15/CoreDesk/issues).
