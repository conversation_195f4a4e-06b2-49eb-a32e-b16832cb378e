#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * Copy and rename electron builder outputs to apps folder with proper naming
 * This script maps the electron builder output files to the naming convention 
 * expected by the generate-latest-yml.js script
 */

// Parse command line arguments
const args = process.argv.slice(2);
const forceOverwrite = args.includes('--force');

// Configuration
const config = {
  distPath: '../coredesk/dist',
  targetPath: '.',
  version: '0.0.2', // Will be auto-detected later
  mappings: {
    // Map from electron builder output to our expected naming
    'CoreDesk Framework Setup 0.0.2.exe': 'CoreDesk-{VERSION}-win.exe',
    'CoreDesk Framework-0.0.2.AppImage': 'CoreDesk-{VERSION}-linux.AppImage',
    // For Mac, we'll create a tar.gz from the .app folder
    'mac/CoreDesk Framework.app': 'CoreDesk-{VERSION}-mac.tar.gz'
  }
};

async function detectVersion() {
  const packagePath = '../coredesk/package.json';
  try {
    if (fs.existsSync(packagePath)) {
      const packageData = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
      return packageData.version;
    }
  } catch (error) {
    console.log(`⚠️  Could not read ${packagePath}: ${error.message}`);
  }
  return config.version;
}

function formatFileSize(bytes) {
  const mb = bytes / (1024 * 1024);
  return `~${Math.round(mb)} MB`;
}

async function copyAndRenameFiles() {
  console.log('📦 Copying electron builder outputs to apps folder...\n');
  
  const version = await detectVersion();
  console.log(`🔍 Using version: ${version}\n`);
  
  let copiedFiles = 0;
  
  for (const [sourceFile, targetPattern] of Object.entries(config.mappings)) {
    const sourcePath = path.join(config.distPath, sourceFile);
    const targetFile = targetPattern.replace('{VERSION}', version);
    const targetPath = path.join(config.targetPath, targetFile);
    
    console.log(`📁 Processing: ${sourceFile}`);
    console.log(`   📍 Source: ${sourcePath}`);
    console.log(`   🎯 Target: ${targetFile}`);
    
    try {
      // Check if target file already exists
      if (fs.existsSync(targetPath)) {
        const existingStats = fs.statSync(targetPath);
        console.log(`   ⚠️  Target file already exists (${formatFileSize(existingStats.size)})`);
        
        if (sourceFile.endsWith('.app')) {
          const reason = forceOverwrite ? 'Force flag used' : 'Replacing existing tar.gz';
          console.log(`   🔄 ${reason}...`);
          await createMacTarGz(sourcePath, targetPath, targetFile);
          copiedFiles++;
        } else {
          // For regular files, compare source and target timestamps/sizes
          if (fs.existsSync(sourcePath)) {
            const sourceStats = fs.statSync(sourcePath);
            
            if (forceOverwrite || sourceStats.size !== existingStats.size || sourceStats.mtime > existingStats.mtime) {
              const reason = forceOverwrite ? 'force flag used' : 'source is newer/different';
              console.log(`   🔄 ${reason.charAt(0).toUpperCase() + reason.slice(1)}, replacing...`);
              fs.copyFileSync(sourcePath, targetPath);
              console.log(`   ✅ Replaced successfully (${formatFileSize(sourceStats.size)})`);
              copiedFiles++;
            } else {
              console.log(`   ⏭️  Files are identical, skipping`);
            }
          } else {
            console.log(`   ❌ Source file not found`);
          }
        }
      } else {
        // Target doesn't exist, proceed normally
        if (sourceFile.endsWith('.app')) {
          // Handle macOS .app folder - create a tar.gz
          await createMacTarGz(sourcePath, targetPath, targetFile);
          copiedFiles++;
        } else {
          // Handle regular files
          if (fs.existsSync(sourcePath)) {
            const stats = fs.statSync(sourcePath);
            fs.copyFileSync(sourcePath, targetPath);
            console.log(`   ✅ Copied successfully (${formatFileSize(stats.size)})`);
            copiedFiles++;
          } else {
            console.log(`   ❌ Source file not found`);
          }
        }
      }
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
    }
    
    console.log('');
  }
  
  console.log(`✅ Successfully copied ${copiedFiles} files!`);
  console.log('🚀 You can now run: node generate-latest-yml.js --upload');
}

async function createMacTarGz(sourcePath, targetPath, targetFile) {
  if (!fs.existsSync(sourcePath)) {
    console.log(`   ❌ macOS .app folder not found`);
    return;
  }
  
  console.log(`   📦 Creating tar.gz from .app folder...`);
  
  const { spawn } = require('child_process');
  
  return new Promise((resolve, reject) => {
    // Create tar.gz of the .app folder
    const absoluteTargetPath = path.resolve(targetPath);
    const tarProcess = spawn('tar', ['-czf', absoluteTargetPath, path.basename(sourcePath)], {
      cwd: path.dirname(sourcePath),
      stdio: 'pipe'
    });
    
    let stderr = '';
    
    tarProcess.stderr.on('data', (data) => {
      stderr += data.toString();
    });
    
    tarProcess.on('close', (code) => {
      if (code === 0) {
        if (fs.existsSync(absoluteTargetPath)) {
          const stats = fs.statSync(absoluteTargetPath);
          console.log(`   ✅ Tar.gz created successfully (${formatFileSize(stats.size)})`);
        } else {
          console.log(`   ✅ Tar.gz creation completed`);
        }
        resolve();
      } else {
        reject(new Error(`Tar creation failed: ${stderr}`));
      }
    });
    
    tarProcess.on('error', (error) => {
      reject(error);
    });
  });
}

// Show help
function showHelp() {
  console.log(`
📦 CoreDesk Dist Files Copier

This script copies and renames electron builder outputs to the apps folder
with the proper naming convention for the generate-latest-yml.js script.

USAGE:
  node copy-dist-files.js [OPTIONS]

OPTIONS:
  --force    Force overwrite existing files (default: only if newer/different)
  --help     Show this help message

WHAT IT DOES:
  1. Detects version from CoreDesk package.json
  2. Copies and renames the following files:
     • CoreDesk Framework Setup X.X.X.exe → CoreDesk-X.X.X-win.exe
     • CoreDesk Framework-X.X.X.AppImage → CoreDesk-X.X.X-linux.AppImage
     • mac/CoreDesk Framework.app → CoreDesk-X.X.X-mac.tar.gz (compressed)

SOURCE DIRECTORY:
  ${path.resolve(config.distPath)}

TARGET DIRECTORY:
  ${path.resolve(config.targetPath)}

NEXT STEPS:
  After running this script, use:
  node generate-latest-yml.js --upload
`);
}

// Run the script
if (require.main === module) {
  if (args.includes('--help') || args.includes('-h')) {
    showHelp();
    process.exit(0);
  }
  
  copyAndRenameFiles().catch(error => {
    console.error('❌ Error:', error.message);
    process.exit(1);
  });
}

module.exports = { copyAndRenameFiles };