{"permissions": {"allow": ["Bash(npm init:*)", "Bash(npm install)", "Bash(npm install:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(chmod:*)", "Bash(node:*)", "<PERSON><PERSON>(mv:*)", "Bash(rm:*)", "Bash(ls:*)", "Bash(grep:*)", "Bash(find:*)", "Bash(rg:*)", "Bash(npm start:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "Bash(npm test)", "Bash(npm test:*)", "mcp__ide__executeCode", "<PERSON><PERSON>(curl:*)", "Bash(cp:*)", "Bash(zip:*)", "Bash(tar:*)", "<PERSON><PERSON>(touch:*)", "Bash(PORT=3000 node src/app.js)", "Bash(docker logs:*)", "<PERSON><PERSON>(docker-compose:*)", "<PERSON><PERSON>(docker exec:*)", "WebFetch(domain:api.coredeskpro.com)", "WebFetch(domain:coredeskpro.com)", "Bash(npm run build:*)", "Bash(timeout 5s npm start:*)", "<PERSON><PERSON>(sudo apt-get:*)", "<PERSON><PERSON>(dos2unix:*)", "<PERSON><PERSON>(sed:*)", "Bash(./upload-build.sh:*)", "Bash(./deploy-manually.sh:*)", "Bash(docker build:*)", "Bash(npm ls:*)", "Bash(npm outdated)", "Bash(timeout 10s npm run dev:*)", "Bash(timeout 20s npm run dev:*)", "Bash(docker tag:*)", "<PERSON><PERSON>(docker run:*)", "Bash(/dev/null)", "<PERSON><PERSON>(docker stop:*)", "<PERSON><PERSON>(docker rm:*)", "Bash(cd:*)", "Bash(cd:*)", "Bash(echo $DISPLAY)", "Bash(xhost:*)", "Bash(npm run dev:*)", "Bash(npm run electron-builder:*)", "<PERSON><PERSON>(docker save:*)", "Bash(gzip:*)", "Bash(npx asar list:*)", "Bash(npx asar extract:*)", "Bash(./create-module-packages.sh:*)", "Bash(./build-coredesk.sh:*)", "Bash(/home/<USER>/coredesk/build-coredesk.sh:*)", "Bash(./upload-to-production.sh:*)", "Bash(/home/<USER>/coredesk/upload-to-production.sh:*)", "Bash(ping:*)", "Bash(nmap:*)", "Bash(telnet:*)", "Bash(./upload-portal-only.sh:*)", "Bash(/home/<USER>/coredesk/upload-portal-only.sh:*)", "Bash(\"/home/<USER>/coredesk/portal/downloads/app/CoreDesk-0.0.2-win.exe\")", "Bash(\"/home/<USER>/coredesk/portal/downloads/app/CoreDesk-0.0.2-mac.zip\")", "Bash(\"/home/<USER>/coredesk/portal/downloads/app/CoreDesk-0.0.2-linux.AppImage\")", "Bash(docker system prune:*)", "Bash(./deploy-portal-fix.sh:*)", "<PERSON><PERSON>(scp:*)", "<PERSON><PERSON>(docker:*)", "Bash(NODE_ENV=production node debug-download-issue.js)", "Bash(NODE_ENV=production node test-latest-redirect.js)", "Bash(ssh:*)", "Bash(./scripts/rebuild-portal-production.sh:*)", "<PERSON><PERSON>(python3:*)", "Bash(git add:*)", "Bash(git commit:*)", "<PERSON><PERSON>(timeout:*)", "Bash(git push:*)", "Bash(./deploy-portal.sh:*)", "Bash(npm run lint)", "Bash(npm run:*)", "Bash(tree:*)", "Bash(sqlite3:*)", "<PERSON><PERSON>(cat:*)", "Bash(npx electron:*)"], "deny": []}}