#!/usr/bin/env node

/**
 * CoreDesk Module Packaging Script
 * Automatiza el empaquetado de módulos desde el repositorio hacia la distribución
 * 
 * Uso:
 *   node scripts/package-modules.js [opciones]
 *   npm run package-modules [opciones]
 * 
 * Opciones:
 *   --module <moduleId>    Empaquetar solo un módulo específico
 *   --force               Sobrescribir archivos existentes
 *   --validate            Solo validar sin empaquetar
 *   --clean               Limpiar directorio de destino antes de empaquetar
 *   --verbose             Mostrar información detallada
 *   --help                Mostrar ayuda
 */

const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');
const { spawn } = require('child_process');
const { promisify } = require('util');

// Configuración del script
const CONFIG = {
    sourceDir: path.join(__dirname, '../coredesk/src/js/packages'),
    targetDir: path.join(__dirname, '../portal/downloads/modules'),
    modulesDir: path.join(__dirname, '../coredesk/src/js/modules'),
    tempDir: path.join(__dirname, '../temp/packaging'),
    
    // Módulos disponibles y sus configuraciones
    modules: {
        lexflow: {
            name: 'LexFlow',
            version: '0.0.2',
            description: 'Gestión completa de casos legales y documentación jurídica',
            category: 'legal',
            icon: '⚖️',
            color: '#1e3a8a',
            requiredLicense: 'professional'
        },
        protocolx: {
            name: 'ProtocolX',
            version: '0.0.2',
            description: 'Administración de protocolo digital y gestión notarial',
            category: 'notarial',
            icon: '🔧',
            color: '#059669',
            requiredLicense: 'professional'
        },
        auditpro: {
            name: 'AuditPro',
            version: '0.0.2',
            description: 'Herramientas profesionales para auditores contables',
            category: 'audit',
            icon: '📊',
            color: '#7c2d12',
            requiredLicense: 'enterprise'
        },
        finsync: {
            name: 'FinSync',
            version: '0.0.2',
            description: 'Módulo de contabilidad, finanzas y análisis de rentabilidad',
            category: 'finance',
            icon: '💰',
            color: '#1e40af',
            requiredLicense: 'professional'
        }
    },
    
    // Archivos requeridos para cada módulo
    requiredFiles: {
        packageFile: '{ModuleName}Package.js',
        moduleFile: '{ModuleName}Module.js',
        styleFile: '{moduleId}.css',
        installFile: 'install.js',
        packageJson: 'package.json'
    }
};

// Variables globales para opciones de línea de comandos
let options = {
    module: null,
    force: false,
    validate: false,
    clean: false,
    verbose: false,
    help: false
};

/**
 * Clase principal para el empaquetado de módulos
 */
class ModulePackager {
    constructor() {
        this.errors = [];
        this.warnings = [];
        this.packaged = [];
    }

    /**
     * Ejecutar el proceso completo de empaquetado
     */
    async run() {
        try {
            this.log('🚀 Iniciando empaquetado de módulos CoreDesk...', 'info');
            
            // Validar entorno
            await this.validateEnvironment();
            
            // Limpiar si se solicita
            if (options.clean) {
                await this.cleanTargetDirectory();
            }
            
            // Crear directorios necesarios
            await this.createDirectories();
            
            // Determinar módulos a procesar
            const modulesToProcess = options.module 
                ? [options.module] 
                : Object.keys(CONFIG.modules);
            
            // Validar módulos especificados
            for (const moduleId of modulesToProcess) {
                if (!CONFIG.modules[moduleId]) {
                    throw new Error(`Módulo '${moduleId}' no está configurado`);
                }
            }
            
            // Procesar cada módulo
            for (const moduleId of modulesToProcess) {
                await this.processModule(moduleId);
            }
            
            // Solo validar si se especifica la opción
            if (options.validate) {
                await this.validatePackages();
                this.log('✅ Validación completada', 'success');
                return;
            }
            
            // Mostrar resumen
            this.showSummary();
            
        } catch (error) {
            this.log(`❌ Error fatal: ${error.message}`, 'error');
            process.exit(1);
        }
    }

    /**
     * Validar el entorno antes de comenzar
     */
    async validateEnvironment() {
        this.log('🔍 Validando entorno...', 'info');
        
        // Verificar que existan los directorios fuente
        const requiredDirs = [CONFIG.sourceDir, CONFIG.modulesDir];
        
        for (const dir of requiredDirs) {
            try {
                await fs.access(dir);
            } catch (error) {
                throw new Error(`Directorio requerido no encontrado: ${dir}`);
            }
        }
        
        // Verificar que tar esté disponible
        try {
            await this.executeCommand('tar', ['--version']);
        } catch (error) {
            throw new Error('El comando tar no está disponible. Es requerido para crear archivos .tar.gz');
        }
        
        this.log('✅ Entorno validado correctamente', 'success');
    }

    /**
     * Limpiar directorio de destino
     */
    async cleanTargetDirectory() {
        this.log('🧹 Limpiando directorio de destino...', 'info');
        
        try {
            await fs.rm(CONFIG.targetDir, { recursive: true, force: true });
            this.log('✅ Directorio limpiado', 'success');
        } catch (error) {
            this.log(`⚠️ Error al limpiar directorio: ${error.message}`, 'warning');
        }
    }

    /**
     * Crear directorios necesarios
     */
    async createDirectories() {
        const dirs = [CONFIG.targetDir, CONFIG.tempDir];
        
        for (const dir of dirs) {
            await fs.mkdir(dir, { recursive: true });
        }
    }

    /**
     * Procesar un módulo específico
     */
    async processModule(moduleId) {
        this.log(`📦 Procesando módulo: ${moduleId}`, 'info');
        
        try {
            const moduleConfig = CONFIG.modules[moduleId];
            const moduleName = moduleConfig.name;
            
            // Crear directorio temporal para este módulo
            const tempModuleDir = path.join(CONFIG.tempDir, moduleId);
            await fs.mkdir(tempModuleDir, { recursive: true });
            
            // Recopilar archivos del módulo
            const moduleFiles = await this.collectModuleFiles(moduleId, moduleName);
            
            // Copiar archivos al directorio temporal
            await this.copyFilesToTemp(moduleFiles, tempModuleDir);
            
            // Generar manifest.json completo
            await this.generateManifest(moduleId, moduleConfig, tempModuleDir);
            
            // Crear archivo tar.gz
            const tarPath = await this.createTarGz(moduleId, moduleConfig.version, tempModuleDir);
            
            // Calcular checksum
            const checksum = await this.calculateChecksum(tarPath);
            
            // Crear directorio de destino
            const targetModuleDir = path.join(CONFIG.targetDir, moduleId, moduleConfig.version);
            await fs.mkdir(targetModuleDir, { recursive: true });
            
            // Mover archivo tar.gz al destino
            const finalTarPath = path.join(targetModuleDir, `${moduleId}-${moduleConfig.version}.tar.gz`);
            await fs.rename(tarPath, finalTarPath);
            
            // Actualizar manifest con checksum y moverlo al destino
            await this.updateManifestWithChecksum(tempModuleDir, targetModuleDir, checksum, finalTarPath);
            
            // Limpiar directorio temporal
            await fs.rm(tempModuleDir, { recursive: true, force: true });
            
            this.packaged.push({
                moduleId,
                version: moduleConfig.version,
                path: finalTarPath,
                checksum
            });
            
            this.log(`✅ Módulo ${moduleId} empaquetado correctamente`, 'success');
            
        } catch (error) {
            this.errors.push(`Error en módulo ${moduleId}: ${error.message}`);
            this.log(`❌ Error procesando ${moduleId}: ${error.message}`, 'error');
        }
    }

    /**
     * Recopilar archivos del módulo desde diferentes ubicaciones
     */
    async collectModuleFiles(moduleId, moduleName) {
        const files = {};
        
        // Directorio de packages
        const packageDir = path.join(CONFIG.sourceDir, moduleId);
        
        // Directorio de modules (para archivos Module.js)
        const moduleDir = path.join(CONFIG.modulesDir, moduleId);
        
        // Buscar archivo Package.js
        const packageFile = path.join(packageDir, `${moduleName}Package.js`);
        try {
            await fs.access(packageFile);
            files.packageFile = packageFile;
        } catch (error) {
            this.warnings.push(`Archivo Package.js no encontrado para ${moduleId}: ${packageFile}`);
        }
        
        // Buscar archivo Module.js
        const moduleFile = path.join(moduleDir, `${moduleName}Module.js`);
        try {
            await fs.access(moduleFile);
            files.moduleFile = moduleFile;
        } catch (error) {
            this.warnings.push(`Archivo Module.js no encontrado para ${moduleId}: ${moduleFile}`);
        }
        
        // Buscar otros archivos requeridos en el directorio de packages
        const otherFiles = ['install.js', 'package.json', `${moduleId}.css`];
        
        for (const fileName of otherFiles) {
            const filePath = path.join(packageDir, fileName);
            try {
                await fs.access(filePath);
                files[fileName] = filePath;
            } catch (error) {
                this.warnings.push(`Archivo ${fileName} no encontrado para ${moduleId}: ${filePath}`);
            }
        }
        
        return files;
    }

    /**
     * Copiar archivos al directorio temporal
     */
    async copyFilesToTemp(moduleFiles, tempDir) {
        for (const [key, sourcePath] of Object.entries(moduleFiles)) {
            if (sourcePath) {
                const fileName = path.basename(sourcePath);
                const destPath = path.join(tempDir, fileName);
                await fs.copyFile(sourcePath, destPath);

                if (options.verbose) {
                    this.log(`  📄 Copiado: ${fileName}`, 'info');
                }
            }
        }
    }

    /**
     * Generar manifest.json completo
     */
    async generateManifest(moduleId, moduleConfig, tempDir) {
        const manifest = {
            id: moduleId,
            name: moduleConfig.name,
            version: moduleConfig.version,
            description: moduleConfig.description,
            author: 'CoreDesk Team',
            license: 'Proprietary',
            category: moduleConfig.category,
            tags: this.generateTags(moduleConfig.category),
            main: `${moduleConfig.name}Module.js`,
            package: `${moduleConfig.name}Package.js`,
            style: `${moduleId}.css`,
            install: 'install.js',
            dependencies: {
                coredesk: '>=0.0.2'
            },
            requiredPermissions: [
                'storage.read',
                'storage.write',
                'ui.tabs',
                'ui.panels'
            ],
            requiredLicense: moduleConfig.requiredLicense,
            engines: {
                coredesk: '>=0.0.2'
            },
            homepage: `https://coredeskpro.com/modules/${moduleId}`,
            repository: {
                type: 'git',
                url: `https://github.com/coredesk/modules/${moduleId}`
            },
            packageFile: `${moduleId}-${moduleConfig.version}.tar.gz`,
            downloadCount: 0,
            lastUpdated: new Date().toISOString(),
            compatibility: {
                coredesk: '>=0.0.2',
                platforms: ['windows', 'macos', 'linux']
            },
            coredesk: {
                moduleType: 'ui',
                activationMethod: 'lazy',
                supportedThemes: ['dark', 'light'],
                minScreenWidth: 1024,
                moduleIcon: moduleConfig.icon,
                moduleColor: moduleConfig.color
            },
            files: [
                `${moduleConfig.name}Module.js`,
                `${moduleConfig.name}Package.js`,
                `${moduleId}.css`,
                'install.js',
                'package.json'
            ]
        };

        const manifestPath = path.join(tempDir, 'manifest.json');
        await fs.writeFile(manifestPath, JSON.stringify(manifest, null, 2));

        if (options.verbose) {
            this.log(`  📋 Manifest generado: ${manifestPath}`, 'info');
        }

        return manifest;
    }

    /**
     * Generar tags basados en la categoría
     */
    generateTags(category) {
        const tagMap = {
            legal: ['legal', 'case-management', 'documents', 'law'],
            notarial: ['notarial', 'protocol', 'documents', 'digital-signature'],
            audit: ['audit', 'accounting', 'reports', 'compliance'],
            finance: ['finance', 'accounting', 'analysis', 'cash-flow']
        };

        return tagMap[category] || ['general'];
    }

    /**
     * Crear archivo tar.gz
     */
    async createTarGz(moduleId, version, tempDir) {
        const tarFileName = `${moduleId}-${version}.tar.gz`;
        const tarPath = path.join(CONFIG.tempDir, tarFileName);

        // Crear tar.gz con todos los archivos del directorio temporal
        await this.executeCommand('tar', [
            '-czf',
            tarPath,
            '-C',
            tempDir,
            '.'
        ]);

        if (options.verbose) {
            this.log(`  📦 Archivo tar.gz creado: ${tarFileName}`, 'info');
        }

        return tarPath;
    }

    /**
     * Calcular checksum SHA256
     */
    async calculateChecksum(filePath) {
        const fileBuffer = await fs.readFile(filePath);
        const hashSum = crypto.createHash('sha256');
        hashSum.update(fileBuffer);
        const checksum = hashSum.digest('hex');

        if (options.verbose) {
            this.log(`  🔐 Checksum calculado: ${checksum}`, 'info');
        }

        return `sha256:${checksum}`;
    }

    /**
     * Actualizar manifest con checksum y moverlo al destino
     */
    async updateManifestWithChecksum(tempDir, targetDir, checksum, tarPath) {
        const manifestPath = path.join(tempDir, 'manifest.json');
        const manifest = JSON.parse(await fs.readFile(manifestPath, 'utf8'));

        // Obtener tamaño del archivo
        const stats = await fs.stat(tarPath);

        // Actualizar manifest con información final
        manifest.checksum = checksum;
        manifest.size = stats.size;

        // Guardar manifest actualizado en el destino
        const finalManifestPath = path.join(targetDir, 'manifest.json');
        await fs.writeFile(finalManifestPath, JSON.stringify(manifest, null, 2));

        if (options.verbose) {
            this.log(`  📋 Manifest final guardado: ${finalManifestPath}`, 'info');
        }
    }

    /**
     * Ejecutar comando del sistema
     */
    async executeCommand(command, args) {
        return new Promise((resolve, reject) => {
            const process = spawn(command, args, { stdio: 'pipe' });

            let stdout = '';
            let stderr = '';

            process.stdout.on('data', (data) => {
                stdout += data.toString();
            });

            process.stderr.on('data', (data) => {
                stderr += data.toString();
            });

            process.on('close', (code) => {
                if (code === 0) {
                    resolve(stdout);
                } else {
                    reject(new Error(`Comando falló: ${command} ${args.join(' ')}\n${stderr}`));
                }
            });

            process.on('error', (error) => {
                reject(error);
            });
        });
    }

    /**
     * Validar paquetes generados
     */
    async validatePackages() {
        this.log('🔍 Validando paquetes generados...', 'info');

        for (const pkg of this.packaged) {
            try {
                // Verificar que el archivo existe
                await fs.access(pkg.path);

                // Verificar checksum
                const calculatedChecksum = await this.calculateChecksum(pkg.path);
                if (calculatedChecksum !== pkg.checksum) {
                    this.errors.push(`Checksum inválido para ${pkg.moduleId}: esperado ${pkg.checksum}, obtenido ${calculatedChecksum}`);
                }

                // Verificar que se puede extraer
                const tempValidationDir = path.join(CONFIG.tempDir, `validate_${pkg.moduleId}`);
                await fs.mkdir(tempValidationDir, { recursive: true });

                await this.executeCommand('tar', [
                    '-xzf',
                    pkg.path,
                    '-C',
                    tempValidationDir
                ]);

                // Verificar archivos requeridos
                const requiredFiles = ['manifest.json'];
                for (const file of requiredFiles) {
                    const filePath = path.join(tempValidationDir, file);
                    try {
                        await fs.access(filePath);
                    } catch (error) {
                        this.errors.push(`Archivo requerido faltante en ${pkg.moduleId}: ${file}`);
                    }
                }

                // Limpiar directorio de validación
                await fs.rm(tempValidationDir, { recursive: true, force: true });

                this.log(`✅ Paquete ${pkg.moduleId} validado correctamente`, 'success');

            } catch (error) {
                this.errors.push(`Error validando ${pkg.moduleId}: ${error.message}`);
            }
        }
    }

    /**
     * Mostrar resumen de la operación
     */
    showSummary() {
        console.log('\n' + '='.repeat(60));
        console.log('📊 RESUMEN DE EMPAQUETADO');
        console.log('='.repeat(60));

        console.log(`\n✅ Módulos empaquetados exitosamente: ${this.packaged.length}`);
        for (const pkg of this.packaged) {
            console.log(`   • ${pkg.moduleId} v${pkg.version}`);
            console.log(`     📦 ${path.basename(pkg.path)}`);
            console.log(`     🔐 ${pkg.checksum.substring(0, 16)}...`);
        }

        if (this.warnings.length > 0) {
            console.log(`\n⚠️  Advertencias: ${this.warnings.length}`);
            for (const warning of this.warnings) {
                console.log(`   • ${warning}`);
            }
        }

        if (this.errors.length > 0) {
            console.log(`\n❌ Errores: ${this.errors.length}`);
            for (const error of this.errors) {
                console.log(`   • ${error}`);
            }
        }

        console.log(`\n📁 Archivos generados en: ${CONFIG.targetDir}`);
        console.log('='.repeat(60));

        if (this.errors.length > 0) {
            process.exit(1);
        }
    }

    /**
     * Logging con colores y timestamps
     */
    log(message, level = 'info') {
        const timestamp = new Date().toISOString().substring(11, 19);
        const colors = {
            info: '\x1b[36m',    // Cyan
            success: '\x1b[32m', // Green
            warning: '\x1b[33m', // Yellow
            error: '\x1b[31m',   // Red
            reset: '\x1b[0m'     // Reset
        };

        const color = colors[level] || colors.info;
        console.log(`${color}[${timestamp}] ${message}${colors.reset}`);
    }
}

/**
 * Parsear argumentos de línea de comandos
 */
function parseArguments() {
    const args = process.argv.slice(2);

    for (let i = 0; i < args.length; i++) {
        const arg = args[i];

        switch (arg) {
            case '--module':
                options.module = args[++i];
                break;
            case '--force':
                options.force = true;
                break;
            case '--validate':
                options.validate = true;
                break;
            case '--clean':
                options.clean = true;
                break;
            case '--verbose':
                options.verbose = true;
                break;
            case '--help':
                options.help = true;
                break;
            default:
                if (arg.startsWith('--')) {
                    console.error(`❌ Opción desconocida: ${arg}`);
                    process.exit(1);
                }
        }
    }
}

/**
 * Mostrar ayuda
 */
function showHelp() {
    console.log(`
📦 CoreDesk Module Packaging Script

DESCRIPCIÓN:
  Automatiza el empaquetado de módulos desde el repositorio hacia la distribución.
  Genera archivos .tar.gz con manifiestos completos y checksums validados.

USO:
  node scripts/package-modules.js [opciones]
  npm run package-modules [opciones]

OPCIONES:
  --module <moduleId>    Empaquetar solo un módulo específico
                         Módulos disponibles: ${Object.keys(CONFIG.modules).join(', ')}

  --force               Sobrescribir archivos existentes sin preguntar
  --validate            Solo validar paquetes existentes sin empaquetar
  --clean               Limpiar directorio de destino antes de empaquetar
  --verbose             Mostrar información detallada del proceso
  --help                Mostrar esta ayuda

EJEMPLOS:
  # Empaquetar todos los módulos
  npm run package-modules

  # Empaquetar solo LexFlow
  npm run package-modules -- --module lexflow

  # Limpiar y empaquetar con información detallada
  npm run package-modules -- --clean --verbose

  # Solo validar paquetes existentes
  npm run package-modules -- --validate

DIRECTORIOS:
  Fuente:  ${CONFIG.sourceDir}
  Destino: ${CONFIG.targetDir}
  Temp:    ${CONFIG.tempDir}
`);
}

/**
 * Función principal
 */
async function main() {
    parseArguments();

    if (options.help) {
        showHelp();
        return;
    }

    const packager = new ModulePackager();
    await packager.run();
}

// Ejecutar si se llama directamente
if (require.main === module) {
    main().catch(error => {
        console.error(`❌ Error fatal: ${error.message}`);
        process.exit(1);
    });
}

module.exports = { ModulePackager, CONFIG };
