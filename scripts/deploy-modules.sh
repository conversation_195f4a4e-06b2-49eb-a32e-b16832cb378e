#!/bin/bash

# CoreDesk Module Deployment Script
# Despliega módulos empaquetados al servidor de producción

set -e  # Salir en caso de error

# Configuración
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
LOCAL_MODULES_DIR="$PROJECT_ROOT/portal/downloads/modules"
REMOTE_HOST="coredesk-prod"
REMOTE_PATH="/opt/coredesk/data/downloads/modules"
BACKUP_DIR="/opt/coredesk/backups/modules/$(date +%Y%m%d_%H%M%S)"

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Funciones de logging
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Función para mostrar ayuda
show_help() {
    cat << EOF
📦 CoreDesk Module Deployment Script

DESCRIPCIÓN:
  Despliega módulos empaquetados al servidor de producción con backup automático.

USO:
  ./scripts/deploy-modules.sh [opciones]

OPCIONES:
  --module <moduleId>    Desplegar solo un módulo específico
  --dry-run             Mostrar qué se haría sin ejecutar
  --no-backup           No crear backup antes del despliegue
  --force               Forzar despliegue sin confirmación
  --help                Mostrar esta ayuda

EJEMPLOS:
  # Desplegar todos los módulos
  ./scripts/deploy-modules.sh
  
  # Desplegar solo LexFlow
  ./scripts/deploy-modules.sh --module lexflow
  
  # Dry run para ver qué se haría
  ./scripts/deploy-modules.sh --dry-run

REQUISITOS:
  - Acceso SSH configurado para coredesk-prod
  - Módulos empaquetados en portal/downloads/modules/
EOF
}

# Parsear argumentos
MODULE=""
DRY_RUN=false
NO_BACKUP=false
FORCE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --module)
            MODULE="$2"
            shift 2
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --no-backup)
            NO_BACKUP=true
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            log_error "Opción desconocida: $1"
            show_help
            exit 1
            ;;
    esac
done

# Función para verificar prerrequisitos
check_prerequisites() {
    log_info "Verificando prerrequisitos..."
    
    # Verificar conexión SSH
    if ! ssh -o ConnectTimeout=5 "$REMOTE_HOST" "echo 'SSH connection OK'" >/dev/null 2>&1; then
        log_error "No se puede conectar al servidor $REMOTE_HOST"
        log_error "Verifica tu configuración SSH"
        exit 1
    fi
    
    # Verificar que existan módulos locales
    if [[ ! -d "$LOCAL_MODULES_DIR" ]]; then
        log_error "Directorio de módulos no encontrado: $LOCAL_MODULES_DIR"
        log_error "Ejecuta primero: npm run package-modules"
        exit 1
    fi
    
    # Verificar que haya módulos para desplegar
    local module_count
    if [[ -n "$MODULE" ]]; then
        if [[ ! -d "$LOCAL_MODULES_DIR/$MODULE" ]]; then
            log_error "Módulo $MODULE no encontrado en $LOCAL_MODULES_DIR"
            exit 1
        fi
        module_count=1
    else
        module_count=$(find "$LOCAL_MODULES_DIR" -mindepth 1 -maxdepth 1 -type d | wc -l)
        if [[ $module_count -eq 0 ]]; then
            log_error "No se encontraron módulos para desplegar"
            log_error "Ejecuta primero: npm run package-modules"
            exit 1
        fi
    fi
    
    log_success "Prerrequisitos verificados ($module_count módulos para desplegar)"
}

# Función para crear backup en el servidor
create_backup() {
    if [[ "$NO_BACKUP" == true ]]; then
        log_info "Saltando backup (--no-backup especificado)"
        return
    fi
    
    log_info "Creando backup en el servidor..."
    
    if [[ "$DRY_RUN" == true ]]; then
        log_info "[DRY RUN] ssh $REMOTE_HOST \"mkdir -p $BACKUP_DIR && cp -r $REMOTE_PATH/* $BACKUP_DIR/ 2>/dev/null || true\""
        return
    fi
    
    ssh "$REMOTE_HOST" "mkdir -p $BACKUP_DIR && cp -r $REMOTE_PATH/* $BACKUP_DIR/ 2>/dev/null || true"
    
    log_success "Backup creado en: $BACKUP_DIR"
}

# Función para desplegar módulos
deploy_modules() {
    log_info "Iniciando despliegue de módulos..."
    
    if [[ -n "$MODULE" ]]; then
        deploy_single_module "$MODULE"
    else
        # Desplegar todos los módulos
        for module_dir in "$LOCAL_MODULES_DIR"/*; do
            if [[ -d "$module_dir" ]]; then
                local module_name=$(basename "$module_dir")
                deploy_single_module "$module_name"
            fi
        done
    fi
}

# Función para desplegar un módulo específico
deploy_single_module() {
    local module_id="$1"
    local local_module_dir="$LOCAL_MODULES_DIR/$module_id"
    local remote_module_dir="$REMOTE_PATH/$module_id"
    
    log_info "Desplegando módulo: $module_id"
    
    if [[ ! -d "$local_module_dir" ]]; then
        log_error "Módulo $module_id no encontrado localmente"
        return 1
    fi
    
    # Verificar que el módulo tenga archivos
    local file_count=$(find "$local_module_dir" -type f | wc -l)
    if [[ $file_count -eq 0 ]]; then
        log_warning "Módulo $module_id no tiene archivos, saltando"
        return 0
    fi
    
    if [[ "$DRY_RUN" == true ]]; then
        log_info "[DRY RUN] rsync -avz --delete $local_module_dir/ $REMOTE_HOST:$remote_module_dir/"
        return 0
    fi
    
    # Crear directorio remoto si no existe
    ssh "$REMOTE_HOST" "mkdir -p $remote_module_dir"
    
    # Sincronizar archivos
    if rsync -avz --delete "$local_module_dir/" "$REMOTE_HOST:$remote_module_dir/"; then
        log_success "Módulo $module_id desplegado correctamente"
        
        # Verificar archivos desplegados
        local remote_files
        remote_files=$(ssh "$REMOTE_HOST" "find $remote_module_dir -type f | wc -l")
        log_info "  📁 Archivos desplegados: $remote_files"
        
        # Mostrar checksums si es verbose
        if [[ -f "$local_module_dir"/*/manifest.json ]]; then
            local checksum
            checksum=$(grep -o '"checksum": "[^"]*"' "$local_module_dir"/*/manifest.json | cut -d'"' -f4 | head -1)
            if [[ -n "$checksum" ]]; then
                log_info "  🔐 Checksum: ${checksum:0:16}..."
            fi
        fi
    else
        log_error "Error desplegando módulo $module_id"
        return 1
    fi
}

# Función para verificar despliegue
verify_deployment() {
    log_info "Verificando despliegue..."
    
    if [[ "$DRY_RUN" == true ]]; then
        log_info "[DRY RUN] Verificación saltada en modo dry-run"
        return
    fi
    
    # Verificar que los módulos estén en el servidor
    local modules_to_check
    if [[ -n "$MODULE" ]]; then
        modules_to_check="$MODULE"
    else
        modules_to_check=$(find "$LOCAL_MODULES_DIR" -mindepth 1 -maxdepth 1 -type d -exec basename {} \;)
    fi
    
    local success_count=0
    local total_count=0
    
    for module_id in $modules_to_check; do
        ((total_count++))
        
        local remote_module_dir="$REMOTE_PATH/$module_id"
        if ssh "$REMOTE_HOST" "test -d $remote_module_dir && find $remote_module_dir -name '*.tar.gz' | head -1" >/dev/null 2>&1; then
            log_success "✅ $module_id verificado correctamente"
            ((success_count++))
        else
            log_error "❌ $module_id no se encontró o está incompleto"
        fi
    done
    
    log_info "Verificación completada: $success_count/$total_count módulos OK"
    
    if [[ $success_count -ne $total_count ]]; then
        log_error "Algunos módulos no se desplegaron correctamente"
        exit 1
    fi
}

# Función para mostrar resumen
show_summary() {
    echo ""
    echo "=============================================="
    echo "📊 RESUMEN DE DESPLIEGUE"
    echo "=============================================="
    echo "🎯 Destino: $REMOTE_HOST:$REMOTE_PATH"
    
    if [[ -n "$MODULE" ]]; then
        echo "📦 Módulo: $MODULE"
    else
        echo "📦 Módulos: Todos los disponibles"
    fi
    
    if [[ "$NO_BACKUP" != true ]]; then
        echo "💾 Backup: $BACKUP_DIR"
    fi
    
    if [[ "$DRY_RUN" == true ]]; then
        echo "🔍 Modo: DRY RUN (no se realizaron cambios)"
    else
        echo "✅ Estado: Despliegue completado"
    fi
    
    echo "=============================================="
}

# Función principal
main() {
    echo "🚀 CoreDesk Module Deployment Script"
    echo "======================================"
    
    # Verificar prerrequisitos
    check_prerequisites
    
    # Mostrar confirmación si no es forzado
    if [[ "$FORCE" != true && "$DRY_RUN" != true ]]; then
        echo ""
        log_warning "¿Continuar con el despliegue? (y/N)"
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            log_info "Despliegue cancelado por el usuario"
            exit 0
        fi
    fi
    
    # Crear backup
    create_backup
    
    # Desplegar módulos
    deploy_modules
    
    # Verificar despliegue
    verify_deployment
    
    # Mostrar resumen
    show_summary
    
    log_success "🎉 Despliegue completado exitosamente"
}

# Ejecutar función principal
main "$@"
