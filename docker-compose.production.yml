version: '3.8'

services:
  # Portal Web CoreDesk
  portal:
    image: portal:0.0.2
    container_name: coredesk-portal
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - HOST=0.0.0.0
      - API_BASE_URL=http://api:3010/v1
      - ADMIN_PANEL_URL=http://admin:3020
    volumes:
      - ./portal/logs:/app/logs
      - ./portal/downloads:/app/downloads
      - ./portal/uploads:/app/uploads
    networks:
      - coredesk-network
    depends_on:
      - api
    healthcheck:
      test: ["CMD", "node", "healthcheck.js"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # API Backend CoreDesk
  api:
    image: api:latest
    container_name: coredesk-api
    restart: unless-stopped
    ports:
      - "3010:3010"
    environment:
      - NODE_ENV=production
      - PORT=3010
      - MONGODB_URI=mongodb://mongo:27017/coredesk
    volumes:
      - ./api/logs:/app/logs
      - ./api/uploads:/app/uploads
    networks:
      - coredesk-network
    depends_on:
      - mongo

  # Panel de Administración
  admin:
    image: admin:latest
    container_name: coredesk-admin
    restart: unless-stopped
    ports:
      - "3020:3020"
    environment:
      - NODE_ENV=production
      - PORT=3020
      - API_BASE_URL=http://api:3010/v1
    volumes:
      - ./admin/logs:/app/logs
    networks:
      - coredesk-network
    depends_on:
      - api

  # Base de Datos MongoDB
  mongo:
    image: mongo:6.0
    container_name: coredesk-mongo
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=coredesk
      - MONGO_INITDB_ROOT_PASSWORD=CoreDeskMongo2025!
      - MONGO_INITDB_DATABASE=coredesk
    volumes:
      - mongo_data:/data/db
      - ./backups:/backups
    networks:
      - coredesk-network

networks:
  coredesk-network:
    driver: bridge
    name: coredesk-network

volumes:
  mongo_data:
    driver: local
