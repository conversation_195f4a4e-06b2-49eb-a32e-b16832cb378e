<!-- Module Header -->
<section class="hero module-hero">
    <div class="hero-content">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <div class="module-header-info">
                    <!-- Module Icon -->
                    <div class="module-icon mb-3">
                        <% if (module.id === 'lexflow') { %>
                            <i class="fas fa-balance-scale fa-4x"></i>
                        <% } else if (module.id === 'protocolx') { %>
                            <i class="fas fa-clipboard-list fa-4x" style="color: var(--accent-green);"></i>
                        <% } else if (module.id === 'auditpro') { %>
                            <i class="fas fa-search fa-4x" style="color: var(--accent-purple);"></i>
                        <% } else if (module.id === 'finsync') { %>
                            <i class="fas fa-chart-line fa-4x" style="color: var(--accent-orange);"></i>
                        <% } else { %>
                            <i class="fas fa-puzzle-piece fa-4x"></i>
                        <% } %>
                    </div>
                    
                    <!-- Module Title and Info -->
                    <h1 class="hero-title">
                        <%= module.name || module.id %>
                        <% if (module.latestVersion) { %>
                            <span class="version-badge">v<%= module.latestVersion.replace('v', '') %></span>
                        <% } %>
                    </h1>
                    
                    <p class="hero-subtitle">
                        <%= module.description || 'Módulo para CoreDesk Framework' %>
                    </p>
                    
                    <!-- Module Badges -->
                    <div class="module-badges mb-3">
                        <span class="module-badge <%= module.category || 'general' %>">
                            <%= module.category ? module.category.charAt(0).toUpperCase() + module.category.slice(1) : 'General' %>
                        </span>
                        <% if (module.downloadable && module.status === 'available') { %>
                            <span class="status-badge available">Disponible</span>
                        <% } else { %>
                            <span class="status-badge">Próximamente</span>
                        <% } %>
                        <% if (module.manifest && module.manifest.license) { %>
                            <span class="license-badge"><%= module.manifest.license %></span>
                        <% } %>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <!-- Download Card -->
                <div class="download-card">
                    <div class="download-info">
                        <% if (module.downloadable && module.status === 'available') { %>
                            <h4><i class="fas fa-download me-2"></i>Descargar Módulo</h4>
                            <div class="download-stats">
                                <div class="stat-item">
                                    <span class="stat-label">Versión:</span>
                                    <span class="stat-value">v<%= module.latestVersion ? module.latestVersion.replace('v', '') : '0.0.2' %></span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Tamaño:</span>
                                    <span class="stat-value"><%= module.packageSize || 'N/A' %></span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Descargas:</span>
                                    <span class="stat-value"><%= module.downloads || 0 %></span>
                                </div>
                            </div>
                            
                            <!-- Compatibility Check -->
                            <% if (compatibility && !compatibility.compatible) { %>
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>Incompatible:</strong> <%= compatibility.reason %>
                                </div>
                            <% } %>
                            
                            <!-- Download Button -->
                            <a href="/modules/<%= module.id %>/download" 
                               class="btn btn-primary btn-lg w-100 download-btn glow-effect"
                               data-module="<%= module.id %>"
                               <%= compatibility && !compatibility.compatible ? 'data-disabled="true"' : '' %>>
                                <i class="fas fa-download me-2"></i>
                                <%= compatibility && !compatibility.compatible ? 'Incompatible' : 'Descargar Ahora' %>
                            </a>
                        <% } else { %>
                            <h4><i class="fas fa-clock me-2"></i>En Desarrollo</h4>
                            <p class="text-muted">Este módulo está actualmente en desarrollo y estará disponible próximamente.</p>
                            <button class="btn btn-secondary btn-lg w-100" disabled>
                                <i class="fas fa-clock me-2"></i>Próximamente
                            </button>
                        <% } %>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Module Details -->
<section class="section">
    <div class="container">
        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <!-- Features -->
                <div class="module-section">
                    <h3><i class="fas fa-star me-2"></i>Características Principales</h3>
                    <% if (module.features && module.features.length > 0) { %>
                        <div class="features-grid">
                            <% module.features.forEach((feature, index) => { %>
                                <div class="feature-item">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    <%= feature %>
                                </div>
                            <% }); %>
                        </div>
                    <% } else { %>
                        <p class="text-muted">No hay características específicas disponibles.</p>
                    <% } %>
                </div>

                <!-- Installation Instructions -->
                <% if (module.downloadable && module.status === 'available') { %>
                    <div class="module-section">
                        <h3><i class="fas fa-cogs me-2"></i>Instrucciones de Instalación</h3>
                        <div class="installation-steps">
                            <div class="step">
                                <div class="step-number">1</div>
                                <div class="step-content">
                                    <h5>Descargar el Módulo</h5>
                                    <p>Haz clic en el botón "Descargar Ahora" para obtener el archivo del módulo.</p>
                                </div>
                            </div>
                            <div class="step">
                                <div class="step-number">2</div>
                                <div class="step-content">
                                    <h5>Extraer Archivos</h5>
                                    <p>Extrae el contenido del archivo descargado en la carpeta <code>modules/<%= module.id %>/</code> dentro de tu instalación de CoreDesk.</p>
                                </div>
                            </div>
                            <div class="step">
                                <div class="step-number">3</div>
                                <div class="step-content">
                                    <h5>Reiniciar CoreDesk</h5>
                                    <p>Reinicia la aplicación CoreDesk para que el módulo sea detectado y cargado automáticamente.</p>
                                </div>
                            </div>
                            <div class="step">
                                <div class="step-number">4</div>
                                <div class="step-content">
                                    <h5>Verificar Instalación</h5>
                                    <p>El módulo aparecerá en la barra de actividades de CoreDesk y estará listo para usar.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                <% } %>

                <!-- Changelog -->
                <% if (module.manifest && module.manifest.changelog) { %>
                    <div class="module-section">
                        <h3><i class="fas fa-history me-2"></i>Historial de Cambios</h3>
                        <div class="changelog">
                            <% Object.entries(module.manifest.changelog).forEach(([version, changes]) => { %>
                                <div class="changelog-entry">
                                    <h5 class="changelog-version">v<%= version %></h5>
                                    <ul class="changelog-changes">
                                        <% changes.forEach(change => { %>
                                            <li><%= change %></li>
                                        <% }); %>
                                    </ul>
                                </div>
                            <% }); %>
                        </div>
                    </div>
                <% } %>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Module Info -->
                <div class="sidebar-card">
                    <h4><i class="fas fa-info-circle me-2"></i>Información del Módulo</h4>
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="info-label">Autor:</span>
                            <span class="info-value"><%= module.manifest && module.manifest.author || 'CoreDesk Team' %></span>
                        </div>
                        <% if (module.manifest && module.manifest.releaseDate) { %>
                            <div class="info-item">
                                <span class="info-label">Fecha de Lanzamiento:</span>
                                <span class="info-value"><%= new Date(module.manifest.releaseDate).toLocaleDateString() %></span>
                            </div>
                        <% } %>
                        <% if (module.packageSize) { %>
                            <div class="info-item">
                                <span class="info-label">Tamaño del Paquete:</span>
                                <span class="info-value"><%= module.packageSize %></span>
                            </div>
                        <% } %>
                        <% if (module.versions && module.versions.length > 0) { %>
                            <div class="info-item">
                                <span class="info-label">Versiones Disponibles:</span>
                                <span class="info-value"><%= module.versions.length %></span>
                            </div>
                        <% } %>
                    </div>
                </div>

                <!-- System Requirements -->
                <% if (module.manifest && module.manifest.compatibility) { %>
                    <div class="sidebar-card">
                        <h4><i class="fas fa-desktop me-2"></i>Requisitos del Sistema</h4>
                        <div class="requirements">
                            <% Object.entries(module.manifest.compatibility).forEach(([key, value]) => { %>
                                <div class="requirement-item">
                                    <span class="req-label"><%= key.charAt(0).toUpperCase() + key.slice(1) %>:</span>
                                    <span class="req-value"><%= value %></span>
                                </div>
                            <% }); %>
                        </div>
                    </div>
                <% } %>

                <!-- Support Links -->
                <div class="sidebar-card">
                    <h4><i class="fas fa-life-ring me-2"></i>Soporte y Enlaces</h4>
                    <div class="support-links">
                        <% if (module.manifest && module.manifest.documentation) { %>
                            <a href="<%= module.manifest.documentation %>" class="support-link" target="_blank">
                                <i class="fas fa-book me-2"></i>Documentación
                            </a>
                        <% } %>
                        <% if (module.manifest && module.manifest.support) { %>
                            <a href="<%= module.manifest.support %>" class="support-link" target="_blank">
                                <i class="fas fa-question-circle me-2"></i>Soporte Técnico
                            </a>
                        <% } %>
                        <a href="/modules" class="support-link">
                            <i class="fas fa-arrow-left me-2"></i>Volver a Módulos
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Download Modal -->
<div class="modal fade" id="downloadModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-download me-2"></i>Descargando <%= module.name || module.id %>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <div class="download-progress">
                    <i class="fas fa-spinner fa-spin fa-3x text-primary mb-3"></i>
                    <h5>Preparando descarga...</h5>
                    <p class="text-muted">
                        Tu descarga comenzará automáticamente. Si no inicia, 
                        <a href="/modules/<%= module.id %>/download" id="manualDownloadLink">haz clic aquí</a>.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const downloadBtn = document.querySelector('.download-btn');
    const downloadModal = new bootstrap.Modal(document.getElementById('downloadModal'));
    
    if (downloadBtn && !downloadBtn.dataset.disabled) {
        downloadBtn.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Show modal
            downloadModal.show();
            
            // Start download
            window.location.href = this.href;
            
            // Auto-close modal after 3 seconds
            setTimeout(() => {
                downloadModal.hide();
            }, 3000);
        });
    }
});
</script>

<style>
.module-hero {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 4rem 0;
}

.module-icon {
    opacity: 0.9;
}

.version-badge {
    font-size: 0.6em;
    background: rgba(255,255,255,0.2);
    padding: 0.25rem 0.5rem;
    border-radius: 15px;
    margin-left: 1rem;
}

.module-badges {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.module-badge, .status-badge, .license-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.85em;
    font-weight: 500;
}

.license-badge {
    background: rgba(255,255,255,0.15);
    color: white;
}

.download-card {
    background: var(--bg-card, white);
    border-radius: 15px;
    padding: 2rem;
    box-shadow: var(--shadow-card, 0 10px 30px rgba(0,0,0,0.1));
    backdrop-filter: blur(10px);
}

.download-stats {
    margin: 1.5rem 0;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--text-secondary, #6c757d);
}

.stat-value {
    font-weight: 600;
}

.module-section {
    background: var(--bg-card, white);
    border-radius: 10px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-card, 0 4px 20px rgba(0,0,0,0.1));
    color: var(--text-primary, #2d3748);
}

.features-grid {
    display: grid;
    gap: 1rem;
    margin-top: 1rem;
}

.feature-item {
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-primary, #eee);
}

.feature-item:last-child {
    border-bottom: none;
}

.installation-steps {
    margin-top: 1.5rem;
}

.step {
    display: flex;
    margin-bottom: 2rem;
    align-items: flex-start;
}

.step-number {
    background: var(--primary-color);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-right: 1rem;
    flex-shrink: 0;
}

.step-content h5 {
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.step-content code {
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.9em;
}

.sidebar-card {
    background: var(--bg-card, white);
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--shadow-card, 0 4px 20px rgba(0,0,0,0.1));
    color: var(--text-primary, #2d3748);
}

.info-grid, .requirements {
    margin-top: 1rem;
}

.info-item, .requirement-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-primary, #f0f0f0);
}

.info-item:last-child, .requirement-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.info-label, .req-label {
    color: var(--text-secondary, #6c757d);
    font-weight: 500;
}

.support-links {
    margin-top: 1rem;
}

.support-link {
    display: block;
    padding: 0.75rem;
    color: var(--text-primary, #495057);
    text-decoration: none;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.support-link:hover {
    background: var(--bg-elevated, #f8f9fa);
    color: var(--accent-cyan, var(--primary-color));
    text-decoration: none;
}

.changelog {
    margin-top: 1rem;
}

.changelog-entry {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.changelog-entry:last-child {
    border-bottom: none;
}

.changelog-version {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.changelog-changes {
    list-style: none;
    padding-left: 0;
}

.changelog-changes li {
    padding: 0.25rem 0;
    color: #6c757d;
}

.changelog-changes li:before {
    content: "•";
    color: var(--primary-color);
    font-weight: bold;
    display: inline-block;
    width: 1em;
    margin-left: -1em;
}

@media (max-width: 768px) {
    .module-hero {
        padding: 2rem 0;
    }
    
    .download-card {
        margin-top: 2rem;
    }
    
    .step {
        flex-direction: column;
        text-align: center;
    }
    
    .step-number {
        margin-right: 0;
        margin-bottom: 1rem;
    }
}
</style>