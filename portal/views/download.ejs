<!-- Hero Section -->
<section class="hero">
    <div class="hero-content">
        <div class="tech-icon-bg mb-4">
            <i class="fas fa-download fa-4x"></i>
        </div>
        <h1 class="hero-title">
            Descargas <span class="gradient-text">CoreDesk</span>
        </h1>
        <p class="hero-subtitle">
            Obtén la última versión de CoreDesk para tu sistema operativo.
            Mantente siempre actualizado con las últimas características y mejoras.
        </p>
        
        <div class="hero-badges">
            <span class="tech-badge floating">Windows</span>
            <span class="tech-badge floating" style="animation-delay: 0.2s;">macOS</span>
            <span class="tech-badge floating" style="animation-delay: 0.4s;">Linux</span>
            <span class="tech-badge floating" style="animation-delay: 0.6s;">Docker</span>
        </div>
    </div>
</section>

<!-- Download Section -->
<section class="section">
    <div class="container">
        <div class="text-center mb-5">
            <h3 class="section-title">Descarga <span class="gradient-text">CoreDesk v<%= versionInfo.version %></span></h3>
            <p class="section-subtitle">
                Versión actual disponible para descarga. Elige tu plataforma preferida.
                <% if (versionInfo.source === 'dynamic') { %>
                    <small class="text-success d-block mt-1">
                        <i class="fas fa-check-circle"></i> Información actualizada dinámicamente
                    </small>
                <% } else { %>
                    <small class="text-warning d-block mt-1">
                        <i class="fas fa-exclamation-triangle"></i> Usando información de respaldo
                    </small>
                <% } %>
            </p>
        </div>
        
        <div class="download-buttons-grid">
            <!-- Windows Download -->
            <div class="download-card">
                <div class="download-header">
                    <i class="fab fa-windows download-icon" style="color: var(--accent-blue);"></i>
                    <h4>Windows</h4>
                    <span class="version-tag">v<%= versionInfo.version %></span>
                </div>
                <div class="download-info">
                    <span class="file-size"><%= versionInfo.platforms.windows.sizeFormatted %></span>
                    <span class="file-type">NSIS Installer</span>
                </div>
                <a href="/download/latest/windows" class="btn btn-primary btn-download">
                    <i class="fas fa-download"></i>
                    Descargar Instalador
                </a>
                <div class="download-options mt-2">
                    <small class="download-note">
                        <i class="fas fa-info-circle"></i>
                        Instalador NSIS - instalación automática
                    </small>
                    <br>
                    <small class="text-muted">
                        <strong>Incluye:</strong> Accesos directos y desinstalador
                    </small>
                </div>
                <small class="download-note">Compatible con Windows 10/11 (64-bit)</small>
            </div>
            
            <!-- macOS Download -->
            <div class="download-card">
                <div class="download-header">
                    <i class="fab fa-apple download-icon" style="color: var(--accent-green);"></i>
                    <h4>macOS</h4>
                    <span class="version-tag">v<%= versionInfo.version %></span>
                </div>
                <div class="download-info">
                    <span class="file-size"><%= versionInfo.platforms.macos.sizeFormatted %></span>
                    <span class="file-type">Application Bundle</span>
                </div>
                <a href="/download/latest/macos" class="btn btn-primary btn-download">
                    <i class="fas fa-download"></i>
                    Descargar Instalador
                </a>
                <div class="download-options mt-2">
                    <small class="download-note">
                        <i class="fas fa-info-circle"></i>
                        Aplicación nativa - instalación sencilla
                    </small>
                    <br>
                    <small class="text-muted">
                        <strong>Incluye:</strong> Integración completa con macOS
                    </small>
                </div>
                <small class="download-note">Compatible con macOS 10.15+ (Intel & Apple Silicon)</small>
            </div>
            
            <!-- Linux Download -->
            <div class="download-card">
                <div class="download-header">
                    <i class="fab fa-linux download-icon" style="color: var(--accent-purple);"></i>
                    <h4>Linux</h4>
                    <span class="version-tag">v<%= versionInfo.version %></span>
                </div>
                <div class="download-info">
                    <span class="file-size"><%= versionInfo.platforms.linux.sizeFormatted %></span>
                    <span class="file-type">AppImage</span>
                </div>
                <a href="/download/latest/linux" class="btn btn-primary btn-download">
                    <i class="fas fa-download"></i>
                    Descargar Instalador
                </a>
                <div class="download-options mt-2">
                    <small class="download-note">
                        <i class="fas fa-info-circle"></i>
                        AppImage portable - sin instalación requerida
                    </small>
                    <br>
                    <small class="text-muted">
                        <strong>Incluye:</strong> Ejecutable independiente y portable
                    </small>
                </div>
                <small class="download-note">Compatible con Ubuntu 18.04+, CentOS 7+, Debian 10+</small>
            </div>
        </div>
    </div>
</section>

<!-- Planned Releases -->
<section class="section">
    <div class="container">
        <h2 class="section-title">Versiones <span class="gradient-text">Planificadas</span></h2>
        <p class="section-subtitle">
            CoreDesk estará disponible para múltiples plataformas con diferentes opciones de instalación.
        </p>
        
        <div class="cards-grid">
            <!-- Windows Release -->
            <div class="card scale-in">
                <i class="fab fa-windows card-icon" style="color: var(--accent-blue);"></i>
                <h3 class="card-title">Windows</h3>
                <div class="status-badge">Próximamente</div>
                <p class="card-description">
                    Instalador MSI nativo con integración completa en Windows.
                </p>
                <ul class="card-features">
                    <li>Instalador MSI nativo</li>
                    <li>Integración con Windows</li>
                    <li>Actualizaciones automáticas</li>
                    <li>Soporte para Windows 10/11</li>
                </ul>
                <div class="system-requirements">
                    <h6>Requisitos del Sistema:</h6>
                    <small>Windows 10 (64-bit) o superior<br>4 GB RAM mínimo<br>2 GB espacio en disco</small>
                </div>
            </div>
            
            <!-- macOS Release -->
            <div class="card scale-in" style="animation-delay: 0.2s;">
                <i class="fab fa-apple card-icon" style="color: var(--accent-green);"></i>
                <h3 class="card-title">macOS</h3>
                <div class="status-badge">Próximamente</div>
                <p class="card-description">
                    Aplicación nativa para macOS con integración completa del sistema.
                </p>
                <ul class="card-features">
                    <li>Aplicación nativa .app</li>
                    <li>Integración con macOS</li>
                    <li>Actualizaciones automáticas</li>
                    <li>Soporte para Intel y Apple Silicon</li>
                </ul>
                <div class="system-requirements">
                    <h6>Requisitos del Sistema:</h6>
                    <small>macOS 11.0 o superior<br>4 GB RAM mínimo<br>2 GB espacio en disco</small>
                </div>
            </div>
            
            <!-- Linux Release -->
            <div class="card scale-in" style="animation-delay: 0.4s;">
                <i class="fab fa-linux card-icon" style="color: var(--accent-purple);"></i>
                <h3 class="card-title">Linux</h3>
                <div class="status-badge">Próximamente</div>
                <p class="card-description">
                    Paquetes para las principales distribuciones de Linux.
                </p>
                <ul class="card-features">
                    <li>Paquetes .deb y .rpm</li>
                    <li>AppImage portable</li>
                    <li>Actualizaciones automáticas</li>
                    <li>Soporte para Ubuntu, Debian, CentOS</li>
                </ul>
                <div class="system-requirements">
                    <h6>Requisitos del Sistema:</h6>
                    <small>Linux (64-bit)<br>4 GB RAM mínimo<br>2 GB espacio en disco</small>
                </div>
            </div>
            
            <!-- Docker Release -->
            <div class="card scale-in" style="animation-delay: 0.6s;">
                <i class="fab fa-docker card-icon" style="color: var(--accent-cyan);"></i>
                <h3 class="card-title">Docker</h3>
                <div class="status-badge">Próximamente</div>
                <p class="card-description">
                    Contenedores Docker para despliegue empresarial y desarrollo.
                </p>
                <ul class="card-features">
                    <li>Imágenes Docker oficiales</li>
                    <li>Docker Compose incluido</li>
                    <li>Configuración empresarial</li>
                    <li>Escalabilidad horizontal</li>
                </ul>
                <div class="system-requirements">
                    <h6>Requisitos del Sistema:</h6>
                    <small>Docker 20.10+<br>4 GB RAM mínimo<br>5 GB espacio en disco</small>
                </div>
            </div>        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="section cta-section">
    <div class="container">
        <div class="cta-content text-center">
            <h2 class="section-title">¿Quieres ser <span class="gradient-text">Beta Tester</span>?</h2>
            <p class="section-subtitle">
                Únete a nuestro programa beta y accede a las versiones de prueba antes que nadie.
                Ayúdanos a mejorar CoreDesk con tu feedback.
            </p>
            <div class="hero-buttons">
                <a href="/contact" class="btn btn-primary glow-effect">
                    <i class="fas fa-rocket me-2"></i>
                    Únete al Beta
                </a>
                <a href="/" class="btn btn-secondary">
                    <i class="fas fa-home me-2"></i>
                    Volver al Inicio
                </a>
            </div>
        </div>
    </div>
</section>
                    </div>
                    <div class="col-md-4">
                        <div class="beta-benefit text-center">
                            <i class="fas fa-comments fa-3x text-success mb-3"></i>
                            <h5 class="fw-bold">Feedback Directo</h5>
                            <p class="text-muted">
                                Tu opinión ayuda a mejorar CoreDesk para todos los usuarios.
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="beta-benefit text-center">
                            <i class="fas fa-star fa-3x text-warning mb-3"></i>
                            <h5 class="fw-bold">Reconocimiento</h5>
                            <p class="text-muted">
                                Forma parte de los créditos como beta tester oficial.
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="mt-5">
                    <button class="btn btn-primary btn-lg" data-bs-toggle="modal" data-bs-target="#betaModal">
                        <i class="fas fa-user-plus me-2"></i>Unirse al Programa Beta
                    </button>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Version History -->
<section class="version-history py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <h2 class="display-5 fw-bold text-center mb-5">Historial de Versiones</h2>
                
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-primary"></div>
                        <div class="timeline-content">
                            <h5 class="fw-bold">v0.0.2-beta <span class="badge bg-warning text-dark">Próxima</span></h5>
                            <p class="text-muted mb-2">Primer lanzamiento beta</p>
                            <ul class="text-muted">
                                <li>Módulos LexFlow y FinSync básicos</li>
                                <li>Panel de administración</li>
                                <li>Sistema de autenticación</li>
                                <li>API REST completa</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="timeline-item">
                        <div class="timeline-marker bg-success"></div>
                        <div class="timeline-content">
                            <h5 class="fw-bold">v0.0.3-beta <span class="badge bg-secondary">Planificada</span></h5>
                            <p class="text-muted mb-2">Segunda iteración beta</p>
                            <ul class="text-muted">
                                <li>Módulos ProtocolX y AuditPro</li>
                                <li>Asistentes virtuales MCP</li>
                                <li>Mejoras de rendimiento</li>
                                <li>Nuevas integraciones</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="timeline-item">
                        <div class="timeline-marker bg-info"></div>
                        <div class="timeline-content">
                            <h5 class="fw-bold">v0.0.2 <span class="badge bg-secondary">Futuro</span></h5>
                            <p class="text-muted mb-2">Lanzamiento estable</p>
                            <ul class="text-muted">
                                <li>Versión completa y estable</li>
                                <li>Todos los módulos integrados</li>
                                <li>Soporte completo para múltiples plataformas</li>
                                <li>Documentación y soporte</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Beta Program Modal -->
<div class="modal fade" id="betaModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-plus me-2"></i>Programa Beta CoreDesk
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    El programa beta estará disponible próximamente. Completa este formulario 
                    para ser notificado cuando abramos las inscripciones.
                </div>
                
                <form id="betaForm">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="betaName" class="form-label">Nombre Completo</label>
                            <input type="text" class="form-control" id="betaName" required>
                        </div>
                        <div class="col-md-6">
                            <label for="betaEmail" class="form-label">Correo Electrónico</label>
                            <input type="email" class="form-control" id="betaEmail" required>
                        </div>
                        <div class="col-md-6">
                            <label for="betaCompany" class="form-label">Empresa/Organización</label>
                            <input type="text" class="form-control" id="betaCompany">
                        </div>
                        <div class="col-md-6">
                            <label for="betaProfession" class="form-label">Profesión</label>
                            <select class="form-select" id="betaProfession">
                                <option value="">Seleccionar...</option>
                                <option value="lawyer">Abogado/a</option>
                                <option value="accountant">Contador/a</option>
                                <option value="consultant">Consultor/a</option>
                                <option value="manager">Gerente/Director</option>
                                <option value="other">Otro</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label for="betaExperience" class="form-label">Experiencia con Software Legal/Contable</label>
                            <textarea class="form-control" id="betaExperience" rows="3" 
                                placeholder="Describe tu experiencia con software similar..."></textarea>
                        </div>
                        <div class="col-12">
                            <label for="betaModules" class="form-label">Módulos de Mayor Interés</label>
                            <div class="form-check-group">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="lexflow" value="lexflow">
                                    <label class="form-check-label" for="lexflow">LexFlow (Gestión Legal)</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="protocolx" value="protocolx">
                                    <label class="form-check-label" for="protocolx">ProtocolX (Automatización)</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="auditpro" value="auditpro">
                                    <label class="form-check-label" for="auditpro">AuditPro (Auditoría)</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="finsync" value="finsync">
                                    <label class="form-check-label" for="finsync">FinSync (Gestión Financiera)</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    Cancelar
                </button>
                <button type="submit" form="betaForm" class="btn btn-primary">
                    <i class="fas fa-paper-plane me-2"></i>Solicitar Acceso Beta
                </button>
            </div>
        </div>
    </div>
</div>
