FROM node:18-alpine

# Metadatos del contenedor
LABEL maintainer="CoreDesk Team"
LABEL description="Portal web oficial de CoreDesk"
LABEL version="0.0.2"

# Configurar directorio de trabajo
WORKDIR /app

# Etapa 1: Build dependencies
FROM node:18-alpine AS builder

# Configurar directorio de trabajo
WORKDIR /app

# Copiar archivos de dependencias
COPY package*.json ./

# Instalar todas las dependencias (incluyendo devDependencies para el build)
RUN npm ci

# Copiar código fuente para el build
COPY . .

# Ejecutar build (compilar SASS, etc.)
RUN npm run build

# Etapa 2: Production
FROM node:18-alpine

# Metadatos del contenedor
LABEL maintainer="CoreDesk Team"
LABEL description="Portal web oficial de CoreDesk"
LABEL version="0.0.2"

# Configurar directorio de trabajo
WORKDIR /app

# Crear usuario no-root para seguridad
RUN addgroup -g 1001 -S nodejs && \
    adduser -S portal -u 1001 -G nodejs

# Crear directorios necesarios con permisos correctos
RUN mkdir -p /app/logs /app/src/public/uploads && \
    chown -R portal:nodejs /app

# Copiar archivos de dependencias
COPY package*.json ./

# Instalar solo dependencias de producción
RUN npm ci --omit=dev && \
    npm cache clean --force

# Copiar código compilado desde la etapa builder
COPY --from=builder --chown=portal:nodejs /app/src ./src
COPY --from=builder --chown=portal:nodejs /app/views ./views
COPY --from=builder --chown=portal:nodejs /app/healthcheck.js ./

# Cambiar a usuario no-root
USER portal

# Exponer puerto
EXPOSE 3000

# Variables de entorno por defecto
ENV NODE_ENV=production
ENV PORT=3000
ENV HOST=0.0.0.0

# Comando de inicio
CMD ["npm", "start"]

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node healthcheck.js
