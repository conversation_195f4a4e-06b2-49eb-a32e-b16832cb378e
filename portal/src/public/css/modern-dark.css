/* ========================================
   MODERN DARK THEME - CoreDesk Portal
   High-tech design with dark colors and vibrant accents
   TEST: Sincronización de volúmenes funcionando ✓
   ======================================== */

/* ========================================
   CSS VARIABLES & ROOT STYLES
   ======================================== */
:root {
  /* Dark Theme Colors */
  --bg-primary: #0a0a0a;
  --bg-secondary: #121212;
  --bg-tertiary: #1a1a1a;
  --bg-card: #1e1e1e;
  --bg-elevated: #252525;
  
  /* Vibrant Accents */
  --accent-cyan: #00d4ff;
  --accent-purple: #6c5ce7;
  --accent-pink: #fd79a8;
  --accent-green: #00b894;
  --accent-orange: #fdcb6e;
  --accent-blue: #74b9ff;
  
  /* Gradients */
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-tertiary: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --gradient-dark: linear-gradient(135deg, #0c0c0c 0%, #1a1a1a 100%);
  --gradient-purple: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-cyber: linear-gradient(135deg, #00d4ff 0%, #6c5ce7 100%);
  
  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #b4b4b4;
  --text-muted: #6b6b6b;
  --text-accent: var(--accent-cyan);
    /* Borders & Effects */
  --border-primary: #333333;
  --border-accent: var(--accent-cyan);
  --shadow-primary: 0 4px 20px rgba(0, 212, 255, 0.1);
  --shadow-elevated: 0 8px 32px rgba(0, 0, 0, 0.3);
  --shadow-glow: 0 0 20px rgba(0, 212, 255, 0.3);
  --shadow-card: 0 4px 20px rgba(0, 0, 0, 0.2);
  --shadow-card-hover: 0 8px 30px rgba(0, 0, 0, 0.3);
  
  /* Animation */
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
  
  /* Typography */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
}

/* ========================================
   GLOBAL STYLES & RESET
   ======================================== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  background: var(--bg-primary) !important;
}

body {
  font-family: var(--font-primary);
  background: var(--bg-primary) !important;
  color: var(--text-primary) !important;
  line-height: 1.6;
  overflow-x: hidden;
  min-height: 100vh;
}

/* ========================================
   SCROLLBAR STYLING
   ======================================== */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--accent-cyan);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-purple);
}

/* ========================================
   UTILITY CLASSES
   ======================================== */
.glow-text {
  text-shadow: 0 0 10px currentColor;
}

.gradient-text {
  background: var(--gradient-cyber);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.cyber-border {
  border: 1px solid var(--border-accent);
  box-shadow: var(--shadow-glow);
}

.floating {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

.slide-in {
  animation: slideIn 0.6s ease-out;
}

@keyframes slideIn {
  from { 
    opacity: 0; 
    transform: translateY(30px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

/* ========================================
   NAVIGATION BAR
   ======================================== */
.navbar {
  background: rgba(10, 10, 10, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-primary);
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 1000;
  transition: var(--transition-normal);
}

.navbar.scrolled {
  background: rgba(10, 10, 10, 0.98);
  box-shadow: var(--shadow-elevated);
}

.navbar .container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.navbar-brand {
  font-size: 1.8rem;
  font-weight: 700;
  text-decoration: none;
  background: var(--gradient-cyber);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.navbar-brand:hover {
  filter: brightness(1.2);
  text-shadow: var(--shadow-glow);
}

.navbar-nav {
  display: flex;
  list-style: none;
  gap: 2rem;
  align-items: center;
  flex-direction: row; /* Forzar dirección horizontal en escritorio */
  position: static; /* Posición normal en escritorio */
  background: transparent; /* Sin fondo en escritorio */
  padding: 0; /* Sin padding en escritorio */
}

.nav-link {
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  transition: var(--transition-normal);
  position: relative;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
}

.nav-link:hover,
.nav-link.active {
  color: var(--accent-cyan);
  background: rgba(0, 212, 255, 0.1);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--gradient-cyber);
  transition: var(--transition-normal);
  transform: translateX(-50%);
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 80%;
}

/* Mobile menu button */
.mobile-menu-btn {
  display: none; /* Solo visible en móviles */
  flex-direction: column;
  gap: 4px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
}

.mobile-menu-btn span {
  width: 25px;
  height: 3px;
  background: var(--accent-cyan);
  transition: var(--transition-normal);
}

.mobile-menu-btn.active span:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-btn.active span:nth-child(2) {
  opacity: 0;
}

.mobile-menu-btn.active span:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* ========================================
   MAIN CONTENT
   ======================================== */
.main-content {
  min-height: calc(100vh - 80px);
  padding-top: 80px; /* Altura del navbar fijo */
}

/* ========================================
   HERO SECTION
   ======================================== */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-primary);
  position: relative;
  overflow: hidden;
  padding: 2rem;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 50%, rgba(108, 92, 231, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(253, 121, 168, 0.1) 0%, transparent 50%);
  animation: float 6s ease-in-out infinite;
}

.hero-content {
  text-align: center;
  max-width: 1000px;
  z-index: 2;
  position: relative;
}

.hero-title {
  font-size: clamp(2.5rem, 8vw, 4.5rem);
  font-weight: 800;
  margin-bottom: 1.5rem;
  background: var(--gradient-cyber);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.2;
}

.hero-subtitle {
  font-size: clamp(1.1rem, 3vw, 1.4rem);
  color: var(--text-secondary);
  margin-bottom: 3rem;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

.hero-buttons {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 4rem;
}

.hero-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  margin-top: 4rem;
}

.hero-stat {
  text-align: center;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 1rem;
  border: 1px solid var(--border-primary);
  backdrop-filter: blur(10px);
}

.hero-stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--accent-cyan);
  display: block;
}

.hero-stat-label {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-top: 0.5rem;
}

/* ========================================
   BUTTONS
   ======================================== */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border: none;
  border-radius: 0.75rem;
  font-weight: 600;
  font-size: 1rem;
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: var(--transition-slow);
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: var(--gradient-cyber);
  color: var(--text-primary);
  box-shadow: var(--shadow-primary);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 212, 255, 0.3);
}

.btn-secondary {
  background: transparent;
  color: var(--accent-cyan);
  border: 2px solid var(--accent-cyan);
}

.btn-secondary:hover {
  background: var(--accent-cyan);
  color: var(--bg-primary);
  box-shadow: var(--shadow-glow);
}

.btn-outline {
  background: transparent;
  color: var(--text-secondary);
  border: 1px solid var(--border-primary);
}

.btn-outline:hover {
  background: var(--bg-elevated);
  color: var(--text-primary);
  border-color: var(--accent-cyan);
}

/* ========================================
   CARDS & SECTIONS
   ======================================== */
.section {
  padding: 6rem 2rem;
  position: relative;
}

.section-title {
  font-size: clamp(2rem, 5vw, 3rem);
  text-align: center;
  margin-bottom: 1rem;
  background: var(--gradient-cyber);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-subtitle {
  text-align: center;
  color: var(--text-secondary);
  font-size: 1.1rem;
  margin-bottom: 4rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.card {
  background: var(--bg-card);
  border-radius: 1rem;
  padding: 2.5rem;
  border: 1px solid var(--border-primary);
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-cyber);
  transform: scaleX(0);
  transition: var(--transition-normal);
  transform-origin: left;
}

.card:hover::before {
  transform: scaleX(1);
}

.card:hover {
  transform: translateY(-8px);
  border-color: var(--accent-cyan);
  box-shadow: var(--shadow-elevated);
}

.card-icon {
  font-size: 3rem;
  color: var(--accent-cyan);
  margin-bottom: 1.5rem;
  display: block;
}

.card-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.card-description {
  color: var(--text-secondary);
  line-height: 1.7;
  margin-bottom: 1.5rem;
}

.card-features {
  list-style: none;
  margin-bottom: 2rem;
}

.card-features li {
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
  padding-left: 1.5rem;
  position: relative;
}

.card-features li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--accent-green);
  font-weight: bold;
}

/* ========================================
   FOOTER
   ======================================== */
.footer {
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-primary);
  padding: 4rem 2rem 2rem;
}

.footer-content {
  max-width: 1400px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 3rem;
  margin-bottom: 3rem;
}

.footer-section h3 {
  color: var(--text-primary);
  margin-bottom: 1.5rem;
  font-size: 1.2rem;
}

.footer-section p,
.footer-section a {
  color: var(--text-secondary);
  text-decoration: none;
  line-height: 1.8;
  transition: var(--transition-normal);
}

.footer-section a:hover {
  color: var(--accent-cyan);
}

.footer-links {
  list-style: none;
}

.footer-links li {
  margin-bottom: 0.5rem;
}

.footer-bottom {
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid var(--border-primary);
  color: var(--text-muted);
}

/* ========================================
   SOCIAL LINKS
   ======================================== */
.social-links {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.social-link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--bg-elevated);
  color: var(--text-secondary);
  border-radius: 50%;
  text-decoration: none;
  transition: var(--transition-normal);
  border: 1px solid var(--border-primary);
}

.social-link:hover {
  background: var(--accent-cyan);
  color: var(--bg-primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-glow);
}

/* ========================================
   ENHANCED ANIMATIONS
   ======================================== */
.bounce {
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.rotate {
  animation: rotate 20s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.glow-pulse {
  animation: glowPulse 2s ease-in-out infinite alternate;
}

@keyframes glowPulse {
  from {
    box-shadow: 0 0 5px var(--accent-cyan), 0 0 10px var(--accent-cyan), 0 0 15px var(--accent-cyan);
  }
  to {
    box-shadow: 0 0 10px var(--accent-cyan), 0 0 20px var(--accent-cyan), 0 0 30px var(--accent-cyan);
  }
}

/* ===== MODULES PAGE STYLES ===== */

/* Module Cards */
.module-card {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: 20px;
    padding: 3rem;
    margin-bottom: 3rem;
    position: relative;
    overflow: hidden;
    transition: var(--transition-normal);
}

.module-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 48%, var(--accent-cyan) 49%, var(--accent-cyan) 51%, transparent 52%);
    opacity: 0.02;
    pointer-events: none;
}

.module-card:hover {
    transform: translateY(-5px);
    border-color: var(--accent-cyan);
    box-shadow: var(--shadow-glow);
}

/* Module Header */
.module-header {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.module-icon {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: var(--text-primary);
    background: var(--gradient-cyber);
    position: relative;
    overflow: hidden;
}

.module-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: var(--transition-slow);
}

.module-icon:hover::before {
    left: 100%;
}

.module-icon.automation {
    background: linear-gradient(135deg, var(--accent-green) 0%, #00b894 100%);
}

.module-icon.audit {
    background: linear-gradient(135deg, var(--accent-blue) 0%, #0984e3 100%);
}

.module-icon.finance {
    background: linear-gradient(135deg, var(--accent-orange) 0%, #e17055 100%);
}

.module-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    background: var(--gradient-cyber);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.module-badge {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-top: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.module-badge.legal {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: var(--text-primary);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.module-badge.automation {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: var(--text-primary);
    box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
}

.module-badge.audit {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: var(--text-primary);
    box-shadow: 0 4px 15px rgba(240, 147, 251, 0.3);
}

.module-badge.finance {
    background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
    color: var(--text-primary);
    box-shadow: 0 4px 15px rgba(253, 203, 110, 0.3);
}

/* Status Badge */
.status-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--gradient-cyber);
    color: var(--text-primary);
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    box-shadow: 0 2px 10px rgba(0, 212, 255, 0.3);
    z-index: 5;
}

/* ========================================
   MODULES PAGE SPECIFIC STYLES
   ======================================== */

/* Module Badges */
.module-badge {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-top: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.module-badge.legal {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: var(--text-primary);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.module-badge.automation {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: var(--text-primary);
    box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
}

.module-badge.audit {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: var(--text-primary);
    box-shadow: 0 4px 15px rgba(240, 147, 251, 0.3);
}

.module-badge.finance {
    background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
    color: var(--text-primary);
    box-shadow: 0 4px 15px rgba(253, 203, 110, 0.3);
}

/* Status Badge */
.status-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--gradient-cyber);
    color: var(--text-primary);
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    box-shadow: 0 2px 10px rgba(0, 212, 255, 0.3);
    z-index: 5;
}

/* ========================================
   CONTACT PAGE STYLES
   ======================================== */

/* Contact Form */
.contact-section {
    background: var(--bg-secondary);
}

.contact-card {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: 20px;
    padding: 3rem;
}

.contact-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 48%, var(--accent-cyan) 49%, var(--accent-cyan) 51%, transparent 52%);
    opacity: 0.02;
    pointer-events: none;
}

.contact-header {
    text-align: center;
    margin-bottom: 2rem;
}

.contact-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    text-align: center;
}

.contact-subtitle {
    font-size: 1.125rem;
    color: var(--text-secondary);
    margin: 0;
}

.contact-form {
    position: relative;
    z-index: 2;
}

.form-group {
    margin-bottom: 2rem;
}

.form-label {
    color: var(--text-primary);
    font-weight: 600;
}

.form-control,
.form-select {
    background: var(--bg-elevated);
    border: 2px solid var(--border-primary);
    border-radius: 10px;
    color: var(--text-primary);
    padding: 1rem 1.5rem;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--accent-cyan);
    box-shadow: 0 0 0 0.2rem rgba(0, 212, 255, 0.25);
    color: var(--text-primary);
}

.form-control::placeholder {
    color: var(--text-muted);
}

.form-select option {
    background: var(--bg-elevated);
    color: var(--text-primary);
}

.form-check {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.form-check-input {
    width: 1.25rem;
    height: 1.25rem;
    margin: 0;
    background: var(--bg-elevated);
    border: 2px solid var(--border-primary);
    border-radius: 4px;
    transition: var(--transition-normal);
}

.form-check-input:checked {
    background-color: var(--accent-cyan);
    border-color: var(--accent-cyan);
}

.form-check-input:focus {
    border-color: var(--accent-cyan);
    box-shadow: 0 0 0 0.2rem rgba(0, 212, 255, 0.25);
}

.form-check-label {
    color: var(--text-secondary);
    margin: 0;
    cursor: pointer;
}

.form-submit {
    text-align: center;
    margin-top: 2rem;
}

.invalid-feedback {
    color: var(--accent-pink);
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

.form-control.is-invalid,
.form-select.is-invalid {
    border-color: var(--accent-pink);
}

/* Contact Info Cards */
.contact-info-card {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    height: 100%;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.contact-info-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--accent-cyan) 0%, transparent 50%);
    opacity: 0;
    transition: var(--transition-normal);
}

.contact-info-card:hover::before {
    opacity: 0.05;
}

.contact-info-card:hover {
    transform: translateY(-5px);
    border-color: var(--accent-cyan);
    box-shadow: var(--shadow-glow);
}

.contact-info-icon {
    width: 70px;
    height: 70px;
    background: var(--gradient-cyber);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 1.75rem;
    color: var(--text-primary);
    position: relative;
    z-index: 2;
}

.contact-info-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
    position: relative;
    z-index: 2;
}

.contact-info-text {
    color: var(--text-secondary);
    margin: 0;
    position: relative;
    z-index: 2;
}

.contact-info-link {
    color: var(--accent-cyan);
    text-decoration: none;
    transition: var(--transition-fast);
}

.contact-info-link:hover {
    color: var(--text-primary);
    text-decoration: underline;
}

/* Social Links */
.social-links {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
}

.social-link {
    width: 50px;
    height: 50px;
    background: var(--bg-card);
    border: 2px solid var(--border-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition-normal);
    font-size: 1.25rem;
}

.social-link:hover {
    border-color: var(--accent-cyan);
    color: var(--accent-cyan);
    transform: translateY(-3px);
    box-shadow: var(--shadow-glow);
}

/* Response Messages */
.alert-success {
    background: rgba(0, 184, 148, 0.1);
    border: 1px solid var(--accent-green);
    color: var(--accent-green);
    border-radius: 10px;
    padding: 1rem;
}

.alert-danger {
    background: rgba(253, 121, 168, 0.1);
    border: 1px solid var(--accent-pink);
    color: var(--accent-pink);
    border-radius: 10px;
    padding: 1rem;
}

/* =================================
   BOOTSTRAP OVERRIDES FOR DARK THEME
   ================================= */

/* Override Bootstrap background classes */
.bg-light {
  background-color: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
}

.bg-white {
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
}

.bg-success {
  background: linear-gradient(135deg, var(--accent-cyan), var(--accent-purple)) !important;
  color: var(--text-primary) !important;
}

.bg-warning {
  background: linear-gradient(135deg, #ffd700, #ff8c00) !important;
  color: var(--bg-primary) !important;
}

.bg-info {
  background: linear-gradient(135deg, var(--accent-cyan), var(--accent-blue)) !important;
  color: var(--text-primary) !important;
}

/* Override Bootstrap text colors */
.text-muted {
  color: var(--text-secondary) !important;
}

.text-dark {
  color: var(--text-primary) !important;
}

/* Override Bootstrap card styles */
.card {
  background-color: var(--bg-card) !important;
  border: 1px solid var(--border-primary) !important;
  color: var(--text-primary) !important;
}

.card-header {
  background-color: var(--bg-secondary) !important;
  border-bottom: 1px solid var(--border-primary) !important;
  color: var(--text-primary) !important;
}

.card-body {
  background-color: var(--bg-card) !important;
  color: var(--text-primary) !important;
}

/* Override Bootstrap alert styles */
.alert {
  background-color: var(--bg-secondary) !important;
  border: 1px solid var(--border-primary) !important;
  color: var(--text-primary) !important;
}

.alert-warning {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.2), rgba(255, 140, 0, 0.2)) !important;
  border-color: #ffd700 !important;
  color: var(--text-primary) !important;
}

.alert-info {
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.2), rgba(116, 185, 255, 0.2)) !important;
  border-color: var(--accent-cyan) !important;
  color: var(--text-primary) !important;
}

.alert-success {
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.2), rgba(108, 92, 231, 0.2)) !important;
  border-color: var(--accent-cyan) !important;
  color: var(--text-primary) !important;
}

/* Override Bootstrap form styles */
.form-control {
  background-color: var(--bg-secondary) !important;
  border: 1px solid var(--border-primary) !important;
  color: var(--text-primary) !important;
}

.form-control:focus {
  background-color: var(--bg-secondary) !important;
  border-color: var(--accent-cyan) !important;
  box-shadow: 0 0 0 0.2rem rgba(0, 212, 255, 0.25) !important;
  color: var(--text-primary) !important;
}

.form-control::placeholder {
  color: var(--text-secondary) !important;
}

.form-select {
  background-color: var(--bg-secondary) !important;
  border: 1px solid var(--border-primary) !important;
  color: var(--text-primary) !important;
}

.form-select:focus {
  background-color: var(--bg-secondary) !important;
  border-color: var(--accent-cyan) !important;
  box-shadow: 0 0 0 0.2rem rgba(0, 212, 255, 0.25) !important;
  color: var(--text-primary) !important;
}

/* Override Bootstrap button styles */
.btn-primary {
  background: linear-gradient(135deg, var(--accent-cyan), var(--accent-blue)) !important;
  border-color: var(--accent-cyan) !important;
  color: var(--text-primary) !important;
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple)) !important;
  border-color: var(--accent-blue) !important;
  transform: translateY(-2px);
}

.btn-secondary {
  background-color: var(--bg-secondary) !important;
  border-color: var(--border-primary) !important;
  color: var(--text-primary) !important;
}

.btn-secondary:hover {
  background-color: var(--bg-tertiary) !important;
  border-color: var(--accent-cyan) !important;
  color: var(--accent-cyan) !important;
}

/* Override Bootstrap modal styles */
.modal-content {
  background-color: var(--bg-card) !important;
  border: 1px solid var(--border-primary) !important;
  color: var(--text-primary) !important;
}

.modal-header {
  background-color: var(--bg-secondary) !important;
  border-bottom: 1px solid var(--border-primary) !important;
  color: var(--text-primary) !important;
}

.modal-body {
  background-color: var(--bg-card) !important;
  color: var(--text-primary) !important;
}

.modal-footer {
  background-color: var(--bg-secondary) !important;
  border-top: 1px solid var(--border-primary) !important;
}

/* Override Bootstrap table styles */
.table {
  color: var(--text-primary) !important;
}

.table-dark {
  background-color: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
}

/* Override Bootstrap list group styles */
.list-group-item {
  background-color: var(--bg-secondary) !important;
  border: 1px solid var(--border-primary) !important;
  color: var(--text-primary) !important;
}

/* =================================
   PAGE-SPECIFIC OVERRIDES FOR EMBEDDED STYLES
   ================================= */

/* Contact page specific overrides */
.contact-card {
  background: var(--bg-card) !important;
  color: var(--text-primary) !important;
  border: 1px solid var(--border-primary) !important;
  box-shadow: var(--shadow-card) !important;
}

.contact-card:hover {
  background: var(--bg-elevated) !important;
  transform: translateY(-2px) !important;
  box-shadow: var(--shadow-elevated) !important;
}

/* Force all white backgrounds to dark */
[style*="background: white"],
[style*="background-color: white"],
[style*="background: #fff"],
[style*="background-color: #fff"],
[style*="background: #ffffff"],
[style*="background-color: #ffffff"] {
  background: var(--bg-card) !important;
  color: var(--text-primary) !important;
}

/* Force all embedded light backgrounds to dark */
div[style*="background-color: #f8f9fa"],
div[style*="background-color: #e9ecef"],
div[style*="background: #f8f9fa"],
div[style*="background: #e9ecef"] {
  background: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
}

/* Override any embedded alert styles */
.alert[style*="background-color"],
div[style*="background-color"][class*="alert"] {
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(108, 92, 231, 0.1)) !important;
  border: 1px solid var(--accent-cyan) !important;
  color: var(--text-primary) !important;
}

/* Force form controls to maintain dark theme regardless of embedded styles */
.form-control[style],
.form-select[style],
input[style],
textarea[style],
select[style] {
  background-color: var(--bg-secondary) !important;
  border-color: var(--border-primary) !important;
  color: var(--text-primary) !important;
}

.form-control[style]:focus,
.form-select[style]:focus,
input[style]:focus,
textarea[style]:focus,
select[style]:focus {
  background-color: var(--bg-secondary) !important;
  border-color: var(--accent-cyan) !important;
  box-shadow: 0 0 0 0.2rem rgba(0, 212, 255, 0.25) !important;
  color: var(--text-primary) !important;
}

/* Ensure labels and text remain visible */
.form-label,
label,
.text-muted,
p,
span,
div {
  color: var(--text-primary) !important;
}

.form-label.text-muted,
label.text-muted,
.text-muted {
  color: var(--text-secondary) !important;
}

/* About page specific fixes */
.tech-stack-item,
.feature-card,
.stats-card {
  background: var(--bg-card) !important;
  color: var(--text-primary) !important;
  border: 1px solid var(--border-primary) !important;
}

/* Ensure all cards across all pages are dark */
[class*="card"] {
  background: var(--bg-card) !important;
  color: var(--text-primary) !important;
  border: 1px solid var(--border-primary) !important;
}

[class*="card"]:hover {
  background: var(--bg-elevated) !important;
  border-color: var(--accent-cyan) !important;
}

/* Force container backgrounds */
.container,
.container-fluid,
.row,
.col,
[class*="col-"] {
  background: transparent !important;
  color: inherit !important;
}

/* Ensure section backgrounds */
section {
  background: var(--bg-primary) !important;
  color: var(--text-primary) !important;
}

section.bg-light {
  background: var(--bg-secondary) !important;
}

/* Dark theme override class for JavaScript-forced elements */
.dark-theme-override {
  background: var(--bg-card) !important;
  color: var(--text-primary) !important;
  border-color: var(--border-primary) !important;
}

.dark-theme-override input,
.dark-theme-override textarea,
.dark-theme-override select,
.dark-theme-override .form-control,
.dark-theme-override .form-select {
  background: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
  border-color: var(--border-primary) !important;
}

.dark-theme-override label,
.dark-theme-override .form-label,
.dark-theme-override p,
.dark-theme-override span,
.dark-theme-override div {
  color: var(--text-primary) !important;
}

/* =================================
   ABOUT PAGE - MODERN TIMELINE STYLES
   ================================= */

/* Timeline Container */
.timeline-container {
    position: relative;
    max-width: 1000px;
    margin: 0 auto;
    padding: 3rem 0;
}

/* Central Timeline Line */
.timeline-line {
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--gradient-cyber);
    transform: translateX(-50%);
    border-radius: 2px;
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
}

/* Timeline Items */
.timeline-item {
    position: relative;
    margin-bottom: 4rem;
    width: 100%;
}

/* Timeline Dots */
.timeline-dot {
    position: absolute;
    left: 50%;
    top: 1rem;
    transform: translateX(-50%);
    width: 20px;
    height: 20px;
    background: var(--bg-primary);
    border: 4px solid var(--accent-cyan);
    border-radius: 50%;
    z-index: 10;
    transition: var(--transition-normal);
}

.timeline-dot-inner {
    width: 8px;
    height: 8px;
    background: var(--accent-cyan);
    border-radius: 50%;
    margin: 2px;
    animation: pulse 2s infinite;
}

.timeline-item:hover .timeline-dot {
    border-color: var(--accent-purple);
    box-shadow: 0 0 20px rgba(108, 92, 231, 0.6);
}

.timeline-item:hover .timeline-dot-inner {
    background: var(--accent-purple);
}

/* Timeline Content */
.timeline-content {
    width: 45%;
    position: relative;
}

.timeline-content.left {
    left: 0;
    text-align: right;
    padding-right: 3rem;
}

.timeline-content.right {
    left: 55%;
    text-align: left;
    padding-left: 3rem;
}

/* Timeline Cards */
.timeline-card {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: 15px;
    padding: 2rem;
    position: relative;
    transition: var(--transition-normal);
    box-shadow: var(--shadow-card);
    overflow: hidden;
}

.timeline-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-cyber);
}

.timeline-card:hover {
    transform: translateY(-5px);
    border-color: var(--accent-cyan);
    box-shadow: var(--shadow-elevated), 0 0 30px rgba(0, 212, 255, 0.2);
}

/* Timeline Card Arrows */
.timeline-content.left .timeline-card::after {
    content: '';
    position: absolute;
    top: 1.5rem;
    right: -15px;
    width: 0;
    height: 0;
    border-top: 15px solid transparent;
    border-bottom: 15px solid transparent;
    border-left: 15px solid var(--bg-card);
}

.timeline-content.right .timeline-card::after {
    content: '';
    position: absolute;
    top: 1.5rem;
    left: -15px;
    width: 0;
    height: 0;
    border-top: 15px solid transparent;
    border-bottom: 15px solid transparent;
    border-right: 15px solid var(--bg-card);
}

/* Timeline Year Badge */
.timeline-year {
    display: inline-block;
    background: var(--gradient-purple);
    color: var(--text-primary);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 1rem;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.timeline-title {
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    line-height: 1.3;
}

.timeline-description {
    color: var(--text-secondary);
    font-size: 1rem;
    line-height: 1.6;
    margin: 0;
}

/* Responsive Timeline */
@media (max-width: 768px) {
    .timeline-line {
        left: 2rem;
    }
    
    .timeline-dot {
        left: 2rem;
    }
    
    .timeline-content.left,
    .timeline-content.right {
        width: calc(100% - 5rem);
        left: 5rem;
        text-align: left;
        padding-left: 0;
        padding-right: 0;
    }
    
    .timeline-content.left .timeline-card::after,
    .timeline-content.right .timeline-card::after {
        display: none;
    }
    
    .timeline-card {
        margin-left: 1rem;
    }
}

/* ========================================
   CONTACT PAGE SPECIFIC STYLES
   ======================================== */
.contact-card {
    padding: 30px 20px !important;
    border-radius: 12px !important;
    background: var(--bg-card) !important;
    border: 1px solid var(--border-primary) !important;
    box-shadow: var(--shadow-card) !important;
    transition: transform 0.3s ease, box-shadow 0.3s ease !important;
}

.contact-card:hover {
    transform: translateY(-5px) !important;
    box-shadow: var(--shadow-card-hover) !important;
}

#formMessage {
    padding: 15px !important;
    border-radius: 8px !important;
    margin-top: 20px !important;
}

/* ========================================
   ABOUT PAGE SPECIFIC STYLES
   ======================================== */

/* Tech Badges */
.tech-badge {
    display: inline-block;
    background: var(--gradient-cyber);
    color: var(--text-primary);
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 500;
    margin: 0 8px 8px 0;
    box-shadow: var(--shadow-glow);
    transition: var(--transition-normal);
}

.tech-badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 212, 255, 0.4);
}

/* Tech Icon in Hero */
.tech-icon-bg {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 120px;
    height: 120px;
    background: var(--gradient-cyber);
    border-radius: 50%;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-glow);
    animation: pulse 2s infinite;
}

.tech-icon-bg i {
    color: var(--text-primary);
    font-size: 3rem;
}

/* Feature Icons */
.feature-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    border-radius: 20px;
    margin-bottom: 1.5rem;
    font-size: 2rem;
    color: var(--text-primary);
    transition: var(--transition-normal);
}

.feature-icon.ai {
    background: var(--gradient-purple);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.feature-icon.automation {
    background: var(--gradient-tertiary);
    box-shadow: 0 8px 25px rgba(79, 172, 254, 0.3);
}

.feature-icon.security {
    background: var(--gradient-secondary);
    box-shadow: 0 8px 25px rgba(240, 147, 251, 0.3);
}

/* Tech Icons in Technology Section */
.tech-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    background: var(--gradient-cyber);
    border-radius: 15px;
    margin-bottom: 1rem;
    font-size: 1.5rem;
    color: var(--text-primary);
    transition: var(--transition-normal);
}

.tech-grid .card:hover .tech-icon {
    transform: scale(1.1);
    box-shadow: var(--shadow-glow);
}

/* Timeline Enhancements */
.timeline-section {
    position: relative;
    overflow: hidden;
}

.timeline-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 50% 50%, rgba(0, 212, 255, 0.05) 0%, transparent 70%);
    pointer-events: none;
}

/* Floating Animation */
@keyframes floating {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

.floating {
    animation: floating 3s ease-in-out infinite;
}

/* Pulse Animation */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 212, 255, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(0, 212, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(0, 212, 255, 0);
    }
}

/* ========================================
   DOWNLOAD PAGE SPECIFIC STYLES
   ======================================== */

/* Alert Card */
.alert-card {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: 15px;
    padding: 3rem 2rem;
    box-shadow: var(--shadow-card);
    max-width: 600px;
    margin: 0 auto;
}

.alert-card i {
    color: var(--accent-orange);
    margin-bottom: 1rem;
}

/* System Requirements */
.system-requirements {
    background: var(--bg-secondary);
    border-radius: 10px;
    padding: 1rem;
    margin-top: 1.5rem;
    border-left: 3px solid var(--accent-cyan);
}

.system-requirements h6 {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.system-requirements small {
    color: var(--text-secondary);
    line-height: 1.5;
}

/* ========================================
   FAQ SECTION STYLES
   ======================================== */

/* FAQ Container */
.faq-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem 0;
}

/* FAQ Items */
.faq-item {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: 15px;
    margin-bottom: 1rem;
    overflow: hidden;
    transition: var(--transition-normal);
    box-shadow: var(--shadow-card);
}

.faq-item:hover {
    border-color: var(--accent-cyan);
    box-shadow: var(--shadow-elevated);
    transform: translateY(-2px);
}

/* FAQ Question */
.faq-question {
    padding: 1.5rem 2rem;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--bg-card);
    transition: var(--transition-normal);
    position: relative;
}

.faq-question::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-cyber);
    opacity: 0;
    transition: var(--transition-normal);
}

.faq-question:hover::before {
    opacity: 1;
}

.faq-question:hover {
    background: var(--bg-elevated);
}

.faq-question h4 {
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
    padding-right: 2rem;
    line-height: 1.4;
}

/* FAQ Icon */
.faq-icon {
    color: var(--accent-cyan);
    font-size: 1.2rem;
    transition: transform 0.3s ease, color 0.3s ease;
    flex-shrink: 0;
}

.faq-question:hover .faq-icon {
    color: var(--accent-purple);
}

/* FAQ Answer */
.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.4s ease, padding 0.4s ease;
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-primary);
}

.faq-answer.active {
    max-height: 200px;
    padding: 1.5rem 2rem;
}

.faq-answer p {
    color: var(--text-secondary);
    font-size: 1rem;
    line-height: 1.6;
    margin: 0;
}

/* Responsive FAQ */
@media (max-width: 768px) {
    .faq-question {
        padding: 1rem 1.5rem;
    }
    
    .faq-question h4 {
        font-size: 1rem;
        padding-right: 1rem;
    }
    
    .faq-answer.active {
        padding: 1rem 1.5rem;
    }
    
    .faq-icon {
        font-size: 1rem;
    }
}

/* ========================================
   DOWNLOAD PAGE CARDS - MISSING STYLES
   ======================================== */

/* Download Buttons Grid */
.download-buttons-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

/* Download Card */
.download-card {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: 20px;
    padding: 2.5rem;
    text-align: center;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-card);
}

.download-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-cyber);
    transform: scaleX(0);
    transition: var(--transition-normal);
    transform-origin: left;
}

.download-card:hover::before {
    transform: scaleX(1);
}

.download-card:hover {
    transform: translateY(-8px);
    border-color: var(--accent-cyan);
    box-shadow: var(--shadow-elevated), 0 0 30px rgba(0, 212, 255, 0.2);
}

/* Download Header */
.download-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.download-header h4 {
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

/* Download Icon */
.download-icon {
    font-size: 3rem;
    margin-bottom: 0.5rem;
    display: block;
    transition: var(--transition-normal);
}

.download-card:hover .download-icon {
    transform: scale(1.1);
    filter: brightness(1.2);
}

/* Version Tag */
.version-tag {
    background: var(--gradient-cyber);
    color: var(--text-primary);
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    box-shadow: 0 2px 10px rgba(0, 212, 255, 0.3);
}

/* Download Info */
.download-info {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.file-size,
.file-type {
    color: var(--text-secondary);
    font-size: 0.9rem;
    padding: 0.25rem 0.75rem;
    background: var(--bg-secondary);
    border-radius: 8px;
    border: 1px solid var(--border-primary);
}

/* Download Button */
.btn-download {
    background: var(--gradient-cyber) !important;
    color: var(--text-primary) !important;
    border: none !important;
    padding: 1rem 2rem !important;
    font-weight: 600 !important;
    font-size: 1rem !important;
    border-radius: 12px !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
    text-decoration: none !important;
    transition: var(--transition-normal) !important;
    position: relative !important;
    overflow: hidden !important;
    box-shadow: var(--shadow-glow) !important;
}

.btn-download::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: var(--transition-slow);
}

.btn-download:hover::before {
    left: 100%;
}

.btn-download:hover {
    transform: translateY(-3px) !important;
    box-shadow: 0 8px 25px rgba(0, 212, 255, 0.4) !important;
    color: var(--text-primary) !important;
    text-decoration: none !important;
}

/* Download Options */
.download-options {
    margin-top: 1rem;
}

.download-note {
    color: var(--text-secondary);
    font-size: 0.85rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.download-note i {
    color: var(--accent-cyan);
    font-size: 0.8rem;
}

.download-note strong {
    color: var(--text-primary);
}

/* CTA Section Styling */
.cta-section {
    background: var(--bg-secondary);
    position: relative;
    overflow: hidden;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 50%, rgba(108, 92, 231, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(0, 212, 255, 0.1) 0%, transparent 50%);
    animation: float 8s ease-in-out infinite;
}

.cta-content {
    position: relative;
    z-index: 2;
}

/* Glow Effect for CTA Button */
.glow-effect {
    position: relative;
    overflow: hidden;
}

.glow-effect::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-cyber);
    opacity: 0;
    border-radius: inherit;
    transition: var(--transition-normal);
    z-index: -1;
}

.glow-effect:hover::after {
    opacity: 0.2;
    animation: glowPulse 1.5s ease-in-out infinite alternate;
}

/* Scale In Animation */
.scale-in {
    animation: scaleIn 0.6s ease-out;
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Responsive Download Cards */
@media (max-width: 768px) {
    .download-buttons-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .download-card {
        padding: 2rem;
    }
    
    .download-icon {
        font-size: 2.5rem;
    }
    
    .download-header h4 {
        font-size: 1.3rem;
    }
    
    .download-info {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .btn-download {
        padding: 0.875rem 1.5rem !important;
        font-size: 0.9rem !important;
    }
}