const express = require('express');
const config = require('../config');
const logger = require('../middleware/logger');
const { asyncHandler } = require('../middleware/errorHandler');
const emailService = require('../services/emailService');
const {
  contactFormLimiter,
  advancedValidation,
  honeypotValidation,
  timingValidation,
  suspiciousIPHandler
} = require('../middleware/antiSpam');

const router = express.Router();

// Página principal
router.get('/', asyncHandler(async (req, res) => {
  logger.info('Acceso a página principal');
    const pageData = {
    title: `${config.appName} - ${config.appDescription}`,
    description: config.appDescription,
    keywords: 'coredesk, legal, contable, lexflow, protocolx, auditpro, finsync, asistentes virtuales, IA',
    modules: config.modules,
    currentYear: new Date().getFullYear(),
    version: config.appVersion,
    config: config, // Añadir objeto config completo
    features: [
      {
        icon: 'fas fa-brain',
        title: 'Asistentes Virtuales Inteligentes',
        description: 'Sistema MCP con IA conversacional especializada para cada módulo'
      },
      {
        icon: 'fas fa-balance-scale',
        title: 'Gestión Legal Avanzada',
        description: 'LexFlow: Análisis de casos, jurisprudencia y documentos legales'
      },
      {
        icon: 'fas fa-cogs',
        title: 'Automatización de Procesos',
        description: 'ProtocolX: Flujos de trabajo digitales y protocolos automatizados'
      },      {
        icon: 'fas fa-search',
        title: 'Auditoría Profesional',
        description: 'AuditPro: Módulo en desarrollo para auditorías y control interno'
      },
      {
        icon: 'fas fa-chart-line',
        title: 'Gestión Financiera',
        description: 'FinSync: Módulo en desarrollo para contabilidad y finanzas'
      },
      {
        icon: 'fas fa-shield-alt',
        title: 'Seguridad y Confiabilidad',
        description: 'Arquitectura robusta con Docker y sistemas de respaldo'
      }
    ],
    stats: {
      modules: Object.keys(config.modules).length,
      tools: 25, // Número estimado de herramientas MCP
      integrations: 15, // Integraciones disponibles
      users: '1000+' // Usuarios estimados
    }
  };
  
  res.render('index', pageData);
}));

// Página Acerca de
router.get('/about', asyncHandler(async (req, res) => {
  logger.info('Acceso a página Acerca de');
  const pageData = {
    title: `Acerca de ${config.appName}`,
    description: `Conoce más sobre ${config.appName} y nuestra visión`,
    currentYear: new Date().getFullYear(),
    config: config, // Añadir objeto config completo
    timeline: [
      {
        year: '2024',
        title: 'Conceptualización',
        description: 'Inicio del proyecto CoreDesk con visión de plataforma integral'
      },
      {
        year: '2025',
        title: 'Desarrollo Actual',
        description: 'Implementación de módulos base y sistema de asistentes virtuales MCP'
      },
      {
        year: '2025 Q2',
        title: 'Lanzamiento Beta',
        description: 'Primera versión beta con funcionalidades principales'
      },
      {        year: '2025 Q4',
        title: 'Versión Completa',
        description: 'Lanzamiento oficial con todas las funcionalidades'
      }
    ]
  };
  
  res.render('about', pageData);
}));

// Página de Contacto
router.get('/contact', asyncHandler(async (req, res) => {
  logger.info('Acceso a página de contacto');
  const pageData = {
    title: 'Contacto - CoreDesk',
    description: 'Ponte en contacto con el equipo de CoreDesk',
    currentYear: new Date().getFullYear(),
    config: config // Añadir objeto config completo
  };
  
  res.render('contact', pageData);
}));

// Endpoint para suscripción a newsletter
router.post('/newsletter', asyncHandler(async (req, res) => {
  const { email, name } = req.body;
  
  if (!email || !email.includes('@')) {
    return res.status(400).json({
      success: false,
      message: 'Email válido es requerido'
    });
  }
  
  // TODO: Integrar con servicio de email marketing
  logger.info(`Nueva suscripción al newsletter: ${email} (${name || 'Sin nombre'})`);
  
  res.json({
    success: true,
    message: 'Gracias por suscribirte. Te mantendremos informado sobre las novedades de CoreDesk.'
  });
}));

// Endpoint para formulario de contacto con protección anti-spam
router.post('/contact', 
  suspiciousIPHandler,
  contactFormLimiter,
  honeypotValidation,
  timingValidation,
  advancedValidation,
  asyncHandler(async (req, res) => {
    const { name, email, subject, message, newsletter } = req.body;
    
    // Validación final de campos requeridos (redundante pero segura)
    if (!name || !email || !message) {
      return res.status(400).json({
        success: false,
        message: 'Nombre, email y mensaje son requeridos'
      });
    }
      // Log con información adicional para monitoreo
    logger.info(`✉️ Nuevo mensaje de contacto válido`, {
      from: `${name} (${email})`,
      subject: subject || 'Sin asunto',
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      newsletter: newsletter ? 'Sí' : 'No',
      messageLength: message.length,
      timestamp: new Date().toISOString()
    });
    
    // Enviar notificación por email al equipo
    const emailResult = await emailService.sendContactFormNotification({
      name,
      email,
      subject: subject || 'Consulta general',
      message,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });

    // Enviar confirmación al remitente (opcional)
    if (process.env.SEND_CONFIRMATION_EMAIL === 'true') {
      await emailService.sendContactConfirmation({
        name,
        email,
        subject: subject || 'Consulta general'
      });
    }
    
    // TODO: Guardar en base de datos para seguimiento
    // await ContactMessage.create({ name, email, subject, message, ip: req.ip });
    
    // Respuesta basada en si el email se envió exitosamente
    const responseMessage = emailResult.success 
      ? 'Mensaje enviado exitosamente. Te contactaremos pronto.'
      : 'Mensaje recibido y registrado. Te contactaremos pronto.';
    
    res.json({
      success: true,
      message: responseMessage,
      emailSent: emailResult.success
    });
  })
);

// Sitemap XML
router.get('/sitemap.xml', (req, res) => {
  const baseUrl = `${req.protocol}://${req.get('host')}`;
  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>${baseUrl}/</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>1.0</priority>
  </url>
  <url>
    <loc>${baseUrl}/about</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.8</priority>
  </url>
  <url>
    <loc>${baseUrl}/download</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.9</priority>
  </url>
  <url>
    <loc>${baseUrl}/updates</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.7</priority>
  </url>
  <url>
    <loc>${baseUrl}/contact</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.6</priority>
  </url>
</urlset>`;
  
  res.set('Content-Type', 'text/xml');
  res.send(sitemap);
});

// Robots.txt
router.get('/robots.txt', (req, res) => {
  const robots = `User-agent: *
Allow: /
Disallow: /api/
Disallow: /downloads/
Disallow: /logs/

Sitemap: ${req.protocol}://${req.get('host')}/sitemap.xml`;
  
  res.set('Content-Type', 'text/plain');
  res.send(robots);
});

// Página de Descargas - REMOVIDA: Ahora se maneja en routes/download.js

// Página de Actualizaciones
router.get('/updates', asyncHandler(async (req, res) => {
  logger.info('Acceso a página de actualizaciones');
    const pageData = {
    title: 'Actualizaciones - CoreDesk',
    description: 'Historial de versiones y novedades de CoreDesk',
    currentYear: new Date().getFullYear(),
    config: config, // Añadir objeto config completo
    releases: [
      {
        version: '0.0.2-beta',
        date: '2025-06-09',
        type: 'beta',
        changes: [
          'Lanzamiento inicial de la versión beta',
          'Implementación del sistema de asistentes virtuales MCP',
          'Módulos base: LexFlow, ProtocolX, AuditPro, FinSync',
          'Interfaz de usuario rediseñada'
        ]
      },
      {
        version: '0.9.0-alpha',
        date: '2025-05-15',
        type: 'alpha',
        changes: [
          'Versión alpha con funcionalidades principales',          'Sistema de autenticación implementado',
          'Base de datos y arquitectura Docker'
        ]
      }
    ],
    config: {
      appName: config.appName,
      appDescription: config.appDescription
    }
  };
  
  res.render('updates', pageData);
}));


// ===== ENDPOINTS DE ADMINISTRACIÓN ANTI-SPAM =====

// Endpoint para bloquear/desbloquear IPs (solo para administradores)
router.post('/admin/spam/block-ip', asyncHandler(async (req, res) => {
  const { ip, action } = req.body; // action: 'block' o 'unblock'
  
  // TODO: Agregar autenticación de administrador
  const { blockIP, unblockIP } = require('../middleware/antiSpam');
  
  if (action === 'block') {
    blockIP(ip);
    logger.warn(`🚫 IP bloqueada manualmente: ${ip}`);
    res.json({ success: true, message: `IP ${ip} bloqueada exitosamente` });
  } else if (action === 'unblock') {
    unblockIP(ip);
    logger.info(`✅ IP desbloqueada manualmente: ${ip}`);
    res.json({ success: true, message: `IP ${ip} desbloqueada exitosamente` });
  } else {
    res.status(400).json({ success: false, message: 'Acción no válida' });
  }
}));

// Endpoint para obtener estadísticas de spam
router.get('/admin/spam/stats', asyncHandler(async (req, res) => {
  // TODO: Agregar autenticación de administrador
  
  res.json({
    success: true,
    stats: {
      blockedIPs: 0, // En producción, obtener de base de datos
      suspiciousIPs: 0,
      totalContactMessages: 0,
      spamBlocked: 0,
      lastUpdate: new Date().toISOString()
    }
  });
}));

// Endpoint para verificar configuración de correo (solo administradores)
router.get('/admin/email/status', asyncHandler(async (req, res) => {
  // TODO: Agregar autenticación de administrador
  
  const config = emailService.getConfig();
  const isConfigured = config.configured;
  
  res.json({
    success: true,
    status: isConfigured ? 'configured' : 'not_configured',
    message: isConfigured ? 'Configuración de correo válida' : 'Correo no configurado',
    emailConfig: config,
    timestamp: new Date().toISOString()
  });
}));

router.get('/admin/email/config', asyncHandler(async (req, res) => {
  // TODO: Agregar autenticación de administrador
  
  const config = emailService.getConfig();
  
  res.json({
    success: true,
    emailConfig: config,
    timestamp: new Date().toISOString()
  });
}));

// Endpoint para mostrar página de prueba de correo
router.get('/admin/email/test', asyncHandler(async (req, res) => {
  // TODO: Agregar autenticación de administrador
  
  const config = emailService.getConfig();
  
  if (!config.configured) {
    return res.status(400).json({
      success: false,
      message: 'Correo no configurado. Configure las variables SMTP primero.',
      emailConfig: config
    });
  }
  
  res.json({
    success: true,
    message: 'Sistema listo para pruebas de correo',
    instructions: 'Envíe una petición POST a esta misma URL con { "testEmail": "<EMAIL>" }',
    currentConfig: {
      host: config.host,
      port: config.port,
      secure: config.secure,
      recipient: config.contactRecipient
    },
    timestamp: new Date().toISOString()
  });
}));

// Endpoint para probar envío de correo
router.post('/admin/email/test', asyncHandler(async (req, res) => {
  // TODO: Agregar autenticación de administrador
  
  const { testEmail } = req.body;
  
  if (!testEmail) {
    return res.status(400).json({
      success: false,
      message: 'Email de prueba requerido'
    });
  }
  
  try {
    const result = await emailService.sendContactFormNotification({
      name: 'Test User',
      email: testEmail,
      subject: 'Prueba de configuración de correo',
      message: 'Este es un mensaje de prueba para verificar la configuración del sistema de correo.',
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });
    
    res.json({
      success: result.success,
      message: result.success ? 'Email de prueba enviado exitosamente' : 'Error enviando email de prueba',
      details: result
    });
    
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error interno enviando email de prueba',
      error: error.message
    });
  }
}));

module.exports = router;
