const path = require('path');
const fs = require('fs').promises;
const crypto = require('crypto');
const config = require('../config');
const logger = require('../middleware/logger');
const updateService = require('./updateService');

class DownloadService {
  constructor() {
    this.downloadStats = new Map();
    this.platformMappings = {
      'windows': ['win32', 'windows', 'win'],
      'macos': ['darwin', 'mac', 'osx', 'macos'],
      'linux': ['linux', 'ubuntu', 'debian', 'centos', 'fedora']
    };
  }

  // Obtener versiones disponibles para descarga
  async getAvailableReleases() {
    try {
      const releases = await updateService.getAllReleases({ limit: 20 });
      return releases.data.filter(release => !release.prerelease);
    } catch (error) {
      logger.error('Error al obtener releases disponibles:', error);
      return [];
    }
  }

  // Obtener información de una versión específica
  async getReleaseInfo(version) {
    try {
      return await updateService.getReleaseInfo(version);
    } catch (error) {
      logger.error(`Error al obtener info de release ${version}:`, error);
      return null;
    }
  }

  // Obtener última versión disponible
  async getLatestVersion() {
    try {
      return await updateService.getLatestVersion();
    } catch (error) {
      logger.error('Error al obtener última versión:', error);
      return null;
    }
  }

  // Obtener URL de descarga para plataforma y versión específica
  async getDownloadUrl(platform, version, architecture = 'x64') {
    try {
      const normalizedPlatform = this.normalizePlatform(platform);
      logger.info(`Simple download request: ${normalizedPlatform} v${version}`);
      
      // Simple direct mapping to working static URLs
      const downloadMap = {
        'windows': {
          '0.0.2': 'https://coredeskpro.com/downloads/app/CoreDesk-0.0.2-win.exe'
        },
        'macos': {
          '0.0.2': 'https://coredeskpro.com/downloads/app/CoreDesk-0.0.2-mac.zip'
        },
        'linux': {
          '0.0.2': 'https://coredeskpro.com/downloads/app/CoreDesk-0.0.2-linux.AppImage'
        }
      };
      
      const platformMap = downloadMap[normalizedPlatform];
      if (platformMap && platformMap[version]) {
        const url = platformMap[version];
        const filename = url.split('/').pop();
        
        logger.info(`✅ Returning direct URL: ${url}`);
        return {
          type: 'external',
          url: url,
          filename: filename,
          size: 0
        };
      }
      
      logger.error(`❌ No download mapping found for ${normalizedPlatform} v${version}`);
      return null;
    } catch (error) {
      logger.error(`Error al obtener URL de descarga ${platform} v${version}:`, error);
      return null;
    }
  }

  // Obtener URLs de descarga para todas las plataformas de una versión
  async getDownloadUrls(version) {
    try {
      const platforms = ['windows', 'macos', 'linux'];
      const urls = {};
      
      for (const platform of platforms) {
        const downloadInfo = await this.getDownloadUrl(platform, version);
        if (downloadInfo) {
          urls[platform] = downloadInfo;
        }
      }
      
      return urls;
    } catch (error) {
      logger.error(`Error al obtener URLs de descarga para v${version}:`, error);
      return {};
    }
  }

  // Obtener checksums de archivos
  async getFileChecksums(platform, version) {
    try {
      // TODO: Implementar generación/almacenamiento real de checksums
      const releaseInfo = await this.getReleaseInfo(version);
      
      if (!releaseInfo || !releaseInfo.assets) {
        return null;
      }

      const checksums = {};
      
      for (const asset of releaseInfo.assets) {
        if (!platform || this.assetMatchesPlatform(asset.name, platform)) {
          // Generar checksum simulado (en producción, esto se calcularía del archivo real)
          checksums[asset.name] = {
            sha256: this.generateSimulatedChecksum(asset.name, version),
            md5: this.generateSimulatedChecksum(asset.name + version, 'md5'),
            size: asset.size
          };
        }
      }
      
      return checksums;
    } catch (error) {
      logger.error(`Error al obtener checksums ${platform} v${version}:`, error);
      return null;
    }
  }

  // Registrar descarga
  async logDownload(downloadInfo) {
    try {
      const logEntry = {
        ...downloadInfo,
        id: Date.now().toString(),
        createdAt: new Date()
      };
      
      // TODO: Almacenar en base de datos real
      logger.info('Descarga registrada:', logEntry);
      
      // Actualizar estadísticas en memoria
      const key = `${downloadInfo.platform}_${downloadInfo.version}`;
      if (!this.downloadStats.has(key)) {
        this.downloadStats.set(key, 0);
      }
      this.downloadStats.set(key, this.downloadStats.get(key) + 1);
      
      return logEntry;
    } catch (error) {
      logger.error('Error al registrar descarga:', error);
      throw error;
    }
  }

  // Obtener estadísticas de descarga
  async getDownloadStats() {
    try {
      // TODO: Implementar estadísticas reales desde base de datos
      const stats = {
        totalDownloads: Array.from(this.downloadStats.values()).reduce((a, b) => a + b, 0) + 2585,
        downloadsByPlatform: {
          windows: 1679,
          macos: 646,
          linux: 260
        },
        downloadsByVersion: {
          '0.0.2': 2585,
          '0.9.8': 892,
          '0.9.7': 445
        },
        recentDownloads: {
          lastHour: 12,
          lastDay: 89,
          lastWeek: 445,
          lastMonth: 1567
        },
        topCountries: [
          { country: 'México', downloads: 567 },
          { country: 'España', downloads: 445 },
          { country: 'Argentina', downloads: 334 },
          { country: 'Colombia', downloads: 289 },
          { country: 'Chile', downloads: 223 }
        ]
      };
      
      return stats;
    } catch (error) {
      logger.error('Error al obtener estadísticas de descarga:', error);
      throw error;
    }
  }

  // Normalizar nombre de plataforma
  normalizePlatform(platform) {
    const normalized = platform.toLowerCase();
    
    for (const [key, variations] of Object.entries(this.platformMappings)) {
      if (variations.includes(normalized)) {
        return key;
      }
    }
    
    return normalized;
  }

  // Get production filename for a platform and version
  getProductionFileName(normalizedPlatform, version) {
    switch (normalizedPlatform) {
      case 'windows':
        return `CoreDesk-${version}-win.exe`;
      case 'macos':
        return `CoreDesk-${version}-mac.zip`;
      case 'linux':
        return `CoreDesk-${version}-linux.AppImage`;
      default:
        return null;
    }
  }

  // Encontrar asset que coincida con plataforma y arquitectura
  findMatchingAsset(assets, platform, architecture) {
    if (!assets || !Array.isArray(assets)) {
      return null;
    }

    // First try exact platform match with our naming convention
    const exactMatch = assets.find(asset => {
      const name = asset.name.toLowerCase();
      switch (platform) {
        case 'windows':
          return name.includes('-win.') && name.includes('.exe');
        case 'macos':
          return name.includes('-mac.') && (name.includes('.zip') || name.includes('.tar.gz'));
        case 'linux':
          return name.includes('-linux.') && name.includes('.appimage');
        default:
          return false;
      }
    });

    if (exactMatch) {
      return exactMatch;
    }

    // Fallback to legacy matching
    return assets.find(asset => {
      const name = asset.name.toLowerCase();
      const hasplatform = this.assetMatchesPlatform(name, platform);
      
      // For production files, be more flexible with architecture matching
      const hasArchitecture = name.includes(architecture.toLowerCase()) || 
                             (architecture === 'x64' && name.includes('64')) ||
                             (architecture === 'x86' && name.includes('32')) ||
                             // Default to true for files that match platform
                             hasplatform;
      
      return hasplatform && hasArchitecture;
    });
  }

  // Verificar si un asset coincide con una plataforma
  assetMatchesPlatform(assetName, platform) {
    const name = assetName.toLowerCase();
    const platformVariations = this.platformMappings[platform] || [platform];
    
    return platformVariations.some(variation => name.includes(variation));
  }

  // Generar checksum simulado para desarrollo
  generateSimulatedChecksum(input, algorithm = 'sha256') {
    return crypto
      .createHash(algorithm)
      .update(input + config.sessionSecret)
      .digest('hex');
  }

  // Verificar disponibilidad de archivo local
  async checkFileAvailability(filename) {
    try {
      const filePath = path.join(config.downloadPath, filename);
      await fs.access(filePath);
      const stats = await fs.stat(filePath);
      
      return {
        available: true,
        size: stats.size,
        lastModified: stats.mtime
      };
    } catch {
      return {
        available: false
      };
    }
  }

  // Obtener información del archivo para descarga
  async getFileInfo(filename) {
    try {
      const availability = await this.checkFileAvailability(filename);
      
      if (!availability.available) {
        return null;
      }
      
      const filePath = path.join(config.downloadPath, filename);
      
      return {
        filename,
        path: filePath,
        size: availability.size,
        lastModified: availability.lastModified,
        mimeType: this.getMimeType(filename)
      };
    } catch (error) {
      logger.error(`Error al obtener info de archivo ${filename}:`, error);
      return null;
    }
  }

  // Obtener MIME type basado en extensión
  getMimeType(filename) {
    const extension = path.extname(filename).toLowerCase();
    
    const mimeTypes = {
      '.exe': 'application/vnd.microsoft.portable-executable',
      '.dmg': 'application/x-apple-diskimage',
      '.appimage': 'application/x-executable',
      '.deb': 'application/vnd.debian.binary-package',
      '.rpm': 'application/x-rpm',
      '.zip': 'application/zip',
      '.tar.gz': 'application/gzip'
    };
    
    return mimeTypes[extension] || 'application/octet-stream';
  }
}

module.exports = new DownloadService();
