const fs = require('fs').promises;
const yaml = require('js-yaml');
const path = require('path');
const config = require('../config');
const logger = require('../middleware/logger');

class VersionService {
  constructor() {
    this.cache = null;
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
    this.lastCacheTime = 0;
  }

  // Get version info from latest.yml with fallback to hardcoded values
  async getVersionInfo() {
    try {
      // Return cached data if still valid
      if (this.cache && (Date.now() - this.lastCacheTime) < this.cacheTimeout) {
        return this.cache;
      }

      // Try to read latest.yml from mounted volume
      const ymlPaths = [
        path.join(config.downloadPath, 'app', 'latest.yml'),
        path.join(config.downloadPath, 'latest.yml')
      ];

      let versionData = null;
      
      for (const ymlPath of ymlPaths) {
        try {
          logger.info(`Attempting to read version info from: ${ymlPath}`);
          const ymlContent = await fs.readFile(ymlPath, 'utf8');
          versionData = yaml.load(ymlContent);
          logger.info(`Successfully loaded version info from: ${ymlPath}`);
          break;
        } catch (error) {
          logger.warn(`Failed to read ${ymlPath}: ${error.message}`);
          continue;
        }
      }

      if (versionData) {
        // Parse dynamic data from YAML
        const dynamicInfo = this.parseVersionData(versionData);
        this.cache = dynamicInfo;
        this.lastCacheTime = Date.now();
        logger.info(`Using dynamic version info: v${dynamicInfo.version}`);
        return dynamicInfo;
      } else {
        // Fallback to hardcoded values
        const fallbackInfo = this.getFallbackVersionInfo();
        logger.warn('Using fallback hardcoded version info');
        return fallbackInfo;
      }
    } catch (error) {
      logger.error('Error getting version info:', error);
      return this.getFallbackVersionInfo();
    }
  }

  // Parse YAML data into standardized format
  parseVersionData(versionData) {
    const platforms = {};
    
    // Convert platforms (remove 'v' prefix from vwindows, vmacos, vlinux)
    Object.keys(versionData).forEach(key => {
      if (key.startsWith('v') && typeof versionData[key] === 'object') {
        const platformName = key.substring(1); // Remove 'v' prefix
        const platformData = versionData[key];
        
        platforms[platformName] = {
          url: `https://coredeskpro.com/downloads/app/${platformData.url}`,
          filename: platformData.url,
          size: platformData.size,
          sizeFormatted: this.formatFileSize(platformData.size),
          sha512: platformData.sha512
        };
      }
    });

    return {
      version: versionData.version,
      releaseDate: versionData.releaseDate ? new Date(versionData.releaseDate) : new Date(),
      platforms,
      source: 'dynamic'
    };
  }

  // Fallback hardcoded values (keeps current functionality working)
  getFallbackVersionInfo() {
    return {
      version: '0.0.2',
      releaseDate: new Date('2025-07-06'),
      platforms: {
        windows: {
          url: 'https://coredeskpro.com/downloads/app/CoreDesk-0.0.2-win.exe',
          filename: 'CoreDesk-0.0.2-win.exe',
          size: 92212002,
          sizeFormatted: '~88 MB',
          sha512: null
        },
        macos: {
          url: 'https://coredeskpro.com/downloads/app/CoreDesk-0.0.2-mac.tar.gz',
          filename: 'CoreDesk-0.0.2-mac.tar.gz',
          size: 126391687,
          sizeFormatted: '~121 MB',
          sha512: null
        },
        linux: {
          url: 'https://coredeskpro.com/downloads/app/CoreDesk-0.0.2-linux.AppImage',
          filename: 'CoreDesk-0.0.2-linux.AppImage',
          size: 129319507,
          sizeFormatted: '~124 MB',
          sha512: null
        }
      },
      source: 'fallback'
    };
  }

  // Get download URL for a specific platform (backwards compatible)
  async getDownloadUrl(platform) {
    const versionInfo = await this.getVersionInfo();
    const platformData = versionInfo.platforms[platform.toLowerCase()];
    
    if (platformData) {
      return {
        type: 'external',
        url: platformData.url,
        filename: platformData.filename,
        size: platformData.size,
        version: versionInfo.version
      };
    }
    
    return null;
  }

  // Format file size in MB
  formatFileSize(bytes) {
    if (!bytes) return '~0 MB';
    const mb = bytes / (1024 * 1024);
    return `~${Math.round(mb)} MB`;
  }

  // Clear cache (useful for testing)
  clearCache() {
    this.cache = null;
    this.lastCacheTime = 0;
  }
}

module.exports = new VersionService();