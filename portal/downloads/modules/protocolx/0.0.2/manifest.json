{"id": "protocolx", "name": "ProtocolX", "version": "0.0.2", "description": "Administración de protocolo digital y gestión notarial", "author": "CoreDesk Team", "license": "Proprietary", "category": "notarial", "tags": ["notarial", "protocol", "documents", "digital-signature"], "main": "ProtocolXModule.js", "package": "ProtocolXPackage.js", "style": "protocolx.css", "install": "install.js", "dependencies": {"coredesk": ">=0.0.2"}, "requiredPermissions": ["storage.read", "storage.write", "ui.tabs", "ui.panels"], "requiredLicense": "professional", "engines": {"coredesk": ">=0.0.2"}, "homepage": "https://coredeskpro.com/modules/protocolx", "repository": {"type": "git", "url": "https://github.com/coredesk/modules/protocolx"}, "packageFile": "protocolx-0.0.2.tar.gz", "downloadCount": 0, "lastUpdated": "2025-07-09T23:39:32.766Z", "compatibility": {"coredesk": ">=0.0.2", "platforms": ["windows", "macos", "linux"]}, "coredesk": {"moduleType": "ui", "activationMethod": "lazy", "supportedThemes": ["dark", "light"], "minScreenWidth": 1024, "moduleIcon": "🔧", "moduleColor": "#059669"}, "files": ["ProtocolXModule.js", "ProtocolXPackage.js", "protocolx.css", "install.js", "package.json"], "checksum": "sha256:83a034d3584250a4c60616d764739ee5c4872d418ae646338e34b93ce377677e", "size": 16886}