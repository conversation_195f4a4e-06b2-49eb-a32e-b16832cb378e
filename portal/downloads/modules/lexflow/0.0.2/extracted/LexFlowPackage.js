/**
 * LexFlow Module Package - Dynamic Module Implementation
 * Legal Case Management System for CoreDesk Framework v2.0.0
 */

class LexFlowPackage extends window.ModulePackage {
    constructor(packageData, options = {}) {
        super(packageData, options);
        
        // LexFlow-specific properties
        this.moduleDescription = 'Gestión completa de casos legales y documentación jurídica';
        this.currentView = 'dashboard';
        this.selectedCase = null;
        
        // UI Components
        this.container = null;
        this.sidebar = null;
        this.mainContent = null;
        
        // Data Management
        this.cases = [];
        this.documents = [];
        this.events = [];
        this.notes = [];
        
        // UI State
        this.searchQuery = '';
        this.filterStatus = 'all';
        this.filterPriority = 'all';
    }

    /**
     * Initialize the LexFlow package
     */
    async initialize() {
        try {
            console.log('Module', '[LexFlow Package] Initializing...');
            
            // Initialize base module package (no super method)
            // ModulePackage doesn't have an async initialize method
            
            // Load LexFlow-specific data
            await this.loadData();
            
            // Create UI structure
            this.createUIStructure();
            this.updateCounts();
            this.setupEventListeners();
            
            console.log('Module', '[LexFlow Package] Initialized successfully');
            
        } catch (error) {
            console.error('Module', '[LexFlow Package] Initialization failed:', error);
            throw error;
        }
    }

    /**
     * Activate the LexFlow module
     */
    async activate() {
        try {
            console.log('Module', '[LexFlow Package] Activating...');
            
            // Base ModulePackage doesn't have activate method
            // This is handled by the dynamic module manager
            
            // Ensure container exists
            if (!this.container) {
                console.log('Module', '[LexFlow Package] Creating UI structure...');
                this.createUIStructure();
                this.setupEventListeners();
            }
            
            // Show module container
            if (this.container) {
                this.container.style.display = 'flex';
                console.log('Module', '[LexFlow Package] Container made visible');
            }
            
            // Refresh data
            await this.refreshData();
            
            // Set initial view
            this.showView('dashboard');
            
            // Update UI components
            this.updateActivityBar();
            this.updateStatusBar();
            
            console.log('Module', '[LexFlow Package] Activated successfully');
            
        } catch (error) {
            console.error('Module', '[LexFlow Package] Activation failed:', error);
            throw error;
        }
    }

    /**
     * Deactivate the LexFlow module
     */
    async deactivate() {
        try {
            console.log('Module', '[LexFlow Package] Deactivating...');
            
            // Save current state
            await this.saveState();
            
            // Hide container
            if (this.container) {
                this.container.style.display = 'none';
            }
            
            // Base ModulePackage doesn't have deactivate method
            // This is handled by the dynamic module manager
            
            console.log('Module', '[LexFlow Package] Deactivated successfully');
            
        } catch (error) {
            console.error('Module', '[LexFlow Package] Deactivation failed:', error);
            throw error;
        }
    }

    /**
     * Save module state
     */
    async saveState() {
        try {
            const state = {
                currentView: this.currentView,
                selectedCase: this.selectedCase,
                searchQuery: this.searchQuery,
                filterStatus: this.filterStatus,
                filterPriority: this.filterPriority,
                timestamp: new Date().toISOString()
            };
            
            localStorage.setItem(`lexflow_state`, JSON.stringify(state));
            console.log('Module', '[LexFlow Package] State saved');
            
        } catch (error) {
            console.error('Module', '[LexFlow Package] Failed to save state:', error);
        }
    }

    /**
     * Load module state
     */
    async loadState() {
        try {
            const savedState = localStorage.getItem(`lexflow_state`);
            if (savedState) {
                const state = JSON.parse(savedState);
                this.currentView = state.currentView || 'dashboard';
                this.selectedCase = state.selectedCase;
                this.searchQuery = state.searchQuery || '';
                this.filterStatus = state.filterStatus || 'all';
                this.filterPriority = state.filterPriority || 'all';
                
                console.log('Module', '[LexFlow Package] State loaded');
            }
        } catch (error) {
            console.error('Module', '[LexFlow Package] Failed to load state:', error);
        }
    }

    /**
     * Load LexFlow data
     */
    async loadData() {
        try {
            // Load saved cases, documents, events, and notes
            const savedCases = localStorage.getItem('lexflow_cases');
            const savedDocuments = localStorage.getItem('lexflow_documents');
            const savedEvents = localStorage.getItem('lexflow_events');
            const savedNotes = localStorage.getItem('lexflow_notes');
            
            this.cases = savedCases ? JSON.parse(savedCases) : this.getDefaultCases();
            this.documents = savedDocuments ? JSON.parse(savedDocuments) : [];
            this.events = savedEvents ? JSON.parse(savedEvents) : [];
            this.notes = savedNotes ? JSON.parse(savedNotes) : [];
            
            console.log('Module', `[LexFlow Package] Loaded ${this.cases.length} cases, ${this.documents.length} documents`);
            
        } catch (error) {
            console.error('Module', '[LexFlow Package] Error loading data:', error);
            // Use default data on error
            this.cases = this.getDefaultCases();
            this.documents = [];
            this.events = [];
            this.notes = [];
        }
    }

    /**
     * Get default sample cases for demo
     */
    getDefaultCases() {
        return [
            {
                id: 'case_001',
                number: 'LEX-2024-001',
                title: 'Demanda Civil por Incumplimiento Contractual',
                client: 'Empresa ABC S.A.',
                status: 'active',
                priority: 'high',
                category: 'civil',
                createdDate: '2024-01-15',
                dueDate: '2024-06-15',
                assignedLawyer: 'Dr. María González',
                description: 'Demanda civil por incumplimiento de contrato de servicios.',
                value: 150000,
                documents: ['contract_abc.pdf', 'complaint_draft.docx'],
                events: ['initial_meeting', 'document_review'],
                notes: 2
            },
            {
                id: 'case_002',
                number: 'LEX-2024-002',
                title: 'Defensa Penal - Delito Fiscal',
                client: 'Juan Pérez López',
                status: 'active',
                priority: 'urgent',
                category: 'penal',
                createdDate: '2024-02-01',
                dueDate: '2024-04-30',
                assignedLawyer: 'Dr. Carlos Ruiz',
                description: 'Defensa en proceso penal por presunto delito fiscal.',
                value: 80000,
                documents: ['tax_records.xlsx', 'defense_strategy.docx'],
                events: ['court_hearing', 'client_meeting'],
                notes: 5
            },
            {
                id: 'case_003',
                number: 'LEX-2024-003',
                title: 'Consultoría Legal Empresarial',
                client: 'Corporación XYZ',
                status: 'completed',
                priority: 'medium',
                category: 'corporativo',
                createdDate: '2024-01-10',
                dueDate: '2024-03-10',
                assignedLawyer: 'Dra. Ana Martínez',
                description: 'Asesoría legal para restructuración corporativa.',
                value: 200000,
                documents: ['corporate_structure.pdf', 'legal_opinion.docx'],
                events: ['board_meeting', 'document_signing'],
                notes: 3
            }
        ];
    }

    /**
     * Create the UI structure
     */
    createUIStructure() {
        // Create main module container
        this.container = document.createElement('div');
        this.container.className = 'lexflow-module';
        this.container.innerHTML = this.getModuleHTML();
        
        // Find the tab content area and append
        const tabContent = document.querySelector('.tab-content');
        if (tabContent) {
            tabContent.appendChild(this.container);
        } else {
            // Fallback: append to body
            document.body.appendChild(this.container);
        }
        
        // Initially hide the container - it will be shown when activated
        this.container.style.display = 'none';
        
        // Store references to key elements
        this.sidebar = this.container.querySelector('.lexflow-sidebar');
        this.mainContent = this.container.querySelector('.lexflow-main-content');
        
        console.log('Module', '[LexFlow Package] UI structure created');
    }

    /**
     * Get the main module HTML structure
     */
    getModuleHTML() {
        return `
            <div class="lexflow-layout">
                <div class="lexflow-sidebar">
                    <div class="sidebar-header">
                        <h2>LexFlow</h2>
                        <p class="module-description">Gestión de Casos Legales</p>
                    </div>
                    
                    <nav class="sidebar-nav">
                        <a href="#" class="nav-item active" data-view="dashboard">
                            <span class="nav-icon">📊</span>
                            <span class="nav-text">Dashboard</span>
                        </a>
                        <a href="#" class="nav-item" data-view="cases">
                            <span class="nav-icon">⚖️</span>
                            <span class="nav-text">Casos</span>
                            <span class="nav-count" id="cases-count">0</span>
                        </a>
                        <a href="#" class="nav-item" data-view="documents">
                            <span class="nav-icon">📄</span>
                            <span class="nav-text">Documentos</span>
                            <span class="nav-count" id="documents-count">0</span>
                        </a>
                        <a href="#" class="nav-item" data-view="events">
                            <span class="nav-icon">📅</span>
                            <span class="nav-text">Eventos</span>
                            <span class="nav-count" id="events-count">0</span>
                        </a>
                    </nav>
                </div>
                
                <div class="lexflow-main-content">
                    <div id="lexflow-content">
                        <!-- Dynamic content will be inserted here -->
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        if (!this.container) return;
        
        // Navigation event listeners
        const navItems = this.container.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const view = e.currentTarget.dataset.view;
                if (view) {
                    this.showView(view);
                }
            });
        });
    }

    /**
     * Show a specific view
     */
    showView(viewName) {
        this.currentView = viewName;
        
        // Update navigation
        const navItems = this.container.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            item.classList.toggle('active', item.dataset.view === viewName);
        });
        
        // Update content
        const contentArea = this.container.querySelector('#lexflow-content');
        if (contentArea) {
            contentArea.innerHTML = this.getViewContent(viewName);
            this.setupViewEventListeners(viewName);
        }
        
        console.log('Module', `[LexFlow Package] Switched to view: ${viewName}`);
    }

    /**
     * Get content for a specific view
     */
    getViewContent(viewName) {
        switch (viewName) {
            case 'dashboard':
                return this.getDashboardContent();
            case 'cases':
                return this.getCasesContent();
            case 'documents':
                return this.getDocumentsContent();
            case 'events':
                return this.getEventsContent();
            default:
                return '<div class="view-content"><h2>Vista no encontrada</h2></div>';
        }
    }

    /**
     * Get dashboard content
     */
    getDashboardContent() {
        const activeCases = this.cases.filter(c => c.status === 'active').length;
        const urgentCases = this.cases.filter(c => c.priority === 'urgent').length;
        const totalValue = this.cases.reduce((sum, c) => sum + (c.value || 0), 0);
        
        return `
            <div class="view-content dashboard-view">
                <div class="dashboard-header">
                    <h1>Dashboard LexFlow</h1>
                    <p>Resumen de actividad legal</p>
                </div>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">⚖️</div>
                        <div class="stat-content">
                            <h3>${this.cases.length}</h3>
                            <p>Total Casos</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">🔥</div>
                        <div class="stat-content">
                            <h3>${activeCases}</h3>
                            <p>Casos Activos</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">⚠️</div>
                        <div class="stat-content">
                            <h3>${urgentCases}</h3>
                            <p>Casos Urgentes</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">💰</div>
                        <div class="stat-content">
                            <h3>$${totalValue.toLocaleString()}</h3>
                            <p>Valor Total</p>
                        </div>
                    </div>
                </div>
                
                <div class="recent-activity">
                    <h2>Casos Recientes</h2>
                    <div class="cases-list">
                        ${this.cases.slice(0, 5).map(case_ => `
                            <div class="case-item" data-case-id="${case_.id}">
                                <div class="case-info">
                                    <h4>${case_.title}</h4>
                                    <p>${case_.client}</p>
                                </div>
                                <div class="case-status">
                                    <span class="status-badge status-${case_.status}">${case_.status}</span>
                                    <span class="priority-badge priority-${case_.priority}">${case_.priority}</span>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Get cases content
     */
    getCasesContent() {
        return `
            <div class="view-content cases-view">
                <div class="view-header">
                    <h1>Gestión de Casos</h1>
                    <button class="btn btn-primary" id="new-case-btn">Nuevo Caso</button>
                </div>
                
                <div class="filters-section">
                    <div class="search-box">
                        <input type="text" placeholder="Buscar casos..." id="case-search" value="${this.searchQuery}">
                    </div>
                    <div class="filter-controls">
                        <select id="status-filter">
                            <option value="all">Todos los estados</option>
                            <option value="active">Activos</option>
                            <option value="completed">Completados</option>
                            <option value="pending">Pendientes</option>
                        </select>
                        <select id="priority-filter">
                            <option value="all">Todas las prioridades</option>
                            <option value="urgent">Urgente</option>
                            <option value="high">Alta</option>
                            <option value="medium">Media</option>
                            <option value="low">Baja</option>
                        </select>
                    </div>
                </div>
                
                <div class="cases-table">
                    <table>
                        <thead>
                            <tr>
                                <th>Número</th>
                                <th>Título</th>
                                <th>Cliente</th>
                                <th>Estado</th>
                                <th>Prioridad</th>
                                <th>Abogado</th>
                                <th>Fecha Límite</th>
                                <th>Acciones</th>
                            </tr>
                        </thead>
                        <tbody id="cases-table-body">
                            ${this.getCasesTableRows()}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    /**
     * Get cases table rows
     */
    getCasesTableRows() {
        return this.cases.map(case_ => `
            <tr data-case-id="${case_.id}">
                <td>${case_.number}</td>
                <td>${case_.title}</td>
                <td>${case_.client}</td>
                <td><span class="status-badge status-${case_.status}">${case_.status}</span></td>
                <td><span class="priority-badge priority-${case_.priority}">${case_.priority}</span></td>
                <td>${case_.assignedLawyer}</td>
                <td>${case_.dueDate}</td>
                <td>
                    <button class="btn btn-sm btn-secondary view-case-btn" data-case-id="${case_.id}">Ver</button>
                    <button class="btn btn-sm btn-primary edit-case-btn" data-case-id="${case_.id}">Editar</button>
                </td>
            </tr>
        `).join('');
    }

    /**
     * Get documents content
     */
    getDocumentsContent() {
        return `
            <div class="view-content documents-view">
                <div class="view-header">
                    <h1>Gestión de Documentos</h1>
                    <button class="btn btn-primary" id="upload-document-btn">Subir Documento</button>
                </div>
                
                <div class="documents-grid">
                    <div class="document-item">
                        <div class="document-icon">📄</div>
                        <div class="document-info">
                            <h4>Próximamente</h4>
                            <p>Funcionalidad de documentos en desarrollo</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Get events content
     */
    getEventsContent() {
        return `
            <div class="view-content events-view">
                <div class="view-header">
                    <h1>Calendario de Eventos</h1>
                    <button class="btn btn-primary" id="new-event-btn">Nuevo Evento</button>
                </div>
                
                <div class="calendar-container">
                    <div class="calendar-placeholder">
                        <div class="calendar-icon">📅</div>
                        <h3>Calendario de Eventos</h3>
                        <p>Funcionalidad de calendario en desarrollo</p>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Setup view-specific event listeners
     */
    setupViewEventListeners(viewName) {
        switch (viewName) {
            case 'cases':
                this.setupCasesEventListeners();
                break;
            case 'documents':
                this.setupDocumentsEventListeners();
                break;
            case 'events':
                this.setupEventsEventListeners();
                break;
        }
    }

    /**
     * Setup cases view event listeners
     */
    setupCasesEventListeners() {
        const container = this.container;
        
        // New case button
        const newCaseBtn = container.querySelector('#new-case-btn');
        if (newCaseBtn) {
            newCaseBtn.addEventListener('click', () => {
                this.showNewCaseModal();
            });
        }
        
        // Search input
        const searchInput = container.querySelector('#case-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchQuery = e.target.value;
                this.filterCases();
            });
        }
        
        // Filter selects
        const statusFilter = container.querySelector('#status-filter');
        const priorityFilter = container.querySelector('#priority-filter');
        
        if (statusFilter) {
            statusFilter.value = this.filterStatus;
            statusFilter.addEventListener('change', (e) => {
                this.filterStatus = e.target.value;
                this.filterCases();
            });
        }
        
        if (priorityFilter) {
            priorityFilter.value = this.filterPriority;
            priorityFilter.addEventListener('change', (e) => {
                this.filterPriority = e.target.value;
                this.filterCases();
            });
        }
    }

    /**
     * Setup documents view event listeners
     */
    setupDocumentsEventListeners() {
        // Placeholder for document functionality
    }

    /**
     * Setup events view event listeners
     */
    setupEventsEventListeners() {
        // Placeholder for events functionality
    }

    /**
     * Filter cases based on search and filter criteria
     */
    filterCases() {
        // This would filter cases and update the table
        // For now, just refresh the cases view
        this.showView('cases');
    }

    /**
     * Show new case modal
     */
    showNewCaseModal() {
        // Placeholder for new case modal
        alert('Funcionalidad de nuevo caso en desarrollo');
    }

    /**
     * Update counts in sidebar
     */
    updateCounts() {
        if (!this.container) return;
        
        const casesCount = this.container.querySelector('#cases-count');
        const documentsCount = this.container.querySelector('#documents-count');
        const eventsCount = this.container.querySelector('#events-count');
        
        if (casesCount) casesCount.textContent = this.cases.length;
        if (documentsCount) documentsCount.textContent = this.documents.length;
        if (eventsCount) eventsCount.textContent = this.events.length;
    }

    /**
     * Refresh data
     */
    async refreshData() {
        await this.loadData();
        this.updateCounts();
        
        // Refresh current view
        if (this.currentView) {
            this.showView(this.currentView);
        }
    }

    /**
     * Update activity bar
     */
    updateActivityBar() {
        if (window.activityBar) {
            window.activityBar.updateActiveModule(this.id);
        }
    }

    /**
     * Update status bar
     */
    updateStatusBar() {
        if (window.statusBar) {
            window.statusBar.updateModuleStatus(this.id);
        }
    }
}

// Export for module system
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LexFlowPackage;
}

// Make available globally for dynamic loading
window.LexFlowPackage = LexFlowPackage;

console.log('LexFlow Package', '[LexFlowPackage] Dynamic module package loaded successfully');