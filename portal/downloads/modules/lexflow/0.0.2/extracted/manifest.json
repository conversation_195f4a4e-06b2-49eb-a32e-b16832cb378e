{"id": "lexflow", "name": "LexFlow", "version": "0.0.2", "description": "Gestión completa de casos legales y documentación jurídica", "author": "CoreDesk Team", "license": "Proprietary", "category": "legal", "tags": ["legal", "case-management", "documents", "law"], "main": "LexFlowModule.js", "package": "LexFlowPackage.js", "style": "lexflow.css", "install": "install.js", "dependencies": {"coredesk": ">=0.0.2"}, "requiredPermissions": ["storage.read", "storage.write", "ui.tabs", "ui.panels"], "requiredLicense": "professional", "engines": {"coredesk": ">=0.0.2"}, "homepage": "https://coredeskpro.com/modules/lexflow", "repository": {"type": "git", "url": "https://github.com/coredesk/modules/lexflow"}, "packageFile": "lexflow-0.0.2.tar.gz", "downloadCount": 0, "lastUpdated": "2025-07-09T23:39:32.712Z", "compatibility": {"coredesk": ">=0.0.2", "platforms": ["windows", "macos", "linux"]}, "coredesk": {"moduleType": "ui", "activationMethod": "lazy", "supportedThemes": ["dark", "light"], "minScreenWidth": 1024, "moduleIcon": "⚖️", "moduleColor": "#1e3a8a"}, "files": ["LexFlowModule.js", "LexFlowPackage.js", "lexflow.css", "install.js", "package.json"]}