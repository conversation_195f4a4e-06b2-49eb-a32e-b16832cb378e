{"id": "finsync", "name": "FinSync", "version": "0.0.2", "description": "Módulo de contabilidad, finanzas y análisis de rentabilidad", "author": "CoreDesk Team", "license": "Proprietary", "category": "finance", "tags": ["finance", "accounting", "analysis", "cash-flow"], "main": "FinSyncModule.js", "package": "FinSyncPackage.js", "style": "finsync.css", "install": "install.js", "dependencies": {"coredesk": ">=0.0.2"}, "requiredPermissions": ["storage.read", "storage.write", "ui.tabs", "ui.panels"], "requiredLicense": "professional", "engines": {"coredesk": ">=0.0.2"}, "homepage": "https://coredeskpro.com/modules/finsync", "repository": {"type": "git", "url": "https://github.com/coredesk/modules/finsync"}, "packageFile": "finsync-0.0.2.tar.gz", "downloadCount": 0, "lastUpdated": "2025-07-09T23:50:58.526Z", "compatibility": {"coredesk": ">=0.0.2", "platforms": ["windows", "macos", "linux"]}, "coredesk": {"moduleType": "ui", "activationMethod": "lazy", "supportedThemes": ["dark", "light"], "minScreenWidth": 1024, "moduleIcon": "💰", "moduleColor": "#1e40af"}, "files": ["FinSyncModule.js", "FinSyncPackage.js", "finsync.css", "install.js", "package.json"], "checksum": "sha256:f3d4331676b703e1a3e9aa318ae763e161ba97c1dc1665bfc874977b7c04737b", "size": 9069}