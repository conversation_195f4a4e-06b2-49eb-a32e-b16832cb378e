{"id": "finsync", "name": "FinSync", "version": "0.0.1", "description": "Módulo de contabilidad, finanzas y análisis de rentabilidad", "author": "CoreDesk Team", "license": "Proprietary", "category": "finance", "tags": ["finance", "accounting", "analysis", "cash-flow"], "main": "FinSyncModule.js", "package": "FinSyncPackage.js", "style": "finsync.css", "install": "install.js", "dependencies": {"coredesk": ">=0.0.2"}, "requiredPermissions": ["storage.read", "storage.write", "ui.tabs", "ui.panels"], "requiredLicense": "professional", "engines": {"coredesk": ">=0.0.2"}, "homepage": "https://coredeskpro.com/modules/finsync", "repository": {"type": "git", "url": "https://github.com/coredesk/modules/finsync"}, "packageFile": "finsync-0.0.1.tar.gz", "checksum": "sha256:aef7ef4901fb9cd1dd25738bf7b146129c4aa3e16722c94dc1807803f2dd3062", "size": 6286, "downloadCount": 0, "lastUpdated": "2025-07-08T03:41:35.367Z", "status": "development", "compatibility": {"coredesk": ">=0.0.2", "platforms": ["windows", "macos", "linux"]}, "coredesk": {"moduleType": "ui", "activationMethod": "lazy", "supportedThemes": ["dark", "light"], "minScreenWidth": 1024, "moduleIcon": "💰", "moduleColor": "#1e40af"}, "files": ["FinSyncModule.js", "FinSyncPackage.js", "finsync.css", "install.js", "package.json"]}