{"name": "coredesk-workspace", "version": "0.0.2", "description": "CoreDesk Workspace - Module management and deployment scripts", "private": true, "scripts": {"package-modules": "node scripts/package-modules.js", "deploy-modules": "./scripts/deploy-modules.sh", "build-and-deploy": "./scripts/build-and-deploy.sh"}, "workspaces": ["api", "portal", "admin", "adminPanel", "coredesk", "apps"], "engines": {"node": ">=16.0.0"}, "keywords": ["coredesk", "modules", "deployment", "workspace"], "author": "CoreDesk Team", "license": "Proprietary"}