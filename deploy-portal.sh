#!/bin/bash
# Deploy portal updates to production server

echo "Building portal Docker image..."
cd portal
docker build -t portal:0.0.6 .

echo "Saving portal image..."
docker save portal:0.0.6 | gzip > ../portal-v0.0.6.tar.gz

echo "Image saved to portal-v0.0.6.tar.gz"
echo "Size: $(du -h ../portal-v0.0.6.tar.gz | cut -f1)"

echo ""
echo "To deploy to production:"
echo "1. Copy portal-v0.0.6.tar.gz to production server"
echo "2. SSH to production and run:"
echo "   docker load < portal-v0.0.6.tar.gz"
echo "   docker-compose up -d portal"