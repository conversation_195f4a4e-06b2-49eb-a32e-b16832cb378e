# CoreDesk v2.0

## 🚀 Plataforma de Gestión Empresarial Multi-Servicio

CoreDesk es una plataforma empresarial integral que combina una aplicación de escritorio con servicios web backend para proporcionar una solución completa de gestión de licencias, usuarios y módulos empresariales.

### 📋 Características Principales

- **🖥️ Aplicación de Escritorio**: Interfaz nativa desarrollada con Electron
- **🔧 Panel de Administración**: Frontend React para gestión empresarial
- **🌐 API Backend**: Servidor Node.js con MongoDB
- **📦 Portal de Descargas**: Sitio web público para distribución
- **🔐 Sistema de Autenticación**: JWT + Firebase integrado
- **📄 Gestión de Licencias**: Sistema completo de activación y validación
- **🐳 Docker Ready**: Configuración completa para contenedores

### 🏗️ Arquitectura del Proyecto

```
coredesk/
├── api/                  # Backend API (Node.js + MongoDB)
├── admin/               # Panel Admin (Node.js + EJS)
├── adminPanel/          # Panel Admin React (React + Vite)
├── portal/              # Portal público (Node.js + EJS)
├── coredesk/           # Aplicación Electron
├── apps/               # Archivos de distribución
└── docker-compose.yml  # Configuración Docker
```

## Componentes del Sistema

El sistema consta de múltiples componentes integrados:

1. **API Backend**: Servidor Node.js con endpoints RESTful completos
2. **Panel de Administración**: Interfaz web React para gestión avanzada
3. **Portal Público**: Sitio web para descargas y información
4. **Aplicación Desktop**: Cliente Electron multiplataforma

## Requisitos

- Docker y Docker Compose
- Node.js 22+ (para desarrollo local)
- MongoDB 8.0+ (incluido en Docker Compose)

## Estructura del Proyecto

```
/
├── api/               # Servidor API
├── admin/             # Panel de administración (pendiente)
├── nginx/             # Configuración de Nginx
├── docker-compose.yml # Orquestación de servicios
└── .env               # Variables de entorno globales
```

## Configuración

### Variables de Entorno

Antes de iniciar el sistema, configure las variables de entorno en los archivos `.env`:

1. Archivo `.env` en la raíz (para Docker Compose):
   - `MONGO_USER`: Usuario de MongoDB
   - `MONGO_PASSWORD`: Contraseña de MongoDB

2. Archivo `api/.env`:
   - `MONGO_URI`: URI de conexión a MongoDB
   - `JWT_SECRET`: Clave secreta para JWT
   - `ADMIN_EMAIL`: Email del administrador
   - `ADMIN_PASSWORD`: Contraseña del administrador

## Ejecución con Docker

Para iniciar todos los servicios con Docker Compose:

```bash
docker-compose up -d
```

Esto iniciará:
- Servidor API en http://localhost:3010
- Documentación de la API en http://localhost:3010/api-docs
- MongoDB en localhost:27017
- Mongo Express (administrador de MongoDB) en http://localhost:8081
- Nginx como proxy inverso en http://localhost:8080

## Desarrollo Local

Para ejecutar el servidor API localmente:

1. Instalar dependencias:
   ```bash
   cd api
   npm install
   ```

2. Iniciar el servidor en modo desarrollo:
   ```bash
   npm run dev
   ```

3. Para inicializar la base de datos con valores predeterminados:
   ```bash
   node src/scripts/initDb.js
   ```

## Endpoints API

La API sigue una estructura RESTful con los siguientes grupos de endpoints:

- **Autenticación**: `/v1/auth/*`
- **Licencias**: `/v1/licenses/*`
- **Usuarios**: `/v1/users/*`
- **Activaciones**: `/v1/activations/*`
- **Aplicaciones**: `/v1/applications/*`
- **Usuarios de licencia**: `/v1/license-users/*`

Para ver la documentación completa de la API, acceda a http://localhost:3010/api-docs cuando el servidor esté en ejecución.

## Flujo de Licencias de Prueba

El sistema implementa un flujo de 4 pasos para licencias de prueba:

1. **Verificación de elegibilidad**: Endpoint `/v1/licenses/verify-trial-eligibility`
2. **Registro de usuario**: Endpoint `/v1/auth/register`
3. **Solicitud de licencia de prueba**: Endpoint `/v1/licenses/request-trial`
4. **Activación de licencia**: Endpoint `/v1/licenses/activate`
