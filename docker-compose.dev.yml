services: # Servidor API
  api:
    build: ./api
    container_name: srv-api
    restart: unless-stopped
    ports:
      - "3010:3010"
    env_file:
      - ./api/.env
    environment:
      MONGO_URI: mongodb://admin:Qazwsx123!@mongo:27017/srv_licenses?authSource=admin
    volumes:
      - ./api/logs:/app/logs
      - /var/run/docker.sock:/var/run/docker.sock
      - ./backups:/app/backups
    depends_on:
      - mongo
    networks:
      - srv-network

  # Base de datos MongoDB
  mongo:
    image: mongo:8.0
    container_name: srv-mongo
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_USER:-admin}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_PASSWORD:-Qazwsx123!}
    volumes:
      - mongo-data:/data/db
    networks:
      - srv-network

  # Herramienta de administración para MongoDB
  mongo-express:
    image: mongo-express:latest
    container_name: srv-mongo-express
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: ${MONGO_USER:-admin}
      ME_CONFIG_MONGODB_ADMINPASSWORD: ${MONGO_PASSWORD:-Qazwsx123!}
      ME_CONFIG_MONGODB_SERVER: mongo
      ME_CONFIG_BASICAUTH_USERNAME: ${ME_USER:-admin}
      ME_CONFIG_BASICAUTH_PASSWORD: ${ME_PASSWORD:-Qazwsx123!}
      # Configuración específica para trabajar con el path base      ME_CONFIG_SITE_BASEURL: ""
      ME_CONFIG_OPTIONS_EDITORTHEME: "ambiance"
      ME_CONFIG_REQUEST_SIZE: "100kb"
      ME_CONFIG_MONGODB_PORT: 27017
    depends_on:
      - mongo
    networks:
      - srv-network

  # Portal Web CoreDesk
  portal:
    build: ./portal
    container_name: srv-portal
    restart: unless-stopped
    ports:
      - "3000:3000"
    env_file:
      - ./portal/.env
    environment:
      NODE_ENV: production
      PORT: 3000
      PORTAL_URL: https://coredeskpro.com
      API_URL: https://api.coredeskpro.com/v1
      APP_NAME: CoreDesk Portal
      APP_VERSION: 0.0.2
    volumes:
      - ./portal/logs:/app/logs
      - ./portal/uploads:/app/uploads
      - ./portal/src:/app/src
      - ./portal/views:/app/views
    networks:
      - srv-network

  # Nuevo Panel de Administración React 
  admin-panel:
    build: ./adminPanel
    container_name: admin-panel
    restart: unless-stopped
    ports:
      - "3020:3011"
    environment:
      NODE_ENV: production
      VITE_API_URL: https://api.coredeskpro.com/v1
      VITE_APP_NAME: "Panel de Administración CoreDesk-Pro"
      VITE_APP_VERSION: "1.0.0"
      VITE_ENVIRONMENT: production
      VITE_ENABLE_DEBUG: false
      VITE_JWT_STORAGE_KEY: admin_token
      VITE_REFRESH_TOKEN_KEY: admin_refresh_token
    depends_on:
      - api
    networks:
      - srv-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3011/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Proxy inverso Nginx
  nginx:
    build: ./nginx
    container_name: srv-nginx
    restart: unless-stopped
    ports:
      - "88:80"
    depends_on:
      - api
      - portal
      - admin-panel
      - mongo-express
    networks:
      - srv-network

networks:
  srv-network:
    driver: bridge

volumes:
  mongo-data:
