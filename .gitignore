# Archivos de distribución y builds
*.AppImage
*.dmg
*.pkg
*.deb
*.rpm
*.tar.gz
*.tar.bz2
*.tar.xz
*.exe
*.msi
*.zip
dist/
build/
out/
releases/

# Docker exports y archivos grandes
docker-exports/*.tar.gz
**/docker-exports/*.tar.gz
apps/*.AppImage
apps/*.tar.gz
apps/*.exe
apps/*.dmg

# Archivos sensibles
*.pem
*.key
*.crt
*credentials*
*secrets*
*.p12
*.pfx

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Archivos específicos de aplicación
api/logs/*

# Directorios de dependencias
node_modules/
jspm_packages/

# Directorios de caché
.npm
.eslintcache
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Directorios de entorno
.env
.env.*
!.env.example
.env.local
.env.development.local
.env.test.local
.env.production.local

# Directorios de construcción
dist/
build/
out/

# Archivos de configuración
.DS_Store
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json

# Archivos temporales
*.swp
*.swo
*~

# Directorios de cobertura
coverage/
.nyc_output/

# Docker
.dockerignore
