[Preload] Initializing CoreDesk preload bridge...
VM5:166 [Preload] APIs exposed successfully
VM5:134 [Preload] CoreDesk preload bridge initialized successfully
index.html:48 🔒 IMMEDIATE AUTH GATE: Checking BEFORE any content renders...
index.html:55 🔍 IMMEDIATE AUTH GATE: Token in localStorage: true
index.html:56 🔍 IMMEDIATE AUTH GATE: Expiry in localStorage: true
index.html:72 🔍 IMMEDIATE AUTH GATE: Token is valid: true
index.html:88 ✅ IMMEDIATE AUTH GATE: Authenticated - allowing content to render
GlobalLogger.js:86 [GlobalLogger] Global logger initialized successfully with safe undefined/null handling
constants.js:226 [Constants] Global constants loaded successfully
events.js:274 [CoreDeskEvents] Global event system initialized
Logger.js:42 [Logger] Initialized successfully
Logger.js:373 [Logger] Global logging system ready
MemoryManager.js:551 [MemoryManager] Global instance created successfully
GlobalLogger.js:30 [Memory] [MemoryManager] Initializing memory management system...
GlobalLogger.js:30 [Memory] [MemoryManager] Memory management system initialized
cleanup-localStorage.js:355 🛠️ Utilidades de limpieza de CoreDesk cargadas.
cleanup-localStorage.js:356 �️ PROTECCIÓN DE DATOS DE AUTENTICACIÓN ACTIVADA
cleanup-localStorage.js:357 �💡 Uso seguro:
cleanup-localStorage.js:358    - CoreDeskCleanup.diagnoseCoreDesklocalStorage() - Diagnóstico completo
cleanup-localStorage.js:359    - CoreDeskCleanup.cleanupCorruptedLocalStorage() - Limpieza segura (preserva auth)
cleanup-localStorage.js:360    - CoreDeskCleanup.cleanupSpecificModule("lexflow") - Limpiar módulo específico
cleanup-localStorage.js:361 ⚠️ IMPORTANTE: Los tokens de autenticación y configuración están protegidos automáticamente
InputValidator.js:899 [InputValidator] Enhanced security validation system initialized
SecurityManager.js:841 Security [SecurityManager] Enhanced security system initialized successfully
GlobalInit.js:96 [GlobalInit] Global initialization system loaded
index.html:525 [Success] Axios loaded successfully from local file
ApiClient.js:40 [ApiClient] Initializing... baseURL: https://api.coredeskpro.com/v1
ApiClient.js:44 [ApiClient] Axios found, creating client...
ApiClient.js:57 [ApiClient] Initialization complete
AuthApiService.js:38 [AuthApiService] API client initialized successfully
IconFallback.js:18 [IconFallback] Inicializando sistema de fallback de iconos...
authConfig.js:254 authConfig [CoreDeskAuth] Global configuration loaded successfully
activityBar.js:20 UI [ActivityBar] Initializing...
activityBar.js:91 UI [ActivityBar] Icon fallbacks activated for better visibility
activityBar.js:26 UI [ActivityBar] Initialized successfully
activityBar.js:256 UI [ActivityBar] Global instance created successfully
FileExplorer.js:23 FileExplorer Initializing file explorer...
FileExplorer.js:43 FileExplorer Getting default path...
FileExplorer.js:858 FileExplorer File explorer initialized
panelManager.js:39 UI [PanelManager] Initializing...
FileExplorer.js:498 FileExplorer currentPath no está definido, usando ruta por defecto
loadRealFileList @ FileExplorer.js:498
FileExplorer.js:43 FileExplorer Getting default path...
panelManager.js:472 UI [PanelManager] Opened left panel
panelManager.js:497 UI [PanelManager] Closed right panel
panelManager.js:497 UI [PanelManager] Closed bottom panel
panelManager.js:46 UI [PanelManager] Initialized successfully
panelManager.js:1208 UI [PanelManager] Global instance created successfully
SecureTokenManager.js:62 [SecureTokenManager] Encryption key initialized
statusBar.js:22 UI [StatusBar] Initializing...
statusBar.js:44 UI [StatusBar] Using existing HTML structure
statusBar.js:29 UI [StatusBar] Initialized successfully
statusBar.js:592 UI [StatusBar] Global instance created successfully
ThemeManager.js:20 ThemeManager [ThemeManager] Initializing...
ThemeManager.js:43 ThemeManager [ThemeManager] System prefers: light
ThemeManager.js:152 ThemeManager [ThemeManager] Applied theme: dark
ThemeManager.js:86 ThemeManager [ThemeManager] Loaded theme: dark
ThemeManager.js:34 ThemeManager [ThemeManager] Initialized successfully
ThemeManager.js:480 ThemeManager [ThemeManager] ThemeManager class defined and global instance created
AccountModal.js:20 UI [AccountModal] Initializing...
AccountModal.js:72 AccountModal [AccountModal] User info loaded: Object
AccountModal.js:107 UI [AccountModal] User info loaded: Object
AccountModal.js:639 UI [AccountModal] Class defined and global instance created successfully
AccountModal.js:32 UI [AccountModal] Initialized successfully
PanelResizer.js:24 PanelResizer Initializing panel resizer...
PanelResizer.js:32 PanelResizer Panel resizer initialized successfully
PanelResizer.js:522 PanelResizer Panel resizer component loaded successfully
UpdateDialogs.js:36 [UpdateDialogs] Initialized successfully
ConfigurationPanel.js:100 Config [ConfigurationPanel] Initializing...
ConfigurationPanel.js:128 Config [ConfigurationPanel] Loading settings...
ConfigurationPanel.js:171 Config [ConfigurationPanel] Settings loaded: Object
ConfigurationPanel.js:2791 Config [ConfigurationPanel] Class defined and global instance created successfully
SimplifiedTabManager.js:515 SimplifiedTabManager [SimplifiedTabManager] Class defined successfully
TabContentRenderer.js:636 TabContentRenderer [TabContentRenderer] Class defined successfully
TabStateStore.js:539 TabStateStore [TabStateStore] Class defined successfully
TabEventBus.js:506 TabEventBus [TabEventBus] Class defined successfully
SimplifiedTabSystem.js:737 TabSystem [SimplifiedTabSystem] Class defined successfully
DeviceFingerprint.js:20 DeviceFingerprint [DeviceFingerprint] Initializing device fingerprint generator...
DeviceFingerprint.js:41 DeviceFingerprint [DeviceFingerprint] Collecting fingerprint components...
DeviceFingerprint.js:407 DeviceFingerprint [DeviceFingerprint] Class defined successfully
DeviceFingerprint.js:60 Component [DeviceFingerprint] Collected 23 fingerprint components
DeviceFingerprint.js:262 Component [DeviceFingerprint] Generated fingerprint: e2d6cd2d2e53788f...
DeviceFingerprint.js:26 DeviceFingerprint [DeviceFingerprint] Device fingerprint generated successfully
PanelResizer.js:399 PanelResizer Panel sizes loaded for content types: Object
LicenseValidator.js:595 LicenseValidator [LicenseValidator] Class defined successfully
LicenseManager.js:49 License [LicenseManager] Initializing...
LicenseValidator.js:57 LicenseValidator [LicenseValidator] Initializing...
DeviceFingerprint.js:20 DeviceFingerprint [DeviceFingerprint] Initializing device fingerprint generator...
DeviceFingerprint.js:41 DeviceFingerprint [DeviceFingerprint] Collecting fingerprint components...
DeviceFingerprint.js:20 DeviceFingerprint [DeviceFingerprint] Initializing device fingerprint generator...
DeviceFingerprint.js:41 DeviceFingerprint [DeviceFingerprint] Collecting fingerprint components...
LicenseValidator.js:57 LicenseValidator [LicenseValidator] Initializing...
DeviceFingerprint.js:20 DeviceFingerprint [DeviceFingerprint] Initializing device fingerprint generator...
DeviceFingerprint.js:41 DeviceFingerprint [DeviceFingerprint] Collecting fingerprint components...
DeviceFingerprint.js:20 DeviceFingerprint [DeviceFingerprint] Initializing device fingerprint generator...
DeviceFingerprint.js:41 DeviceFingerprint [DeviceFingerprint] Collecting fingerprint components...
LicenseManager.js:795 License [LicenseManager] Class defined and global instance created successfully
4DeviceFingerprint.js:60 Component [DeviceFingerprint] Collected 23 fingerprint components
DeviceFingerprint.js:262 Component [DeviceFingerprint] Generated fingerprint: e2d6cd2d2e53788f...
DeviceFingerprint.js:26 DeviceFingerprint [DeviceFingerprint] Device fingerprint generated successfully
LicenseActivationModal.js:24 LicenseModal [LicenseActivationModal] Initializing...
LicenseActivationModal.js:45 LicenseModal [LicenseActivationModal] Initialized successfully
LicenseActivationModal.js:1200 LicenseModal [LicenseActivationModal] Class defined and global instance created successfully
DeviceFingerprint.js:262 Component [DeviceFingerprint] Generated fingerprint: e2d6cd2d2e53788f...
DeviceFingerprint.js:26 DeviceFingerprint [DeviceFingerprint] Device fingerprint generated successfully
LicenseValidator.js:64 LicenseValidator [LicenseValidator] Initialized successfully
DeviceFingerprint.js:262 Component [DeviceFingerprint] Generated fingerprint: e2d6cd2d2e53788f...
DeviceFingerprint.js:26 DeviceFingerprint [DeviceFingerprint] Device fingerprint generated successfully
DeviceFingerprint.js:262 Component [DeviceFingerprint] Generated fingerprint: e2d6cd2d2e53788f...
DeviceFingerprint.js:26 DeviceFingerprint [DeviceFingerprint] Device fingerprint generated successfully
LicenseValidator.js:64 LicenseValidator [LicenseValidator] Initialized successfully
LicenseManager.js:355 License [LicenseManager] Checking existing activation...
LicenseManager.js:361 License [LicenseManager] No stored license found
LicenseManager.js:62 License [LicenseManager] Initialized successfully
FirebaseConnector.js:849 FirebaseConnector [FirebaseConnector] Class defined successfully
DataSyncService.js:51 Sync [DataSyncService] Initializing...
DataSyncService.js:139 Sync [DataSyncService] No license found, cloud access disabled
DataSyncService.js:1353 Sync [DataSyncService] Class defined and global instance created successfully
DataSyncService.js:116 Sync [DataSyncService] Configuration loaded Object
DataSyncService.js:78 Sync [DataSyncService] Initialized successfully
SyncStatusPanel.js:31 SyncStatus [SyncStatusPanel] Initializing...
SyncStatusPanel.js:742 SyncStatus [SyncStatusPanel] Class defined and global instance created successfully
SyncStatusPanel.js:46 SyncStatus [SyncStatusPanel] Initialized successfully
ModulePackage.js:1389 ModulePackage [ModulePackage] Class loaded successfully
ModuleRegistry.js:2447 ModuleRegistry [ModuleRegistry] Class loaded successfully
ModuleDownloader.js:786 ModuleDownloader [ModuleDownloader] Service loaded successfully
DynamicStyleLoader.js:623 DynamicStyleLoader [DynamicStyleLoader] Service loaded successfully
DynamicModuleManager.js:1018 DynamicModuleManager [DynamicModuleManager] Class loaded successfully
ExclusiveModuleController.js:34 ModuleController [ExclusiveModuleController] Initializing...
DynamicModuleManager.js:71 [DynamicModuleManager] FORCE PRODUCTION: Using production module repository
GlobalLogger.js:30 [DynamicModuleManager] Initializing...
GlobalLogger.js:30 [ModuleRegistry] Initializing module registry...
GlobalLogger.js:30 [ModuleRegistry] Performing corrupted data cleanup...
ExclusiveModuleController.js:1004 ModuleController [ExclusiveModuleController] Global instance created successfully
UnifiedAuthManager.js:844 Auth [UnifiedAuthManager] Global instance created successfully
GlobalLogger.js:30 [ModuleRegistry] Corrupted data cleanup completed (protected auth data preserved)
UnifiedAuthManager.js:57 Auth [UnifiedAuthManager] Initializing...
authConfig.js:57 authConfig [isAuthenticated] Checking authentication status...
TokenManager.js:231 TokenManager.getToken: encryptedToken exists? true
TokenManager.js:235 TokenManager.getToken: decrypted token exists? true
TokenManager.js:264 TokenManager.isTokenValid: token exists? true
TokenManager.js:265 TokenManager.isTokenValid: expiry exists? true
TokenManager.js:270 TokenManager.isTokenValid: token is valid? true expiry: Sat Jul 12 2025 15:32:02 GMT-0600 (Central Standard Time)
authConfig.js:62 authConfig [isAuthenticated] TokenManager check result: true
UnifiedAuthManager.js:685 Auth [SessionManager] Token refresh timer started
UnifiedAuthManager.js:715 Auth [SessionManager] Inactivity monitor started
UnifiedAuthManager.js:73 Auth [UnifiedAuthManager] Initialized successfully
index.html:96 ✅ IMMEDIATE AUTH GATE: Added authenticated class to body
IconFallback.js:34 [IconFallback] Verificando carga de iconos SVG...
IconFallback.js:38 [IconFallback] Soporte mask-image: true
IconFallback.js:69 [IconFallback] Probando carga de archivos SVG...
authConfig.js:236 authConfig [CoreDeskAuth] Configuration initialized
authConfig.js:241 authConfig [CoreDeskAuth] Deferring authentication to AuthGuard - preventing race conditions
titleBar.js:15 UI [TitleBar] Initializing...
titleBar.js:19 UI [TitleBar] Maximize button found: true
titleBar.js:34 UI [TitleBar] Account button found, adding click listener
titleBar.js:55 UI [TitleBar] Dropdown state initialized as hidden
titleBar.js:61 UI [TitleBar] My Account option found, adding click listener
titleBar.js:73 UI [TitleBar] Logout option found, adding click listener
titleBar.js:24 UI [TitleBar] Initialized successfully
titleBar.js:388 UI [TitleBar] Global instance created successfully
app.js:16 [CoreDeskApp] Could not set global app reference:
warn @ app.js:16
app.js:14 [CoreDeskApp] Starting application initialization...
app.js:60 CoreDeskApp ✅ Starting dashboard initialization (auth verified synchronously)
app.js:61 *** COREDESK APP.JS - FORCED PRODUCTION URL VERSION ***
app.js:62 *** THIS MESSAGE CONFIRMS THE NEW CODE IS RUNNING ***
app.js:67 CoreDeskApp Initializing AuthGuard as central auth authority
AuthGuard.js:19 Auth [AuthGuard] Initializing authentication guard...
AuthGuard.js:25 Auth [AuthGuard] Performing initial authentication check
AuthGuard.js:54 Auth [AuthGuard] Starting authentication check for path: /C:/Users/<USER>/AppData/Local/Programs/CoreDesk%20Framework/resources/app.asar/src/index.html
authConfig.js:57 authConfig [isAuthenticated] Checking authentication status...
TokenManager.js:231 TokenManager.getToken: encryptedToken exists? true
TokenManager.js:235 TokenManager.getToken: decrypted token exists? true
TokenManager.js:264 TokenManager.isTokenValid: token exists? true
TokenManager.js:265 TokenManager.isTokenValid: expiry exists? true
TokenManager.js:270 TokenManager.isTokenValid: token is valid? true expiry: Sat Jul 12 2025 15:32:02 GMT-0600 (Central Standard Time)
authConfig.js:62 authConfig [isAuthenticated] TokenManager check result: true
TokenManager.js:231 TokenManager.getToken: encryptedToken exists? true
TokenManager.js:235 TokenManager.getToken: decrypted token exists? true
TokenManager.js:264 TokenManager.isTokenValid: token exists? true
TokenManager.js:265 TokenManager.isTokenValid: expiry exists? true
TokenManager.js:270 TokenManager.isTokenValid: token is valid? true expiry: Sat Jul 12 2025 15:32:02 GMT-0600 (Central Standard Time)
AuthGuard.js:104 Auth [AuthGuard] User authenticated, access granted
AuthGuard.js:35 Auth [AuthGuard] Authentication guard initialized successfully
app.js:14 [CoreDeskApp] Setting up electronAPI listeners
app.js:272 [CoreDeskApp] Title bar initialized
app.js:14 [CoreDeskApp] Account dropdown initialized
IconFallback.js:100 [IconFallback] SVG cargado exitosamente: ./assets/icons/folder-mask.svg
FileExplorer.js:51 FileExplorer Attempting to get CoreDesk path...
IconFallback.js:100 [IconFallback] SVG cargado exitosamente: ./assets/icons/cloud-mask.svg
FileExplorer.js:51 FileExplorer Attempting to get CoreDesk path...
FileExplorer.js:53 FileExplorer CoreDesk path result: Object
FileExplorer.js:55 FileExplorer Using CoreDesk path: C:\Users\<USER>\OneDrive\Documents\CoreDesk
IconFallback.js:100 [IconFallback] SVG cargado exitosamente: ./assets/icons/search-mask.svg
FileExplorer.js:53 FileExplorer CoreDesk path result: Object
FileExplorer.js:55 FileExplorer Using CoreDesk path: C:\Users\<USER>\OneDrive\Documents\CoreDesk
FileExplorer.js:506 FileExplorer Loading file list for path: C:\Users\<USER>\OneDrive\Documents\CoreDesk
IconFallback.js:100 [IconFallback] SVG cargado exitosamente: ./assets/icons/grid-mask.svg
FileExplorer.js:508 FileExplorer Directory listing result: Object
FileExplorer.js:550 FileExplorer Processed files for rendering: Array(7)
IconFallback.js:100 [IconFallback] SVG cargado exitosamente: ./assets/icons/puzzle-mask.svg
IconFallback.js:100 [IconFallback] SVG cargado exitosamente: ./assets/icons/settings-mask.svg
authConfig.js:250 authConfig [CoreDeskAuth] Dependencies initialized - AuthGuard will handle authentication
IconFallback.js:100 [IconFallback] SVG cargado exitosamente: ./assets/icons/leftPanel-mask.svg
FileExplorer.js:31 FileExplorer File explorer initialized with path: C:\Users\<USER>\OneDrive\Documents\CoreDesk
2FileExplorer.js:506 FileExplorer Loading file list for path: C:\Users\<USER>\OneDrive\Documents\CoreDesk
IconFallback.js:100 [IconFallback] SVG cargado exitosamente: ./assets/icons/rightPanel-mask.svg
FileExplorer.js:508 FileExplorer Directory listing result: Object
FileExplorer.js:550 FileExplorer Processed files for rendering: Array(7)
FileExplorer.js:508 FileExplorer Directory listing result: Object
FileExplorer.js:550 FileExplorer Processed files for rendering: Array(7)
IconFallback.js:100 [IconFallback] SVG cargado exitosamente: ./assets/icons/bottomPanel-mask.svg
IconFallback.js:109 [IconFallback] SVGs cargados: 9/9
IconFallback.js:113 [IconFallback] Prueba de carga SVG: EXITOSA
IconFallback.js:57 [IconFallback] SVG icons loaded successfully, no fallback needed
IconFallback.js:181 [IconFallback] Icono de actividad transparente detectado
(anonymous) @ IconFallback.js:181
IconFallback.js:133 [IconFallback] Activando sistema de fallback...
IconFallback.js:246 [IconFallback] Estilos de fallback agregados
IconFallback.js:249 [IconFallback] Clase icon-fallback-active aplicada: true
IconFallback.js:253 [IconFallback] Iconos de panel encontrados: 4
IconFallback.js:255 [IconFallback] Panel icon 0: panel-icon icon-layout-sidebar-left rgb(150, 150, 150)
IconFallback.js:255 [IconFallback] Panel icon 1: panel-icon icon-layout-sidebar-right rgb(150, 150, 150)
IconFallback.js:255 [IconFallback] Panel icon 2: panel-icon icon-layout-panel rgb(150, 150, 150)
IconFallback.js:255 [IconFallback] Panel icon 3: SVGAnimatedString rgb(150, 150, 150)
IconFallback.js:146 [IconFallback] Iconos de panel encontrados: 4
IconFallback.js:147 [IconFallback] Iconos de actividad encontrados: 7
IconFallback.js:162 [IconFallback] Sistema de fallback activado exitosamente
5IconFallback.js:181 [IconFallback] Icono de actividad transparente detectado
(anonymous) @ IconFallback.js:181
GlobalLogger.js:30 [ModuleRegistry] Loading installed modules from database...
GlobalLogger.js:30 [ModulePersistenceClient] Initializing JSON-based persistence...
GlobalInit.js:12 [GlobalInit] Initializing CoreDesk components...
GlobalInit.js:17 [GlobalInit] InputValidator dependencies initialized
SecurityManager.js:72 Security [SecurityManager] Initializing security policies...
SecurityManager.js:126 Security [SecurityManager] CSP configured successfully
SecurityManager.js:78 Security [SecurityManager] Security manager initialized successfully
GlobalInit.js:23 [GlobalInit] SecurityManager dependencies initialized
AuthApiService.js:38 [AuthApiService] API client initialized successfully
GlobalInit.js:29 [GlobalInit] AuthApiService dependencies initialized
GlobalInit.js:34 [GlobalInit] LicenseApiService dependencies initialized
GlobalInit.js:40 [GlobalInit] UnifiedAuthManager dependencies initialized
GlobalInit.js:53 [GlobalInit] All CoreDesk components initialized successfully
GlobalLogger.js:30 [ModulePersistenceClient] JSON persistence service is ready
GlobalLogger.js:30 [ModulePersistenceClient] Starting cleanup of corrupted localStorage entries...
GlobalLogger.js:30 [ModulePersistenceClient] Found 0 module entries in localStorage
GlobalLogger.js:28 [ModulePersistenceClient] Cleanup completed: {
  "totalEntries": 0,
  "corruptedEntries": 0,
  "cleanedEntries": 0,
  "validEntries": 0,
  "errors": []
}
GlobalLogger.js:30 [ModuleRegistry] Found 0 active modules in database
GlobalLogger.js:30 [ModuleRegistry] Loaded 0 installed modules from database
GlobalLogger.js:30 [ModuleRegistry] Registry initialized with 0 modules
GlobalLogger.js:30 [DynamicModuleManager] Creating ModuleDownloader instance
DynamicModuleManager.js:71 [DynamicModuleManager] FORCE PRODUCTION: Using production module repository
ModuleDownloader.js:66 [ModuleDownloader] FORCE PRODUCTION: Using production module repository
GlobalLogger.js:30 [ModuleDownloader] Downloader initialized
GlobalLogger.js:30 [DynamicModuleManager] Creating ModulePersistenceClient instance
GlobalLogger.js:30 [ModuleRegistry] Initializing module registry...
GlobalLogger.js:30 [ModuleRegistry] Performing corrupted data cleanup...
GlobalLogger.js:30 [ModuleRegistry] Corrupted data cleanup completed (protected auth data preserved)
GlobalLogger.js:30 [ModuleRegistry] Loading installed modules from database...
GlobalLogger.js:30 [ModuleRegistry] Found 0 active modules in database
GlobalLogger.js:30 [ModuleRegistry] Loaded 0 installed modules from database
GlobalLogger.js:30 [ModuleRegistry] Registry initialized with 0 modules
GlobalLogger.js:30 [ModulePersistenceClient] Initializing JSON-based persistence...
GlobalLogger.js:30 [ModulePersistenceClient] JSON persistence service is ready
GlobalLogger.js:30 [ModulePersistenceClient] Starting cleanup of corrupted localStorage entries...
GlobalLogger.js:30 [ModulePersistenceClient] Found 0 module entries in localStorage
GlobalLogger.js:28 [ModulePersistenceClient] Cleanup completed: {
  "totalEntries": 0,
  "corruptedEntries": 0,
  "cleanedEntries": 0,
  "validEntries": 0,
  "errors": []
}
GlobalLogger.js:30 [DynamicModuleManager] Module persistence service initialized
GlobalLogger.js:30 [DynamicModuleManager] Loaded 0 installed modules
GlobalLogger.js:30 [DynamicModuleManager] Initialized successfully
ExclusiveModuleController.js:153 ModuleController [ExclusiveModuleController] Dynamic module system initialized
GlobalLogger.js:30 [DynamicModuleManager] Refreshing registry...
GlobalLogger.js:30 [ModuleRegistry] Initializing module registry...
GlobalLogger.js:30 [ModuleRegistry] Performing corrupted data cleanup...
GlobalLogger.js:30 [ModuleRegistry] Corrupted data cleanup completed (protected auth data preserved)
GlobalLogger.js:30 [ModuleRegistry] Loading installed modules from database...
GlobalLogger.js:30 [ModuleRegistry] Found 0 active modules in database
GlobalLogger.js:30 [ModuleRegistry] Loaded 0 installed modules from database
GlobalLogger.js:30 [ModuleRegistry] Registry initialized with 0 modules
GlobalLogger.js:30 [DynamicModuleManager] Loaded 0 installed modules
GlobalLogger.js:30 [DynamicModuleManager] Registry refreshed successfully
ExclusiveModuleController.js:171 ModuleController [ExclusiveModuleController] Loaded 0 dynamic modules
ExclusiveModuleController.js:175 ModuleController [ExclusiveModuleController] No modules found, checking filesystem...
GlobalLogger.js:30 [ModuleRegistry] Loading installed modules from database...
GlobalLogger.js:30 [ModuleRegistry] Found 0 active modules in database
GlobalLogger.js:30 [ModuleRegistry] Loaded 0 installed modules from database
GlobalLogger.js:30 [DynamicModuleManager] Loaded 0 installed modules
ExclusiveModuleController.js:195 ModuleController [ExclusiveModuleController] Loaded 0 modules from filesystem
ExclusiveModuleController.js:65 ModuleController [ExclusiveModuleController] Initialized successfully
ExclusiveModuleController.js:66 ModuleController [ExclusiveModuleController] Using dynamic module system
ExclusiveModuleController.js:71 ModuleController [ExclusiveModuleController] No active module detected
ConfigurationPanel.js:115 Config [ConfigurationPanel] Initialized successfully
app.js:16 [CoreDeskApp] Backend not available - continuing in frontend-only mode
warn @ app.js:16
app.js:14 [CoreDeskApp] Application initialized successfully
app.js:14 [CoreDeskApp] Loading modules with optimized approach...
app.js:14 [CoreDeskApp] Loading modules directly from server for immediate display
app.js:804 [CoreDeskApp] *** showServerModules() CALLED ***
app.js:805 [CoreDeskApp] *** STARTING SERVER MODULE LOAD ***
app.js:14 [CoreDeskApp] Loading modules from server (clean architecture)
app.js:1023 [CoreDeskApp] FORCE PRODUCTION: Using production module repository
app.js:14 [CoreDeskApp] Fetching modules from URL: https://coredeskpro.com/api/modules
app.js:822 [CoreDeskApp] Debug: Attempting to fetch modules from: https://coredeskpro.com/api/modules
app.js:823 [CoreDeskApp] Debug: User agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) coredesk/0.0.2 Chrome/126.0.6478.234 Electron/31.7.7 Safari/537.36
app.js:824 [CoreDeskApp] Debug: Location hostname: 
app.js:825 [CoreDeskApp] Debug: *** FORCING PRODUCTION URLS ***
app.js:830 [CoreDeskApp] Debug: Cache-busted URL: https://coredeskpro.com/api/modules?cb=1752276936848
app.js:839 [CoreDeskApp] Debug: Response status: 200
app.js:846 [CoreDeskApp] Debug: API response: Object
app.js:14 [CoreDeskApp] Loaded 4 modules from server
app.js:854 [CoreDeskApp] Debug: Modules loaded: Array(4)
app.js:855 [CoreDeskApp] Debug: *** SUCCESS - MODULES LOADED FROM PRODUCTION ***
app.js:872 [CoreDeskApp] *** CALLING displayServerModules ***
app.js:891 [CoreDeskApp] *** displayServerModules() CALLED ***
app.js:892 [CoreDeskApp] *** MODULES TO DISPLAY: Array(4)
app.js:898 [CoreDeskApp] Module lexflow: downloadable=true, installed=false
app.js:898 [CoreDeskApp] Module protocolx: downloadable=true, installed=false
app.js:898 [CoreDeskApp] Module auditpro: downloadable=true, installed=false
app.js:898 [CoreDeskApp] Module finsync: downloadable=true, installed=false
app.js:962 [CoreDeskApp] *** MODULE GRID UPDATED WITH PRODUCTION DATA ***
app.js:963 [CoreDeskApp] *** THIRD-PARTY BUTTON SHOULD BE VISIBLE ***
app.js:874 [CoreDeskApp] *** displayServerModules COMPLETED ***
app.js:14 [CoreDeskApp] Setting up module dropzone
app.js:14 [CoreDeskApp] Module dropzone setup completed
panelManager.js:497 UI [PanelManager] Closed left panel
2titleBar.js:268 UI [TitleBar] Account dropdown forcibly hidden
GlobalLogger.js:30 [Memory] [MemoryManager] Memory stats: 9 active, 9 peak
app.js:14 [CoreDeskApp] Installing module lexflow from https://coredeskpro.com/api/modules/lexflow/latest/package
app.js:14 [CoreDeskApp] Attempting to fetch from: https://coredeskpro.com/api/modules/lexflow/latest/package
titleBar.js:268 UI [TitleBar] Account dropdown forcibly hidden
GlobalLogger.js:30 [DynamicModuleManager] Installing module package: lexflow
GlobalLogger.js:30 [ModulePackage] Initializing package: lexflow
VM66:44 Module [LexFlow] Initializing...
VM66:587 Module [LexFlow] Loading data...
VM66:609 Module [LexFlow] Database not available, using mock data
loadData @ VM66:609
initialize @ VM66:46
LexFlowModule @ VM66:36
eval @ VM66:1808
createUnsandboxedModuleClass @ ModulePackage.js:592
createModuleClass @ ModulePackage.js:426
initialize @ ModulePackage.js:196
installModulePackage @ DynamicModuleManager.js:182
await in installModulePackage (async)
handleModuleInstall @ app.js:1195
await in handleModuleInstall (async)
(anonymous) @ app.js:990
VM66:1810 Module [LexFlow] Module class defined and global instance created successfully
VM66:52 Module [LexFlow] Initialized successfully
GlobalLogger.js:30 [ModulePackage] Package lexflow initialized successfully
GlobalLogger.js:30 [DynamicModuleManager] About to call install() for module: lexflow
GlobalLogger.js:30 [ModulePackage] Installing package: lexflow
VM70:44 Module [LexFlow] Initializing...
VM70:587 Module [LexFlow] Loading data...
VM70:609 Module [LexFlow] Database not available, using mock data
loadData @ VM70:609
initialize @ VM70:46
LexFlowModule @ VM70:36
eval @ VM70:1808
createUnsandboxedModuleClass @ ModulePackage.js:592
createModuleClass @ ModulePackage.js:426
install @ ModulePackage.js:88
await in install (async)
installModulePackage @ DynamicModuleManager.js:186
await in installModulePackage (async)
handleModuleInstall @ app.js:1195
await in handleModuleInstall (async)
(anonymous) @ app.js:990
VM70:1810 Module [LexFlow] Module class defined and global instance created successfully
VM70:52 Module [LexFlow] Initialized successfully
GlobalLogger.js:30 [ModulePackage] Installing module assets to: C:\Users\<USER>\AppData\Local\Programs\CoreDesk Framework\modulos/lexflow
GlobalLogger.js:30 [ModulePackage] Module code written to: C:\Users\<USER>\AppData\Local\Programs\CoreDesk Framework\modulos/lexflow/lexflow.js
GlobalLogger.js:30 [ModulePackage] Styles written to: C:\Users\<USER>\AppData\Local\Programs\CoreDesk Framework\modulos/lexflow/lexflow.css
GlobalLogger.js:30 [ModulePackage] Manifest validation successful for lexflow
GlobalLogger.js:30 [ModulePackage] Attempting manifest write 1/3 for lexflow
GlobalLogger.js:30 [ModulePackage] Writing manifest file: C:\Users\<USER>\AppData\Local\Programs\CoreDesk Framework\modulos/lexflow/manifest.json
GlobalLogger.js:30 [ModulePackage] Manifest written to: C:\Users\<USER>\AppData\Local\Programs\CoreDesk Framework\modulos/lexflow/manifest.json
GlobalLogger.js:30 [ModulePackage] Manifest content preview: {
  "id": "lexflow",
  "name": "lexflow",
  "version": "1.0.0",
  "description": "Module lexflow",
 ...
GlobalLogger.js:30 [ModulePackage] Waiting 500ms for file system synchronization (attempt 1)...
GlobalLogger.js:30 [ModulePackage] Adding extra flush delay for lexflow...
GlobalLogger.js:30 [ModulePackage] Verification attempt 1/3 for lexflow
GlobalLogger.js:30 [ModulePackage] File stats verification successful for lexflow. Size: 173 bytes (expected: 173)
GlobalLogger.js:30 [ModulePackage] Manifest write and existence verification successful for lexflow
GlobalLogger.js:30 [ModulePackage] Assets installed for lexflow at: C:\Users\<USER>\AppData\Local\Programs\CoreDesk Framework\modulos/lexflow
GlobalLogger.js:30 [ModulePackage] Module lexflow marked as installed after successful manifest verification at 2025-07-11T23:36:08.110Z
GlobalLogger.js:30 [ModulePackage] About to call updateLocalMetadata() for: lexflow
GlobalLogger.js:30 [ModulePackage] About to store data with key: coredesk_module_lexflow
GlobalLogger.js:30 [ModulePackage] Successfully stored complete package data for lexflow
GlobalLogger.js:30 [ModulePackage] Verification - data exists in localStorage: YES
GlobalLogger.js:30 [ModulePackage] updateLocalMetadata() completed for: lexflow
GlobalLogger.js:30 [ModulePackage] Installation verification successful for lexflow - isInstalled is true in saved data
GlobalLogger.js:30 [ModulePackage] Post-installation verification successful for lexflow - manifest file size: 173 bytes
GlobalLogger.js:30 [ModulePackage] Final verification successful for lexflow - manifest file is valid
GlobalLogger.js:30 [ModulePackage] Package lexflow installed successfully
GlobalLogger.js:30 [DynamicModuleManager] Install() completed for module: lexflow, isInstalled: true
GlobalLogger.js:30 [ModuleRegistry] Registering module: lexflow
GlobalLogger.js:30 [ModuleRegistry] Module lexflow registered successfully
GlobalLogger.js:30 [ModulePersistenceClient] Registering module: lexflow@1.0.0 (mode: json)
GlobalLogger.js:30 [ModulePersistenceClient] Module registered successfully with JSON persistence: lexflow
GlobalLogger.js:30 [DynamicModuleManager] Module lexflow registered in database
GlobalLogger.js:30 [DynamicModuleManager] Module package lexflow installed successfully
app.js:14 [CoreDeskApp] Module lexflow installation completed
app.js:702 [CoreDeskApp] *** loadDynamicModulesGrid() CALLED (OPTIMIZED) ***
app.js:703 [CoreDeskApp] *** DIRECT SERVER LOADING - NO DELAYS ***
app.js:14 [CoreDeskApp] Loading modules directly from server for instant display
app.js:14 [CoreDeskApp] Starting module loading process...
app.js:14 [CoreDeskApp] Bypassing dynamic manager delays - loading from server
app.js:804 [CoreDeskApp] *** showServerModules() CALLED ***
app.js:805 [CoreDeskApp] *** STARTING SERVER MODULE LOAD ***
app.js:14 [CoreDeskApp] Loading modules from server (clean architecture)
app.js:1023 [CoreDeskApp] FORCE PRODUCTION: Using production module repository
app.js:14 [CoreDeskApp] Fetching modules from URL: https://coredeskpro.com/api/modules
app.js:822 [CoreDeskApp] Debug: Attempting to fetch modules from: https://coredeskpro.com/api/modules
app.js:823 [CoreDeskApp] Debug: User agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) coredesk/0.0.2 Chrome/126.0.6478.234 Electron/31.7.7 Safari/537.36
app.js:824 [CoreDeskApp] Debug: Location hostname: 
app.js:825 [CoreDeskApp] Debug: *** FORCING PRODUCTION URLS ***
app.js:830 [CoreDeskApp] Debug: Cache-busted URL: https://coredeskpro.com/api/modules?cb=1752276969143
app.js:839 [CoreDeskApp] Debug: Response status: 200
app.js:846 [CoreDeskApp] Debug: API response: {success: true, modules: Array(4)}
app.js:14 [CoreDeskApp] Loaded 4 modules from server
app.js:854 [CoreDeskApp] Debug: Modules loaded: (4) [{…}, {…}, {…}, {…}]
app.js:855 [CoreDeskApp] Debug: *** SUCCESS - MODULES LOADED FROM PRODUCTION ***
app.js:872 [CoreDeskApp] *** CALLING displayServerModules ***
app.js:891 [CoreDeskApp] *** displayServerModules() CALLED ***
app.js:892 [CoreDeskApp] *** MODULES TO DISPLAY: (4) [{…}, {…}, {…}, {…}]
app.js:898 [CoreDeskApp] Module lexflow: downloadable=true, installed=true
app.js:898 [CoreDeskApp] Module protocolx: downloadable=true, installed=false
app.js:898 [CoreDeskApp] Module auditpro: downloadable=true, installed=false
app.js:898 [CoreDeskApp] Module finsync: downloadable=true, installed=false
app.js:962 [CoreDeskApp] *** MODULE GRID UPDATED WITH PRODUCTION DATA ***
app.js:963 [CoreDeskApp] *** THIRD-PARTY BUTTON SHOULD BE VISIBLE ***
app.js:874 [CoreDeskApp] *** displayServerModules COMPLETED ***
statusBar.js:144 UI [StatusBar] Status item clicked: current-module
statusBar.js:169 UI [StatusBar] Opening module selector
titleBar.js:268 UI [TitleBar] Account dropdown forcibly hidden
ExclusiveModuleController.js:296 Component [ExclusiveModuleController] Attempting to switch to module: lexflow
ExclusiveModuleController.js:617 Component [ExclusiveModuleController] Activating module: lexflow
ExclusiveModuleController.js:675 Component [ExclusiveModuleController] Activating dynamic module: lexflow
GlobalLogger.js:30 [DynamicModuleManager] Loading module: lexflow
VM70:44 Module [LexFlow] Initializing...
VM70:587 Module [LexFlow] Loading data...
VM70:609 Module [LexFlow] Database not available, using mock data
loadData @ VM70:609
initialize @ VM70:46
LexFlowModule @ VM70:36
loadModule @ DynamicModuleManager.js:391
activateDynamicModule @ ExclusiveModuleController.js:685
activateModule @ ExclusiveModuleController.js:621
switchToModule @ ExclusiveModuleController.js:351
(anonymous) @ statusBar.js:219
VM70:44 Module [LexFlow] Initializing...
VM70:587 Module [LexFlow] Loading data...
VM70:609 Module [LexFlow] Database not available, using mock data
loadData @ VM70:609
initialize @ VM70:46
loadModule @ DynamicModuleManager.js:395
activateDynamicModule @ ExclusiveModuleController.js:685
activateModule @ ExclusiveModuleController.js:621
switchToModule @ ExclusiveModuleController.js:351
(anonymous) @ statusBar.js:219
2VM70:52 Module [LexFlow] Initialized successfully
VM70:65 Module [LexFlow] Starting activation...
VM70:72 Module [LexFlow] Activating...
VM70:84 Module [LexFlow] Container made visible
VM70:587 Module [LexFlow] Loading data...
VM70:609 Module [LexFlow] Database not available, using mock data
loadData @ VM70:609
refreshData @ VM70:785
activate @ VM70:91
loadModule @ DynamicModuleManager.js:400
await in loadModule (async)
activateDynamicModule @ ExclusiveModuleController.js:685
activateModule @ ExclusiveModuleController.js:621
switchToModule @ ExclusiveModuleController.js:351
(anonymous) @ statusBar.js:219
activityBar.js:208 UI [ActivityBar] Active module updated: lexflow
VM70:104 Module [LexFlow] Activated successfully
GlobalLogger.js:30 [DynamicModuleManager] Styles applied after module activation for lexflow
GlobalLogger.js:30 [DynamicModuleManager] Module lexflow loaded successfully
VM70:65 Module [LexFlow] Starting activation...
VM70:72 Module [LexFlow] Activating...
VM70:84 Module [LexFlow] Container made visible
VM70:587 Module [LexFlow] Loading data...
VM70:609 Module [LexFlow] Database not available, using mock data
loadData @ VM70:609
refreshData @ VM70:785
activate @ VM70:91
activateDynamicModule @ ExclusiveModuleController.js:699
await in activateDynamicModule (async)
activateModule @ ExclusiveModuleController.js:621
switchToModule @ ExclusiveModuleController.js:351
(anonymous) @ statusBar.js:219
activityBar.js:208 UI [ActivityBar] Active module updated: lexflow
VM70:104 Module [LexFlow] Activated successfully
ExclusiveModuleController.js:623 Component [ExclusiveModuleController] Module lexflow activated successfully
ExclusiveModuleController.js:629 Component [ExclusiveModuleController] Welcome screen hidden
panelManager.js:133 UI [PanelManager] Received module:switched event: {from: null, to: 'lexflow', timestamp: '2025-07-11T23:36:11.006Z'}
activityBar.js:208 UI [ActivityBar] Active module updated: lexflow
panelManager.js:133 UI [PanelManager] Received module:switched event: {from: null, to: 'lexflow', timestamp: '2025-07-11T23:36:11.007Z'}
ExclusiveModuleController.js:386 Component [ExclusiveModuleController] Successfully switched to module: lexflow
Logger.js:170 [5:36:11 PM] [INFO] [Module] Module switched {from: null, to: 'lexflow'}
titleBar.js:268 UI [TitleBar] Account dropdown forcibly hidden
panelManager.js:978 UI [PanelManager] Module state change detected: null -> lexflow
GlobalLogger.js:30 [Memory] [MemoryManager] Memory stats: 9 active, 9 peak