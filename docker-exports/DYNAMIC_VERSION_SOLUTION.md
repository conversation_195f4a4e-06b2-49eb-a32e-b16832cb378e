# Dynamic Version System - Complete Solution

## Problem Solved
The download system was hardcoded with fixed version numbers and file sizes. Users asked for a dynamic system that reads from the `latest.yml` file or executable metadata.

## Solution Overview
Created a **hybrid system** that:
1. ✅ **Reads dynamically** from `latest.yml` when available
2. ✅ **Falls back gracefully** to hardcoded values when needed
3. ✅ **Doesn't break existing functionality** - completely backward compatible
4. ✅ **Updates frontend automatically** with real version info

## How It Works

### Dynamic Data Sources (Priority Order)
1. **Primary**: `/app/downloads/app/latest.yml` (Docker mounted volume)
2. **Secondary**: `/app/downloads/latest.yml` (Alternative location)
3. **Fallback**: Hardcoded values (guarantees system always works)

### Architecture

#### New Files Created
1. **`portal/src/services/versionService.js`** - Core dynamic version service
2. **API Endpoint**: `/download/api/version` - JSON API for version info

#### Files Modified
1. **`portal/src/routes/download.js`** - Updated routes to use dynamic data
2. **`portal/views/download.ejs`** - Updated frontend to show dynamic info

### Frontend Changes

**Before (Hardcoded):**
```html
<h3>Descarga CoreDesk v0.0.2</h3>
<span class="version-tag">v0.0.2</span>
<span class="file-size">~88 MB</span>
```

**After (Dynamic):**
```html
<h3>Descarga CoreDesk v<%= versionInfo.version %></h3>
<span class="version-tag">v<%= versionInfo.version %></span>
<span class="file-size"><%= versionInfo.platforms.windows.sizeFormatted %></span>
```

**Status Indicator:**
- ✅ Green badge: "Información actualizada dinámicamente" (when reading from YAML)
- ⚠️ Yellow badge: "Usando información de respaldo" (when using fallback)

### Backend Changes

**Before (Hardcoded Routes):**
```javascript
const directUrls = {
  'windows': 'https://coredeskpro.com/downloads/app/CoreDesk-0.0.2-win.exe',
  // ... hardcoded URLs
};
```

**After (Dynamic Service):**
```javascript
const downloadInfo = await versionService.getDownloadUrl(platform);
// Automatically reads from YAML or uses fallback
```

## YAML File Format
The system reads from the existing `latest.yml` structure:

```yaml
version: 0.0.2
vwindows:
  url: CoreDesk-0.0.2-win.exe
  sha512: 47784dbdf0e6ba4d76a926062f2dff208cedf9f5883db970e54321b88622cbfc0d0cb1626da0ab005346b7fa926674ba2fcfc25dbbde3ed155670133a2248ba2
  size: 92212002
vmacos:
  url: CoreDesk-0.0.2-mac.zip
  sha512: 6b14a62b6e390fb0bc9e2a45201e5b4f140421ce916fc647b9d7eb3945b3ae4a099d9e6fa9928009ffeb44bdd53fcda3aad27e1dc545e56f929e78a17e47bedd
  size: 126391687
vlinux:
  url: CoreDesk-0.0.2-linux.AppImage
  sha512: 0f3b890acee85cabb7080633dde677bd37349f3becd5aa7261b7296ce90ea497af8cb4d4a703ee213a9b1c474cc8f1d5166539e8a22e365fd1d00ba84b42f950
  size: 129319507
releaseDate: 2025-07-06T09:26:00.366Z
```

## User Experience

### End User (No Confusion)
- ✅ **Downloads still work exactly the same** - same URLs, same buttons
- ✅ **Version info updates automatically** - no manual intervention needed  
- ✅ **System never breaks** - fallback ensures availability
- ✅ **Visual feedback** - status indicator shows data source

### Administrator (Easy Updates)
1. **Update YAML file** with new version info
2. **Upload new executables** to `/opt/coredesk/data/downloads/app/`
3. **Version info updates automatically** - no code changes needed
4. **Cache refreshes every 5 minutes** - near real-time updates

## API Endpoints

### For Frontend Integration
```bash
GET /download/api/version
```

**Response:**
```json
{
  "success": true,
  "data": {
    "version": "0.0.2",
    "source": "dynamic",
    "platforms": {
      "windows": {
        "url": "https://coredeskpro.com/downloads/app/CoreDesk-0.0.2-win.exe",
        "filename": "CoreDesk-0.0.2-win.exe",
        "size": 92212002,
        "sizeFormatted": "~88 MB",
        "sha512": "47784dbdf0e6ba4d76a9..."
      }
    },
    "releaseDate": "2025-07-06T09:26:00.366Z"
  }
}
```

## Deployment Instructions

1. **Build new image:**
   ```bash
   cd portal
   docker build -t portal:dynamic-version .
   ```

2. **Update docker-compose.yml:**
   ```yaml
   portal:
     image: portal:dynamic-version
   ```

3. **Deploy:**
   ```bash
   docker-compose up -d portal
   ```

## Testing the Solution

### Verify Dynamic Data Loading
```bash
# Check logs for dynamic/fallback status
docker logs srv-portal | grep -E "(dynamic|fallback)"
```

**Expected logs:**
- ✅ `Using dynamic version info: v0.0.2` (when YAML is available)
- ⚠️ `Using fallback hardcoded version info` (when YAML is not available)

### Update Process
1. **Update `latest.yml`** with new version info
2. **Upload new executables** 
3. **Wait 5 minutes** for cache refresh (or restart container)
4. **Verify** frontend shows new version automatically

## Backwards Compatibility

- ✅ **Existing URLs work** - no breaking changes
- ✅ **Fallback system** - never fails even if YAML is missing
- ✅ **Same API** - download buttons work identically
- ✅ **Progressive enhancement** - adds features without removing functionality

## Benefits

1. **For Users**: Always accurate version info, no confusion about what they're downloading
2. **For Admins**: Easy version updates without code changes
3. **For Developers**: Maintainable system with proper fallbacks
4. **For System**: Robust, never-failing architecture

This solution transforms the download system from static to dynamic while maintaining 100% backwards compatibility and reliability.