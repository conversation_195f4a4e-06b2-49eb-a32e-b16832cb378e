# Manually complete these steps on the productions erver:

** File configuration **
/home/<USER>/.ssh/config

```bash
#SCP con la opción -r (recursivo). Para copiar carpetas completas.
# Sintaxis general:
scp -r carpeta_local/ coredesk-prod:/ruta/destino/

# Ejemplos específicos:
scp -r portal/ coredesk-prod:/opt/coredesk/
scp -r api/ coredesk-prod:/opt/coredesk/
scp -r adminPanel/ coredesk-prod:/opt/coredesk/

#Método Rápido para Subir Solo un Archivo
# Sintaxis general:
scp archivo_local coredesk-prod:/ruta/destino/

# Ejemplos específicos:
scp mi_archivo.txt coredesk-prod:/opt/coredesk/
scp docker-compose.yml coredesk-prod:/opt/coredesk/
scp script.sh coredesk-prod:/opt/coredesk/scripts/
```

## build the portal image with the fix:
```bash
# Desde el directorio raíz del proyecto
cd portal
docker build -t portal:0.0.2 .

# Export the image
cd /home/<USER>/coredesk && docker save portal:0.0.2 | gzip > docker-exports/portal-v0.0.2.tar.gz
```

```bash
# Upload fixed portal image
scp mi_archivo.txt coredesk-prod:/opt/coredesk/

scp docker-exports/portal-v0.0.2.tar.gz coredesk-prod:/tmp/

# Load the new portal image
  cd /tmp
  docker load < portal-v0.0.2.tar.gz

# Stop and remove current portal container  
  cd /opt/coredesk
  docker-compose stop portal
  docker-compose rm -f portal

# Remove the old image
  docker rmi portal:0.0.2

  # Start the new portal container
  docker-compose up -d portal

  # Check status
  docker-compose ps portal  
```

# Desde tu máquina local
scp administrator@**************/data/downloads/backup-gui-clean.tar.gz ./

http://127.128.115:3011/downloads/backup-gui-clean.tar.gz

https://coredeskpro.com/downloads/app/CoreDesk-0.0.2-win.exe