# Configuración adicional para producción
x-logging: &default-logging
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"

# Configuración adicional para producción

services:
  # Servidor API
  api:
    image: coredesk-api:latest
    container_name: srv-api
    restart: unless-stopped
    ports:
      - "3010:3010"
    environment:
      NODE_ENV: production
      MONGO_URI: mongodb://admin:Qazwsx123!@mongo:27017/srv_licenses?authSource=admin
      JWT_SECRET: ${JWT_SECRET:-CoreDesk2025SecretKey!}
      API_PORT: 3010
      PORT: 3010
      LOG_LEVEL: info
      CORS_ORIGIN: https://admin.coredeskpro.com,https://coredeskpro.com,https://api.coredeskpro.com,https://db.coredeskpro.com
      ALLOWED_ORIGINS: https://admin.coredeskpro.com,https://coredeskpro.com,https://api.coredeskpro.com,https://db.coredeskpro.com
      ADMIN_EMAIL: <EMAIL>
      ADMIN_PASSWORD: Qazwsx123!
      SMTP_HOST: smtp.titan.email
      SMTP_PORT: 465
      SMTP_SECURE: true
      SMTP_USER: <EMAIL>
      SMTP_PASS: Qazwsx123!
      FROM_EMAIL: <EMAIL>
    volumes:
      - /opt/coredesk/logs/api:/app/logs
      - /opt/coredesk/data/api:/app/data
      - /opt/coredesk/backups:/app/backups
    depends_on:
      mongo:
        condition: service_healthy
    networks:
      - srv-network
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3010/v1/health', (res) => process.exit(res.statusCode === 200 ? 0 : 1))"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
    logging: *default-logging

  # Base de datos MongoDB
  mongo:
    image: mongo:8.0
    container_name: srv-mongo
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_USER:-admin}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_PASSWORD:-Qazwsx123!}
      MONGO_INITDB_DATABASE: srv_licenses
    volumes:
      - /opt/coredesk/data/mongodb:/data/db
      - /opt/coredesk/backups:/backup
    networks:
      - srv-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.runCommand({ping: 1})", "--quiet"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
    logging: *default-logging

  # Herramienta de administración para MongoDB
  mongo-express:
    image: mongo-express:latest
    container_name: srv-mongo-express
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: ${MONGO_USER:-admin}
      ME_CONFIG_MONGODB_ADMINPASSWORD: ${MONGO_PASSWORD:-Qazwsx123!}
      ME_CONFIG_MONGODB_SERVER: mongo
      ME_CONFIG_BASICAUTH_USERNAME: ${ME_USER:-admin}
      ME_CONFIG_BASICAUTH_PASSWORD: ${ME_PASSWORD:-Qazwsx123!}
      ME_CONFIG_SITE_BASEURL: "/mongo-express/"
      ME_CONFIG_OPTIONS_EDITORTHEME: "ambiance"
      ME_CONFIG_REQUEST_SIZE: "100kb"
      ME_CONFIG_MONGODB_PORT: 27017
    depends_on:
      mongo:
        condition: service_healthy
    networks:
      - srv-network
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M
    logging: *default-logging

  # Portal Web CoreDesk
  portal:
    image: portal:0.0.2
    container_name: srv-portal
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      NODE_ENV: production
      PORT: 3000
      HOST: "0.0.0.0"
      PORTAL_URL: ${PORTAL_URL:-https://coredeskpro.com}
      API_URL: http://api:3010/v1
      APP_NAME: "CoreDesk Portal"
      APP_VERSION: "0.0.2"
      SESSION_SECRET: ${SESSION_SECRET:-CoreDeskPortalSecret2025!}
      DOWNLOAD_PATH: /app/downloads
      API_BASE_URL: http://api:3010/v1
      CONTACT_EMAIL: <EMAIL>
      SUPPORT_EMAIL: <EMAIL>
      DOCUMENTATION_URL: https://coredeskpro.com/docs
    volumes:
      - /opt/coredesk/logs/portal:/app/logs
      - /opt/coredesk/data/uploads:/app/uploads
      - /opt/coredesk/data/downloads:/app/downloads
      - /opt/coredesk/data/portal:/app/data
    depends_on:
      api:
        condition: service_healthy
    networks:
      - srv-network
    healthcheck:
      test: ["CMD", "node", "healthcheck.js"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
    logging: *default-logging

  # Panel de Administración React
  admin-panel:
    image: coredesk-admin-panel:latest
    container_name: admin-panel
    restart: unless-stopped
    ports:
      - "3011:3011"
    environment:
      NODE_ENV: production
      NGINX_PORT: 3011
    volumes:
      - /opt/coredesk/logs/admin-panel:/app/logs
    depends_on:
      api:
        condition: service_healthy
    networks:
      - srv-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3011/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M
    logging: *default-logging

  # System Manager - Backup GUI
  system-manager:
    image: backup-gui-coredesk-backup-gui:latest
    container_name: srv-system-manager
    restart: unless-stopped
    ports:
      - "3012:3012"
    environment:
      FLASK_ENV: production
      PYTHONUNBUFFERED: 1
    volumes:
      - /opt/coredesk/backups:/opt/coredesk/backups:ro
      - /opt/coredesk/config:/opt/coredesk/config:ro
      - /opt/coredesk/backup-script.sh:/opt/coredesk/backup-script.sh:ro
      - /opt/coredesk/logs:/var/log:ro
      - /var/run/docker.sock:/var/run/docker.sock
    networks:
      - srv-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3012/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M
    logging: *default-logging

networks:
  srv-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  coredesk-mongo-data:
    driver: local