# CoreDesk Data Structure
# =====Production Server===== 
# Credenciales: ************** administrator:Qaz@123a

/opt/coredesk/
├── data/                   # Datos persistentes de aplicaciones
│   ├── mongodb/            # Base de datos MongoDB (volumen Docker)
│   ├── api/                # Datos y archivos de la API
│   ├── portal/             # Datos del portal web
│   ├── uploads/            # Archivos subidos por usuarios
│   └── downloads/          # Estructura de descargas para clientes
│       ├── modules/        # Módulos de CoreDesk (.zip)
│       │   ├── lexflow/    # Releases del módulo LexFlow
│       │   ├── protocolx/  # Releases del módulo ProtocolX
│       │   ├── auditpro/   # Releases del módulo AuditPro
│       │   └── finsync/    # Releases del módulo FinSync
│       └── app/            # Aplicación principal CoreDesk
│           ├── latest.yml  # Metadata de electron-updater
│           ├── CoreDesk-0.0.2-mac.zip
│           ├── CoreDesk-0.0.2-win.exe
│           └── CoreDesk-0.0.2-linux.AppImage
├── logs/                   # Logs de todos los servicios
│   ├── api/                # Logs de la API
│   ├── portal/             # Logs del portal web
│   ├── admin-panel/        # Logs del panel de administración
│   ├── nginx/              # Logs del proxy Nginx
│   └── mongodb/            # Logs de MongoDB
├── config/                 # Configuraciones
│   ├── nginx/              # Configuración de Nginx
│   └── ssl/                # Certificados SSL
└── backups/                # Backups automatizados
    ├── mongodb/            # Backups de base de datos
    ├── full/               # Backups completos del sistema
    └── daily/              # Backups diarios incrementales


 The following production server domains and ports are tunneled by Cloudflare:

coredeskpro.com → http://localhost:3000
admin.coredeskpro.com → http://localhost:3011
api.coredeskpro.com → http://localhost:3010
db.coredeskpro.com → http://localhost:8081
sm.coredeskpro.com → http://localhost:3012

### Production Server Access

- **SSH Connection**: There is an established SSH connection to the production server
- **SSH Host**: `coredesk-prod` (configured in `~/.ssh/config`)
- **Server Details**: 
  - Hostname: `**************`
  - User: `administrator`
  - Key: `~/.ssh/coredesk_rsa`
- **Connection Command**: `ssh coredesk-prod`
- **Deployment**: Files can be deployed directly to the production server via SSH
- **Module Distribution**: The portal at `https://coredeskpro.com` serves as the primary distribution point for CoreDesk modules